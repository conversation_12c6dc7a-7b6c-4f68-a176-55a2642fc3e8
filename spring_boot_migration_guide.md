# Migration to Spring Boot 3.5

## Overview

Spring Boot 2.7.x reached End of Life in November 2023. This guide provides step-by-step instructions for migrating services to Spring Boot 3.5.

## Prerequisites

1. **joda-time** library must be migrated to **java.time**
2. **Gson** object mapper must be migrated to **Jackson** object mapper

## Migration Steps

1. Update dependencies
2. Fix code changes
3. Update tests
4. Validate migration

## Dependencies

### Java Version Upgrade

**Java 17 → 21**

All Java versions in the project must be replaced with Java 21, including build tools and the project itself.

```xml
<properties>
    <java.version>21</java.version>
</properties>
```

**Important:** The `<maven.compiler.target>21</maven.compiler.target>` could be missing. Sometimes it is referenced in the kotlin-maven-plugin and needs to be defined.

### Spring Cloud Version

**Spring Boot 3.5.0 requires Spring Cloud 2025.0.0**

```xml
<spring-cloud.version>2025.0.0</spring-cloud.version>
```

### Closelink Dependencies

Update to these versions:

| Library                   | Version  |
|---------------------------|----------|
| net.closelink.security    | v2.1.1   |
| net.closelink.tracing     | v2.1.1   |
| net.closelink.database    | v1.2.2   |
| net.closelink.testutils   | v3.1.2   |
| net.closelink.restClients | v2.1.2   |
| net.closelink.common      | v5.2.2   |
| net.closelink.cenq        | v2.2.317 |

### External Dependencies

#### Spring Doc
Update from 1.x to 2.8.x:

```xml
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    <version>2.8.8</version>
</dependency>
```

#### Spring Retry
Update from 1.4 to 2.x (version managed by Spring Boot):

```xml
<dependency>
    <groupId>org.springframework.retry</groupId>
    <artifactId>spring-retry</artifactId>
</dependency>
```

#### Sentry
Sentry usually takes care of its BOM, if it isn't present, use the latest version:

```xml
<dependency>
    <groupId>io.sentry</groupId>
    <artifactId>sentry-logback</artifactId>
</dependency>
<dependency>
    <groupId>io.sentry</groupId>
    <artifactId>sentry-spring-boot-starter-jakarta</artifactId>
</dependency>
```

#### JUnit4 Support

```xml
<dependency>
    <groupId>org.junit.vintage</groupId>
    <artifactId>junit-vintage-engine</artifactId>
    <scope>test</scope>
</dependency>
```

#### Embedded MongoDB

```xml
<dependency>
    <groupId>de.flapdoodle.embed</groupId>
    <artifactId>de.flapdoodle.embed.mongo.spring3x</artifactId>
    <version>4.18.0</version>
    <scope>test</scope>
</dependency>
```

Add to `src/test/resources/application.properties`:

```properties
# ❌ Old approach
spring.mongodb.embedded.version: 4.4.0
# ✅ Modern approach
de.flapdoodle.mongodb.embedded.version: 7.0.14
```

#### OkHttp3

```xml
<dependency>
    <groupId>com.squareup.okhttp3</groupId>
    <artifactId>okhttp</artifactId>
    <version>4.12.0</version>
</dependency>
```

#### Guava
Update to latest version:

```xml
<dependency>
    <groupId>com.google.guava</groupId>
    <artifactId>guava</artifactId>
    <version>33.4.8-jre</version>
</dependency>
```

#### PODAM (for tests)

```xml
<dependency>
    <groupId>uk.co.jemos.podam</groupId>
    <artifactId>podam</artifactId>
    <version>8.0.1.RELEASE</version>
    <scope>test</scope>
</dependency>
```

#### Joda Money (Runtime Dependency)

```xml
<!-- Joda Money needed at runtime for MongoDB custom conversions -->
<dependency>
    <groupId>org.joda</groupId>
    <artifactId>joda-money</artifactId>
    <version>1.0.4</version>
</dependency>
```

### Update Docker and GitHub Workflows

Update GitHub workflows (`.github/workflows/*.yml`):

```yaml
- name: Setup JDK 21
  uses: actions/setup-java@v4
  with:
    java-version: '21'
    distribution: 'temurin'
    cache: maven
```

Update Docker base image:

```xml
<plugin>
    <groupId>com.google.cloud.tools</groupId>
    <artifactId>jib-maven-plugin</artifactId>
    <configuration>
        <from>
            <image>eclipse-temurin:21-jre-alpine</image>
        </from>
    </configuration>
</plugin>
```

### Logback Elasticsearch Appender

The original appender is no longer maintained. Use the maintained fork:

```xml
<dependency>
    <groupId>com.agido</groupId>
    <artifactId>logback-elasticsearch-appender</artifactId>
    <version>3.0.11</version>
</dependency>
```

Update your logback configuration:

```xml
<!-- Before -->
<appender name="ELASTIC" class="com.internetitem.logback.elasticsearch.ElasticsearchAppender">
    <properties>
        <property>
            <name>host</name>
            <value>${HOSTNAME}</value>
        </property>
    </properties>
</appender>

<!-- After -->
<appender name="ELASTIC" class="com.agido.logback.elasticsearch.ElasticsearchAppender">
    <operation>index</operation>
    <properties>
        <esProperty>
            <name>host</name>
            <value>${HOSTNAME}</value>
        </esProperty>
    </properties>
</appender>
```

## Code Changes

### 1. javax.* → jakarta.*

Replace all `javax.*` imports with `jakarta.*`:

```java
// Before
import javax.servlet.http.HttpServletRequest;

// After
import jakarta.servlet.http.HttpServletRequest;
```

### 2. Security Configuration

Update security DSL from fluent API to lambda:

```kotlin
// Before
@Bean
fun filterChain(http: HttpSecurity): SecurityFilterChain =
    http.authorizeRequests()
        .antMatchers("/actuator/**").permitAll()
        .antMatchers("/**").denyAll()
        .and().csrf().disable().build()

// After
@Bean
fun filterChain(http: HttpSecurity): SecurityFilterChain =
    http.authorizeHttpRequests {
        it.requestMatchers("/actuator/**").permitAll()
          .requestMatchers("/**").denyAll()
    }
    .csrf { it.disable() }
    .build()
```

### 3. MongoDB Auditing

When the joda.time migration is done, you might run into issues with MongoDB auditing since it doesn't support `OffsetDateTime`. If `@EnableMongoAuditing` is already existing, move it into its own config:

```java
@Configuration
@EnableMongoAuditing(dateTimeProviderRef = "auditingDateTimeProvider")
public class AuditConfig {

    @Bean // Makes OffsetDateTime compatible with auditing fields
    public DateTimeProvider auditingDateTimeProvider() {
        return () -> Optional.of(OffsetDateTime.now());
    }
}
```

**Note:** There could be issues when using `CoroutineCrudRepository`. If you see that the auditing fields are not set, try using the `MongoRepository` instead.

### 4. Application Properties

Update deprecated properties:

```properties
# Before
server.tomcat.max-http-post-size=10MB
spring.http.encoding.charset=UTF-8
server.max-http-header-size=128KB
spring.redis.host=localhost
spring.redis.port=6379
management.endpoint.health.enabled=true

# After
server.tomcat.max-http-form-post-size=10MB
server.servlet.encoding.charset=UTF-8
server.max-http-request-header-size=128KB
spring.data.redis.host=localhost
spring.data.redis.port=6379
management.endpoint.health.access: unrestricted
```

### 5. Remove @ConstructorBinding

Remove `@ConstructorBinding` from data classes:

```kotlin
// Before
@ConfigurationProperties("app")
@ConstructorBinding
data class AppProperties(val name: String)

// After
@ConfigurationProperties("app")
data class AppProperties(val name: String)
```

### 6. DefaultObject Pattern

Usually, you want to remove the `DefaultObject` pattern as you want to add the entity fields `id`, `dateCreated`, `dateUpdated` and `deleted` as own fields to the entity.

If your service uses the `DefaultObject` pattern for other usage than just inheritance (e.g., caching with the `dateCreated` field), you might want to keep it.

### 7. Mockito.initMocks Deprecation

**Problem:** Warning: 'fun initMocks(testClass: Any!): Unit' is deprecated.

**Solution:** Replace with `@ExtendWith(MockitoExtension::class)` annotation:

```kotlin
// ❌ Deprecated approach
@BeforeEach
fun setUp() {
    MockitoAnnotations.initMocks(this)
    service = MyService(mockDependency)
}

// ✅ Modern approach
@ExtendWith(MockitoExtension::class)
class MyServiceTest {

    @Mock
    private lateinit var mockDependency: MyDependency

    private lateinit var service: MyService

    @BeforeEach
    fun setUp() {
        // No manual mock initialization needed
        service = MyService(mockDependency)
    }
}
```

### 8. Replace Deprecated ResultMatcher.matchAll()

**Problem:** `ResultMatcher.matchAll()` is deprecated.

**Solution:** Use `ResultActions.andExpectAll()` instead:

```kotlin
// ❌ Deprecated approach
import org.springframework.test.web.servlet.ResultMatcher.matchAll

fun checkList(items: List<Item>): ResultMatcher {
    return matchAll(
        jsonPath("$.items").value(hasSize<Any>(items.size)),
        jsonPath("$.items[*].id").value(containsInAnyOrder(*createIdArray(items)))
    )
}

// Usage
mockMvc.perform(get("/api/items"))
    .andExpect(status().isOk)
    .andExpect(ApprovalResultMatcher.checkList(items))

// ✅ Modern approach
fun checkList(items: List<Item>): Array<ResultMatcher> {
    return arrayOf(
        jsonPath("$.items").value(hasSize<Any>(items.size)),
        jsonPath("$.items[*].id").value(containsInAnyOrder(*createIdArray(items)))
    )
}

// Usage
mockMvc.perform(get("/api/items"))
    .andExpect(status().isOk)
    .andExpectAll(*ApprovalResultMatcher.checkList(items))
```

**Migration Steps:**

1. Update matcher methods to return `Array<ResultMatcher>`:
```kotlin
// Before
fun checkList(items: List<Item>): ResultMatcher {
    return matchAll(...)
}

// After
fun checkList(items: List<Item>): Array<ResultMatcher> {
    return arrayOf(...)
}
```

2. Update test usage to use `andExpectAll()`:
```kotlin
// Before
.andExpect(MyMatcher.checkList(items))

// After
.andExpectAll(*MyMatcher.checkList(items))
```

3. Remove deprecated import:
```kotlin
// Remove
import org.springframework.test.web.servlet.ResultMatcher.matchAll
```

## Joda Time Migration

### Remove all Joda Time dependencies from main code

- Use `OffsetDateTime` instead of `LocalDateTime` for timezone support
- Starting with the domain objects and message objects and the mapper classes is a good starting point
- After that, the remaining usages can be fixed individually
- Replace the `DateMapper` with a call that handles nullable values which returns `OffsetDateTime` objects and `String` values for message objects
- In Java, this is done with the `Optional.ofNullable` approach

## GSON to Jackson Migration

Spring Boot supports Jackson out of the box. Since we need to serialize objects like `OffsetDateTime`, we should simply use Jackson and its dependencies to serialize JSON data.

**Important:** We have to keep the retrofit-gson package for the RestClientFactory we are using.

## Testing

### 1. Test Data Setup

**Critical:** PODAM-generated random dates can break tests that rely on `dateCreated` or `dateUpdated`. You might want to add the date fields manually when creating the object that will be tested.

```java
// Fix random test failures
@Test
public void testCacheValidation() {
    final Entity entity = DataCreator.createEntity();

    // Set controlled timestamps instead of random PODAM values
    entity.setDateCreated(OffsetDateTime.now().minusMinutes(30));
    entity.setDateUpdated(OffsetDateTime.now().minusMinutes(10));

    // Test continues...
}
```

### 2. Fix Runtime Dependencies

**Critical:** Some dependencies may be incorrectly scoped as `test` but are needed at runtime:

```xml
<!-- Jackson needed at runtime, not just tests -->
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
</dependency>
<dependency>
    <groupId>com.fasterxml.jackson.datatype</groupId>
    <artifactId>jackson-datatype-jsr310</artifactId>
</dependency>

<!-- Joda Money needed at runtime for MongoDB custom conversions -->
<dependency>
    <groupId>org.joda</groupId>
    <artifactId>joda-money</artifactId>
    <version>1.0.4</version>
</dependency>
```

**Common Error:** If you see `ClassNotFoundException` for Jackson or `Type org.joda.money.BigMoney not present`, remove `<scope>test</scope>` from these dependencies.

## Common Issues

### Tests fail due to mockito validation errors
- **Fix:** Set explicit `dateCreated` / `dateUpdated` values in test data

### ClassNotFoundException: com.fasterxml.jackson.databind.exc.InvalidDefinitionException
- **Cause:** Jackson dependencies are test-scoped but needed at runtime
- **Fix:** Remove `<scope>test</scope>` from Jackson dependencies

### Type org.joda.money.BigMoney not present
- **Cause:** Joda Money is test-scoped but needed for MongoDB custom conversions
- **Fix:** Remove `<scope>test</scope>` from Joda Money dependency

### MongoDB test failures
- **Fix:** Update to `de.flapdoodle.embed.mongo.spring3x` and add test configuration

### Dependency conflicts
- **Fix:** Use `mvn dependency:tree` to identify conflicting versions
- Remove BOMs in the `<dependencyManagement>` as they are higher prioritized than dependencies inherited by other `<dependency>` declarations and use the versions explicitly. Springdoc is a great example here which might break the build.

### Mongo tests: Cannot convert unsupported date type java.time.LocalDateTime to org.joda.time.DateTime
- **Cause:** Spring Data no longer supports Joda Time
- **Fix:** Use `OffsetDateTime` with proper MongoDB converters, or `LocalDateTime` with UTC zone for annotated fields

### Application fails to start with bean creation errors
- **Cause:** Incompatible dependency versions or configuration changes
- **Fix:** Check Spring Boot 3 compatibility matrix and update all related dependencies

### Fix stricter security defaults for polymorphic deserialization

If there are multiple responses possible for an API (e.g. OnePort) we let jackson deserialize into the right data class automatically. There are stricter rules about this since Jackson 2.15.

Therefore we need to add the `JsonSubTypes` annotation to the superclass:

```kotlin
@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
@JsonSubTypes(
    JsonSubTypes.Type(CreatePriceInquirySuccessResponseMessage::class),
    JsonSubTypes.Type(CreatePriceInquiryErrorResponseMessage::class),
)
sealed interface CreatePriceInquiryResponseMessage
```

## Kotest Optimization

Disable autoscan to speed up tests (you might have to add Kotlin into the service as well, will be default in Kotest 6.0):

Add to `src/test/java/config/KotestConfig.kt`:

```kotlin
object KotestConfig : AbstractProjectConfig() {
    override fun extensions() = listOf(SpringAutowireConstructorExtension, SpringExtension)
}
```

Add to `src/test/resources/kotest.properties`:

```properties
kotest.framework.classpath.scanning.autoscan.disable=true
```

## Optional Changes for After Migration

Some things are not necessary, but should be done after a service is migrated.

### JUnit 4 → JUnit 5 Migration

If using JUnit 4, also update and remove the junit-vintage dependency:

```kotlin
// Before (JUnit 4)
import org.junit.Before
import org.junit.After

@Before
fun setUp() { }

@After
fun tearDown() { }

// After (JUnit 5)
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.AfterEach

@BeforeEach
fun setUp() { }

@AfterEach
fun tearDown() { }
```

### @MockBean Deprecation

`@MockBean` will be removed in future Spring Boot versions. Replace with `@MockitoBean`:

```java
// Before
@MockBean
private SomeService someService;

// After
@MockitoBean
private SomeService someService;
```

In some services, we are using a `TestConfiguration.java` file which mocks many services. `@MockitoBean` can't be used on Spring `@TestConfiguration` annotation classes, so we have to mock these via a custom annotation. This is usually done for AWS beans or some other external services. These have to be added to the individual JUnit `@SpringBootTest` tests.

```java
package net.closelink.messageservice;

import de.tschumacher.queueservice.sqs.SQSQueue;
import io.ably.lib.realtime.AblyRealtime;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import net.closelink.messageservice.service.websocket.AblyWebsocketService;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@MockitoBean(
    types = {
        SQSQueue.class,
        AblyWebsocketService.class,
        AblyRealtime.class,
    }
)
public @interface TestMockConfiguration {
}
```

## Sample Migration

See these PRs for complete examples:
- VesselService Migration
- VesselService Config

## CHANGELOG

### Spring Boot 3.5.0
- Added Spring Cloud 2025.0.0 compatibility requirement
- Added cache mechanism considerations for DefaultObject pattern
- Added PODAM test data setup guidance
- Added Mockito verification best practices
- Updated all dependency versions

### Spring Boot 3.4.5
- Updated all Closelink dependencies

### Spring Boot 3.4.3
- Add explicit version of okhttp3

### Spring Boot 3.4.2
- Upgrade necessary versions for Sentry and Springdoc
- Add @MockBean removal guidance
- Added details for embedded MongoDB by flapdoodle
- Removed kotest-extensions-spring dependency from testutils package
