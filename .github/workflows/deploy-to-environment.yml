name: Deploy

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      approval-environment:
        required: true
        type: string

env:
  ECS_SERVICE: notificationservice

jobs:
  deploy:
    runs-on: ubuntu-22.04
    environment: ${{ inputs.approval-environment }}
    steps:
      - uses: actions/checkout@v4
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Get image
        id: get-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          VERSION=$(git describe  --match="" --always --dirty)
          echo "image=$ECR_REGISTRY/$ECS_SERVICE:$VERSION" >> $GITHUB_OUTPUT

      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ecs_task_definitions/ecs_task_definition_${{ inputs.environment }}.json
          container-name: ${{ env.ECS_SERVICE }}
          image: ${{ steps.get-image.outputs.image }}
          environment-variables: |
            SERVICE_NAME=${{ env.ECS_SERVICE }}
            ENVIRONMENT=${{ inputs.environment }}
            DOCKER_IMAGE_TAG=${{ steps.get-image.outputs.image }}

      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        env:
          ENVIRONMENT: ${{ inputs.environment }}
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ env.ECS_SERVICE }}
          cluster: closelink-${{ inputs.environment }}
          wait-for-service-stability: true
