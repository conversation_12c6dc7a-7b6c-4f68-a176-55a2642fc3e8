test.notification.subject=test{0}{1}
test.notification.message=test{0}{1}

state.QUOTED.title=Enquiry - Quoted
cancel_reason_not_available=n/a
activitylog.prepayment=prepayment

BARGE=barge
TRUCK=truck

BULK=bulk
DRUMS=drums

HS=max. 3.5 % S

activitylog.na=n/a

message.subject.customer={0} - {1} - New Message from {2}
message.subjectBuyerRef.customer={0} - {3} - {1} - New Message from {2}
message.subject.trade.customer={0} - {1} - New Message from {2}
message.subjectBuyerRef.trade.customer={0} - {3} - {1} - New Message from {2}
message.subject.supplier={0} - {1} - New Message from {2}
message.subjectBuyerRef.supplier={0} - {3} - {1} - New Message from {2}

report.prepaymentdays=days
report.operator.positive=higher than
report.operator.negative=lower than
