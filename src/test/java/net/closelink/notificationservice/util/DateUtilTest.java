package net.closelink.notificationservice.util;

import static net.closelink.notificationservice.util.DateUtilKt.formatDisplayDate;
import static net.closelink.notificationservice.util.DateUtilKt.fromEpochMillisOrNull;
import static net.closelink.notificationservice.util.DateUtilKt.parseOffsetDateTimeOrNull;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import org.junit.Test;

public class DateUtilTest {

    @Test
    public void testParseOffsetDateTimeOrNull_validString() {
        String validDateString = "2023-07-27T10:15:30+01:00";
        OffsetDateTime result = parseOffsetDateTimeOrNull(validDateString);

        assertEquals(OffsetDateTime.parse(validDateString), result);
    }

    @Test
    public void testParseOffsetDateTimeOrNull_nullString() {
        OffsetDateTime result = parseOffsetDateTimeOrNull(null);
        assertNull(result);
    }

    @Test
    public void testParseOffsetDateTimeOrNull_invalidString() {
        String invalidDateString = "invalid-date";
        OffsetDateTime result = parseOffsetDateTimeOrNull(invalidDateString);
        assertNull(result);
    }

    @Test
    public void testFromEpochMillisOrNull_validEpoch() {
        Long epochMillis = 1627380930000L; // 2021-07-27T10:15:30Z
        OffsetDateTime result = fromEpochMillisOrNull(epochMillis);

        OffsetDateTime expected = OffsetDateTime.of(2021, 7, 27, 10, 15, 30, 0, ZoneOffset.UTC);
        assertEquals(expected, result);
    }

    @Test
    public void testFromEpochMillisOrNull_nullEpoch() {
        OffsetDateTime result = fromEpochMillisOrNull(null);
        assertNull(result);
    }

    @Test
    public void testFormatDisplayDate_validDate() {
        OffsetDateTime dateTime = OffsetDateTime.of(2021, 7, 27, 10, 15, 30, 0, ZoneOffset.UTC);
        String result = formatDisplayDate(dateTime);

        assertEquals("Jul 27, 2021", result);
    }

    @Test
    public void testFormatDisplayDate_nullDate() {
        String result = formatDisplayDate(null);
        assertEquals("", result);
    }
}
