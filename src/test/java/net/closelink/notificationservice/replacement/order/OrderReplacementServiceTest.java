package net.closelink.notificationservice.replacement.order;

import static org.junit.Assert.assertEquals;

import java.time.OffsetDateTime;
import java.util.Map;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.replacement.order.domain.ReplacementOrder;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.port.PortApiRestService;
import net.closelink.notificationservice.service.rest.port.domain.PortMessage;
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class OrderReplacementServiceTest {

    private static final String ORDER = "order_";
    private static final String CUSTOMER_NAME = ORDER + "customer_name";
    private static final String SUPPLIER_NAME = ORDER + "supplier_name";
    private static final String PORT_LOCCODE = ORDER + "port_loccode";
    private static final String PORT_COUNTRY = ORDER + "port_country";
    private static final String PORT_COUNTRY_NAME = ORDER + "port_country_name";
    private static final String PORT_NAME = ORDER + "port_name";
    private static final String VESSEL_IMO = ORDER + "vessel_imo";
    private static final String VESSEL_NAME = ORDER + "vessel_name";
    private static final String STATE = ORDER + "state";
    private static final String CANCELREASON = ORDER + "cancelreason";
    private static final String DELIVERYDATE = ORDER + "deliverydate";
    private static final String DATESTARTED = ORDER + "datestarted";
    private static final String NUMBER = ORDER + "number";
    private static final String BUYERREF = ORDER + "buyerref";
    private static final String ID = ORDER + "id";
    private static final String AGENT = ORDER + "agent";

    private OrderReplacementService service;
    private PortApiRestService portApiRestService;
    private VesselApiRestService vesselApiRestService;
    private CustomerApiRestService customerApiRestService;
    private SupplierApiRestService supplierApiRestService;

    @Before
    public void setUp() {
        this.portApiRestService = Mockito.mock(PortApiRestService.class);
        this.vesselApiRestService = Mockito.mock(VesselApiRestService.class);
        this.customerApiRestService = Mockito.mock(CustomerApiRestService.class);
        this.supplierApiRestService = Mockito.mock(SupplierApiRestService.class);
        this.service = new DefaultOrderReplacementService(
            this.portApiRestService,
            this.vesselApiRestService,
            this.customerApiRestService,
            this.supplierApiRestService
        );
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.portApiRestService);
        Mockito.verifyNoMoreInteractions(this.vesselApiRestService);
        Mockito.verifyNoMoreInteractions(this.customerApiRestService);
        Mockito.verifyNoMoreInteractions(this.supplierApiRestService);
    }

    @Test
    public void createTest() {
        final ReplacementOrder order = DataCreator.createReplacementOrder();

        final VesselMessage vessel = DataCreator.createVesselMessage();
        Mockito.when(this.vesselApiRestService.getVessel(order.getVesselId())).thenReturn(vessel);
        final CustomerMessage customer = DataCreator.createCustomerMessage();
        Mockito.when(this.customerApiRestService.getCustomer(order.getCustomerId())).thenReturn(customer);
        final SupplierMessage supplier = DataCreator.createSupplierMessage();
        Mockito.when(this.supplierApiRestService.getSupplier(order.getSupplierId())).thenReturn(supplier);
        final PortMessage port = DataCreator.createPortMessage();
        Mockito.when(this.portApiRestService.getPort(order.getPortId())).thenReturn(port);

        final Map<String, String> result = this.service.create(order);

        assertOrderReplacements(result, order);
        assertPortReplacements(result, port);
        assertVesselReplacements(result, vessel);
        assertCustomerReplacements(result, customer);
        assertSupplierReplacements(result, supplier);

        Mockito.verify(this.vesselApiRestService).getVessel(order.getVesselId());
        Mockito.verify(this.customerApiRestService).getCustomer(order.getCustomerId());
        Mockito.verify(this.supplierApiRestService).getSupplier(order.getSupplierId());
        Mockito.verify(this.portApiRestService).getPort(order.getPortId());
    }

    private void assertSupplierReplacements(final Map<String, String> result, final SupplierMessage supplier) {
        assertEquals(supplier.getName(), result.get(SUPPLIER_NAME));
    }

    private void assertCustomerReplacements(final Map<String, String> result, final CustomerMessage customer) {
        assertEquals(customer.getName(), result.get(CUSTOMER_NAME));
    }

    private void assertVesselReplacements(final Map<String, String> result, final VesselMessage vessel) {
        assertEquals(vessel.getName(), result.get(VESSEL_NAME));
        assertEquals(vessel.getImo(), result.get(VESSEL_IMO));
    }

    private void assertPortReplacements(final Map<String, String> result, final PortMessage port) {
        assertEquals(port.getName(), result.get(PORT_NAME));
        assertEquals(port.getCountry().getCode(), result.get(PORT_COUNTRY));
        assertEquals(port.getCountry().getName(), result.get(PORT_COUNTRY_NAME));
        assertEquals(port.getLocCode(), result.get(PORT_LOCCODE));
    }

    private void assertOrderReplacements(final Map<String, String> result, final ReplacementOrder order) {
        assertEquals(order.getId(), result.get(ID));
        assertEquals(order.getBuyerReference(), result.get(BUYERREF));
        assertEquals(order.getOrderNumber(), result.get(NUMBER));
        assertEquals(order.getDateDelivery().toString(), result.get(DELIVERYDATE));
        assertEquals(order.getDateCreated().toString(), result.get(DATESTARTED));
        assertEquals(order.getCancelReason(), result.get(CANCELREASON));
        assertEquals(order.getAgent(), result.get(AGENT));
        assertEquals(order.getState().toString(), result.get(STATE));
    }
}
