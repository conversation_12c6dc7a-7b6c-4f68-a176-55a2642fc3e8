package net.closelink.notificationservice.replacement.company.supplier;

import static net.closelink.notificationservice.data.DataCreator.createSupplierMessage;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import java.util.Map;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierMessage;
import org.junit.Test;

public class SupplierReplacerTest {

    private static final String SUPPLIER_NAME = "supplier_name";

    @Test
    public void createSupplierReplacements() {
        final SupplierMessage supplier = createSupplierMessage();

        final Map<String, String> replacements = SupplierReplacer.createSupplierReplacements(supplier);

        assertNotNull(replacements);
        assertEquals(replacements.get(SUPPLIER_NAME), supplier.getName());
    }
}
