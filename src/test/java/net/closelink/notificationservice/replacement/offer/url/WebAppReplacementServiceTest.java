package net.closelink.notificationservice.replacement.offer.url;

import static org.junit.Assert.assertEquals;

import java.util.Map;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.properties.WebappProperties;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementOffer;
import org.junit.Before;
import org.junit.Test;

public class WebAppReplacementServiceTest {

    private static final String URL_KEY = "webapp_url";

    private WebAppReplacementService service;
    private WebappProperties properties;

    @Before
    public void setUp() {
        this.properties = DataCreator.createWebappProperties();
        this.service = new WebAppReplacementService(this.properties);
    }

    @Test
    public void createOfferSupplierTest() {
        ReplacementOffer offer = DataCreator.createReplacementOffer();
        offer.setOrderId(null);

        Map<String, String> result = this.service.create(offer, ReceiverType.SUPPLIER);

        assertEquals(this.properties.getOfferUrl() + offer.getId(), result.get(URL_KEY));
    }

    @Test
    public void createOfferSupplierOrderTest() {
        ReplacementOffer offer = DataCreator.createReplacementOffer();
        offer.setOrderId(DataCreator.createId());
        offer.setEnquiryType("ASSIGNED");

        Map<String, String> result = this.service.create(offer, ReceiverType.SUPPLIER);

        assertEquals(this.properties.getOfferUrl() + offer.getId(), result.get(URL_KEY));
    }

    @Test
    public void createOfferCustomerTest() {
        ReplacementOffer offer = DataCreator.createReplacementOffer();
        offer.setState("ENQUIRY");
        offer.setId(DataCreator.createId());
        offer.setEnquiryType("ASSIGNED");

        Map<String, String> result = this.service.create(offer, ReceiverType.CUSTOMER);

        assertEquals(this.properties.getOrderUrl() + offer.getOrderId(), result.get(URL_KEY));
    }

    @Test
    public void createOfferCustomerOrderTest() {
        ReplacementOffer offer = DataCreator.createReplacementOffer();
        offer.setState("ENQUIRY");
        offer.setOrderId(DataCreator.createId());
        offer.setEnquiryType("SPOT");

        Map<String, String> result = this.service.create(offer, ReceiverType.CUSTOMER);

        assertEquals(this.properties.getOrderUrl() + offer.getOrderId(), result.get(URL_KEY));
    }

    @Test
    public void createOfferCustomerOrderOfferStateTest() {
        ReplacementOffer offer = DataCreator.createReplacementOffer();
        offer.setState("ORDER");
        offer.setOrderId(DataCreator.createId());
        offer.setEnquiryType("SPOT");

        Map<String, String> result = this.service.create(offer, ReceiverType.CUSTOMER);

        assertEquals(this.properties.getOfferUrl() + offer.getId(), result.get(URL_KEY));
    }

    @Test
    public void createOrderTest() {
        String orderId = DataCreator.createString();

        Map<String, String> result = this.service.create(orderId);

        assertEquals(this.properties.getOrderUrl() + orderId, result.get(URL_KEY));
    }

    @Test
    public void createDraftOrderTest() {
        String orderId = DataCreator.createString();

        Map<String, String> result = this.service.createDraft(orderId);

        assertEquals(this.properties.getDraftOrderUrl() + orderId, result.get(URL_KEY));
    }
}
