package net.closelink.notificationservice.replacement.offer.cancel;

import static org.junit.Assert.assertEquals;

import net.closelink.notificationservice.data.DataCreator;
import org.junit.Before;
import org.junit.Test;
import org.springframework.context.MessageSource;
import org.springframework.context.support.ResourceBundleMessageSource;

public class OfferReplacementSupportServiceTest {

    private DefaultOfferReplacementSupportService service;
    private MessageSource messageSource;

    @Before
    public void setUp() {
        this.messageSource = createMessageSoruce();
        this.service = new DefaultOfferReplacementSupportService(this.messageSource);
    }

    private MessageSource createMessageSoruce() {
        final ResourceBundleMessageSource source = new ResourceBundleMessageSource();
        source.setBasenames("strings/messages");
        source.setUseCodeAsDefaultMessage(true);
        return source;
    }

    @Test
    public void createCancelReasonReplacementTest() {
        final String cancelReason = DataCreator.createString();

        final String resultCancelReason = this.service.createCancelReasonReplacement(cancelReason);

        assertEquals(cancelReason, resultCancelReason);
    }

    @Test
    public void createCancelReasonReplacementNullTest() {
        final String cancelReason = null;

        final String resultCancelReason = this.service.createCancelReasonReplacement(cancelReason);

        assertEquals("n/a", resultCancelReason);
    }
}
