package net.closelink.notificationservice.replacement.offer;

import static org.junit.Assert.assertEquals;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.data.MessageSourceCreator;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementItem;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementMoney;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementOffer;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementSamplekit;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementSurcharge;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.port.PortApiRestService;
import net.closelink.notificationservice.service.rest.port.domain.PortMessage;
import net.closelink.notificationservice.service.rest.product.ProductApiRestService;
import net.closelink.notificationservice.service.rest.product.domain.ProductMessage;
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierMessage;
import net.closelink.notificationservice.service.rest.user.UserApiRestService;
import net.closelink.notificationservice.service.rest.user.domain.UserMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import net.closelink.utils.Formatter;
import org.joda.money.BigMoney;
import org.joda.money.CurrencyUnit;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.context.MessageSource;

public class OfferReplacementServiceTest {

    private static final String SURCHARGE_NAME = "name";
    private static final String SURCHARGE_VALUE = "value";
    private static final String SAMPLEKIT_NAME = "name";
    private static final String SAMPLEKIT_VALUE = "value";
    private static final String SAMPLEKIT_QUANTITY = "quantity";
    private static final String ITEM_PACK_TYPE = "packType";
    private static final String ITEM_UNIT_SIZE = "unitSize";
    private static final String ITEM_UNITS = "units";
    private static final String ITEM_UNIT = "unit";
    private static final String ITEM_VOLUME = "volume";
    private static final String ITEM_TOTAL = "total";
    private static final String ITEM_PRICE = "price";
    private static final String ITEM_NAME = "name";

    private static final String ORDER = "order_";
    private static final String PPL = ORDER + "ppl";
    private static final String TOTAL = ORDER + "total";
    private static final String VOLUME = ORDER + "volume";
    private static final String CUSTOMER_NAME = ORDER + "customer_name";
    private static final String SUPPLIER_NAME = ORDER + "supplier_name";
    private static final String PORT_LOCCODE = ORDER + "port_loccode";
    private static final String PORT_COUNTRY = ORDER + "port_country";
    private static final String PORT_COUNTRY_NAME = ORDER + "port_country_name";
    private static final String PORT_NAME = ORDER + "port_name";
    private static final String VESSEL_IMO = ORDER + "vessel_imo";
    private static final String VESSEL_NAME = ORDER + "vessel_name";
    private static final String STATE = ORDER + "state";
    private static final String CANCELREASON = ORDER + "cancelreason";
    private static final String DELIVERYDATE = ORDER + "deliverydate";
    private static final String DATESTARTED = ORDER + "datestarted";
    private static final String NUMBER = ORDER + "number";
    private static final String BUYERREF = ORDER + "buyerref";
    private static final String VENDORREF = ORDER + "vendorref";
    private static final String ID = ORDER + "id";
    private static final String ORDER_ID = ORDER + "trade_id";
    private static final String AGENT = ORDER + "agent";
    private static final String STATE_TITLE = ORDER + "state_title";

    private OfferReplacementService service;
    private PortApiRestService portApiRestService;
    private VesselApiRestService vesselApiRestService;
    private CustomerApiRestService customerApiRestService;
    private SupplierApiRestService supplierApiRestService;
    private ProductApiRestService productApiRestService;
    private UserApiRestService userApiRestService;
    private MessageSource messageSource;

    @Before
    public void setUp() {
        this.portApiRestService = Mockito.mock(PortApiRestService.class);
        this.vesselApiRestService = Mockito.mock(VesselApiRestService.class);
        this.customerApiRestService = Mockito.mock(CustomerApiRestService.class);
        this.supplierApiRestService = Mockito.mock(SupplierApiRestService.class);
        this.productApiRestService = Mockito.mock(ProductApiRestService.class);
        this.userApiRestService = Mockito.mock(UserApiRestService.class);
        this.messageSource = MessageSourceCreator.createMessageSource();
        this.service = new DefaultOfferReplacementService(
            this.portApiRestService,
            this.vesselApiRestService,
            this.customerApiRestService,
            this.supplierApiRestService,
            this.productApiRestService,
            this.userApiRestService,
            this.messageSource
        );
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.portApiRestService);
        Mockito.verifyNoMoreInteractions(this.vesselApiRestService);
        Mockito.verifyNoMoreInteractions(this.customerApiRestService);
        Mockito.verifyNoMoreInteractions(this.supplierApiRestService);
        Mockito.verifyNoMoreInteractions(this.productApiRestService);
    }

    @Test
    public void createTest() {
        final ReplacementOffer offer = DataCreator.createReplacementOffer();
        offer.setState("QUOTED");

        final VesselMessage vessel = DataCreator.createVesselMessage();
        Mockito.when(this.vesselApiRestService.getVessel(offer.getVesselId())).thenReturn(vessel);
        final CustomerMessage customer = DataCreator.createCustomerMessage();
        Mockito.when(this.customerApiRestService.getCustomer(offer.getCustomerId())).thenReturn(customer);
        final SupplierMessage supplier = DataCreator.createSupplierMessage();
        Mockito.when(this.supplierApiRestService.getSupplier(offer.getSupplierId())).thenReturn(supplier);
        final PortMessage port = DataCreator.createPortMessage();
        Mockito.when(this.portApiRestService.getPort(offer.getPortId())).thenReturn(port);

        final UserMessage user = DataCreator.createUserMessage();
        Mockito.when(this.userApiRestService.getUser(offer.getUpdatedBy())).thenReturn(user);

        final Map<String, String> result = this.service.create(offer);

        assertOrderReplacements(result, offer);
        assertPortReplacements(result, port);
        assertVesselReplacements(result, vessel);
        assertCustomerReplacements(result, customer);
        assertSupplierReplacements(result, supplier);

        Mockito.verify(this.vesselApiRestService).getVessel(offer.getVesselId());
        Mockito.verify(this.customerApiRestService).getCustomer(offer.getCustomerId());
        Mockito.verify(this.supplierApiRestService).getSupplier(offer.getSupplierId());
        Mockito.verify(this.portApiRestService).getPort(offer.getPortId());
    }

    private void assertSupplierReplacements(final Map<String, String> result, final SupplierMessage supplier) {
        assertEquals(supplier.getName(), result.get(SUPPLIER_NAME));
    }

    private void assertCustomerReplacements(final Map<String, String> result, final CustomerMessage customer) {
        assertEquals(customer.getName(), result.get(CUSTOMER_NAME));
    }

    private void assertVesselReplacements(final Map<String, String> result, final VesselMessage vessel) {
        assertEquals(vessel.getName(), result.get(VESSEL_NAME));
        assertEquals(vessel.getImo(), result.get(VESSEL_IMO));
    }

    private void assertPortReplacements(final Map<String, String> result, final PortMessage port) {
        assertEquals(port.getName(), result.get(PORT_NAME));
        assertEquals(port.getCountry().getCode(), result.get(PORT_COUNTRY));
        assertEquals(port.getCountry().getName(), result.get(PORT_COUNTRY_NAME));
        assertEquals(port.getLocCode(), result.get(PORT_LOCCODE));
    }

    private void assertOrderReplacements(final Map<String, String> result, final ReplacementOffer offer) {
        assertEquals(offer.getId(), result.get(ID));
        assertEquals(offer.getOrderId(), result.get(ORDER_ID));
        assertEquals(offer.getVendorReference(), result.get(VENDORREF));
        assertEquals(offer.getBuyerReference(), result.get(BUYERREF));
        assertEquals(offer.getOfferNumber(), result.get(NUMBER));
        assertEquals(offer.getDateDelivery().toString(), result.get(DELIVERYDATE));
        assertEquals(offer.getDateCreated().toString(), result.get(DATESTARTED));
        assertEquals(offer.getCancelReason(), result.get(CANCELREASON));
        assertEquals(offer.getAgent(), result.get(AGENT));
        assertEquals(offer.getState().toString(), result.get(STATE));
        assertEquals("Enquiry - Quoted", result.get(STATE_TITLE));
        assertEquals(formatPrice(offer.getPpl()), result.get(PPL));
        assertEquals(offer.getVolume().toString(), result.get(VOLUME));
        assertEquals(formatPrice(offer.getTotal()), result.get(TOTAL));
    }

    @Test
    public void createSurchargeListTest() {
        final List<ReplacementSurcharge> surcharges = DataCreator.createReplacementSurcharges();

        final List<Map<String, String>> createSurcharges = this.service.createSurchargeList(surcharges);

        for (int i = 0; i < createSurcharges.size(); i++) {
            assertSurcharge(createSurcharges.get(i), surcharges.get(i));
        }
    }

    private void assertSurcharge(final Map<String, String> result, final ReplacementSurcharge surcharge) {
        assertEquals(surcharge.getName(), result.get(SURCHARGE_NAME));
        assertEquals(formatPrice(surcharge.getValue()), result.get(SURCHARGE_VALUE));
    }

    @Test
    public void createSamplekitListTest() {
        final List<ReplacementSamplekit> samplekits = DataCreator.createReplacementSamplekits();

        final List<Map<String, String>> createSamplekits = this.service.createSamplekitList(samplekits);

        for (int i = 0; i < createSamplekits.size(); i++) {
            assertSamplekit(createSamplekits.get(i), samplekits.get(i));
        }
    }

    private void assertSamplekit(final Map<String, String> result, final ReplacementSamplekit samplekit) {
        assertEquals(samplekit.getName(), result.get(SAMPLEKIT_NAME));
        assertEquals(formatPrice(samplekit.getValue()), result.get(SAMPLEKIT_VALUE));
        assertEquals(samplekit.getQuantity().toString(), result.get(SAMPLEKIT_QUANTITY));
    }

    @Test
    public void createItemsListTest() {
        final List<ReplacementItem> items = DataCreator.createReplacementItems();
        final List<ProductMessage> products = createProducts(items);

        final List<Map<String, String>> createItemsList = this.service.createItemsList(items);

        for (int i = 0; i < createItemsList.size(); i++) {
            final ReplacementItem item = items.get(i);
            assertItem(createItemsList.get(i), item, products.get(i));
            Mockito.verify(this.productApiRestService).getProduct(item.getProductId());
        }
    }

    private List<ProductMessage> createProducts(final List<ReplacementItem> items) {
        final List<ProductMessage> products = new ArrayList<>();
        for (final ReplacementItem item : items) {
            final ProductMessage product = DataCreator.createProductMessage();
            Mockito.when(this.productApiRestService.getProduct(item.getProductId())).thenReturn(product);

            products.add(product);
        }
        return products;
    }

    private void assertItem(
        final Map<String, String> result,
        final ReplacementItem item,
        final ProductMessage product
    ) {
        assertEquals(product.getName(), result.get(ITEM_NAME));
        assertEquals(formatItemPrice(item.getPrice()), result.get(ITEM_PRICE));
        assertEquals(formatPrice(item.getTotal()), result.get(ITEM_TOTAL));
        assertEquals(item.getVolume().toString(), result.get(ITEM_VOLUME));
        assertEquals(item.getUnit(), result.get(ITEM_UNIT));
        assertEquals(item.getUnits().toString(), result.get(ITEM_UNITS));
        assertEquals(item.getUnitSize().toString(), result.get(ITEM_UNIT_SIZE));
        assertEquals(
            this.messageSource.getMessage(item.getPackType(), null, Locale.ENGLISH),
            result.get(ITEM_PACK_TYPE)
        );
    }

    private String formatPrice(final ReplacementMoney value) {
        return Formatter.formatPrice(createMoney(value));
    }

    private String formatItemPrice(final ReplacementMoney value) {
        return Formatter.formatPrice(createMoney(value), 4);
    }

    private static BigMoney createMoney(ReplacementMoney money) {
        if (money == null) return null;
        return BigMoney.of(CurrencyUnit.of(money.getCurrency()), money.getValue());
    }
}
