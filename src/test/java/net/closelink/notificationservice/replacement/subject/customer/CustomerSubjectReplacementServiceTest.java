package net.closelink.notificationservice.replacement.subject.customer;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;

import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementOffer;
import net.closelink.notificationservice.service.rest.order.OrderApiRestService;
import net.closelink.notificationservice.service.rest.order.domain.OrderMessage;
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class CustomerSubjectReplacementServiceTest {

    private CustomerSubjectReplacementService service;

    private VesselApiRestService vesselApiRestService;
    private OrderApiRestService orderService;
    private SupplierApiRestService supplierApiRestService;

    private ReplacementOffer offer;

    private VesselMessage vessel;

    private SupplierMessage supplier;

    private OrderMessage order;

    @Before
    public void setUp() {
        this.vesselApiRestService = mock(VesselApiRestService.class);
        this.orderService = mock(OrderApiRestService.class);
        this.supplierApiRestService = mock(SupplierApiRestService.class);

        this.service = new DefaultCustomerSubjectReplacementService(
            this.vesselApiRestService,
            this.orderService,
            this.supplierApiRestService
        );

        this.offer = DataCreator.createReplacementOffer();
        this.vessel = DataCreator.createVesselMessage();
        this.supplier = DataCreator.createSupplierMessage();
        this.order = DataCreator.createOrderMessage();
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.vesselApiRestService);
        Mockito.verifyNoMoreInteractions(this.orderService);
        Mockito.verifyNoMoreInteractions(this.supplierApiRestService);
    }

    @Test
    public void createSubjectReplacementsTest() {
        whenGetVessel();
        whenGetSupplier();

        final Object[] replacements = this.service.createSubjectReplacements(this.offer);

        assertEquals(this.vessel.getName(), replacements[0]);
        assertEquals(this.offer.getOfferNumber(), replacements[1]);
        assertEquals(this.supplier.getName(), replacements[2]);
        assertEquals(this.offer.getBuyerReference(), replacements[3]);

        verifyGetVessel();
        verifyGetSupplier();
    }

    @Test
    public void createSpotSubjectReplacementsTest() {
        whenGetVessel();
        whenGetSupplier();
        whenGetOrder();

        final Object[] replacements = this.service.createSpotSubjectReplacements(this.offer);

        assertEquals(this.vessel.getName(), replacements[0]);
        assertEquals(this.order.getOrderNumber(), replacements[1]);
        assertEquals(this.supplier.getName(), replacements[2]);
        assertEquals(this.offer.getBuyerReference(), replacements[3]);

        verifyGetVessel();
        verifyGetSupplier();
        verifyGetOrder();
    }

    private void verifyGetOrder() {
        Mockito.verify(this.orderService).getOrder(this.offer.getOrderId());
    }

    private void whenGetOrder() {
        Mockito.when(this.orderService.getOrder(this.offer.getOrderId())).thenReturn(this.order);
    }

    private void verifyGetSupplier() {
        Mockito.verify(this.supplierApiRestService).getSupplier(this.offer.getSupplierId());
    }

    private void verifyGetVessel() {
        Mockito.verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
    }

    private void whenGetSupplier() {
        Mockito.when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
    }

    private void whenGetVessel() {
        Mockito.when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
    }
}
