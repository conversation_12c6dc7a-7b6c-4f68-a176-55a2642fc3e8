package net.closelink.notificationservice.replacement.subject.supplier;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mock;

import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class SupplierSubjectReplacementServiceTest {

    private SupplierSubjectReplacementService service;

    private VesselApiRestService vesselApiRestService;
    private CustomerApiRestService customerApiRestService;

    private Offer offer;
    private VesselMessage vessel;
    private CustomerMessage customer;

    @Before
    public void setUp() {
        this.vesselApiRestService = mock(VesselApiRestService.class);
        this.customerApiRestService = mock(CustomerApiRestService.class);

        this.service = new DefaultSupplierSubjectReplacementService(
            this.vesselApiRestService,
            this.customerApiRestService
        );

        this.offer = DataCreator.createOffer();
        this.vessel = DataCreator.createVesselMessage();
        this.customer = DataCreator.createCustomerMessage();
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.vesselApiRestService);
        Mockito.verifyNoMoreInteractions(this.customerApiRestService);
    }

    @Test
    public void createSubjectReplacementsTest() {
        whenGetVessel();
        whenGetCustomer();

        final Object[] replacements = this.service.createSubjectReplacements(this.offer);

        assertReplacements(replacements);

        verifyGetVessel();
        verifyGetCustomer();
    }

    private void assertReplacements(final Object[] replacements) {
        assertNotNull(replacements);
        assertEquals(this.vessel.getName(), replacements[0]);
        assertEquals(this.offer.getOfferNumber(), replacements[1]);
        assertEquals(this.customer.getName(), replacements[2]);
    }

    private void verifyGetCustomer() {
        Mockito.verify(this.customerApiRestService).getCustomer(this.offer.getCustomerId());
    }

    private void verifyGetVessel() {
        Mockito.verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
    }

    private void whenGetCustomer() {
        Mockito.when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
    }

    private void whenGetVessel() {
        Mockito.when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
    }
}
