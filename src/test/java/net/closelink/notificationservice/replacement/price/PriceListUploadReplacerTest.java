package net.closelink.notificationservice.replacement.price;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import java.util.Map;
import net.closelink.notificationservice.data.DataCreator;
import org.junit.Test;

public class PriceListUploadReplacerTest {

    private static final String UPLOAD = "upload_";
    private static final String CUSTOMERID = UPLOAD + "customerid";
    private static final String SUPPLIERGROUPID = UPLOAD + "supplierGroupId";

    @Test
    public void createReplacements() {
        final String customerId = DataCreator.createId();
        final String supplierGroupId = DataCreator.createId();

        final Map<String, String> replacements = PriceListUploadReplacer.createReplacements(
            customerId,
            supplierGroupId
        );

        assertNotNull(replacements);
        assertEquals(replacements.get(SUPPLIERGROUPID), supplierGroupId);
        assertEquals(replacements.get(CUSTOMERID), customerId);
    }
}
