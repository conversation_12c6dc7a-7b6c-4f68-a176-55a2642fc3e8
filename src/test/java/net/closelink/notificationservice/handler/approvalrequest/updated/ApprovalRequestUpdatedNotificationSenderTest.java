package net.closelink.notificationservice.handler.approvalrequest.updated;

import static org.mockito.Mockito.verify;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import net.closelink.cenqueue.objects.ApprovalRequest;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.approval.updated.ApprovalRequestUpdatedNotificationSender;
import net.closelink.notificationservice.replacement.offer.OfferReplacementService;
import net.closelink.notificationservice.replacement.offer.mapper.OfferMessageReplacementOfferMapper;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.offer.OfferApiRestService;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.MailNotificationSettings;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.assertj.core.util.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ApprovalRequestUpdatedNotificationSenderTest {

    private ApprovalRequestUpdatedNotificationSender approvalRequestUpdatedNotificationSender;

    @Mock
    private WebAppReplacementService webappReplacementService;

    @Mock
    private OfferReplacementService offerReplacementService;

    @Mock
    private VesselApiRestService vesselApiRestService;

    @Mock
    private OfferApiRestService offerApiRestService;

    @Mock
    private NotificationService notificationService;

    @BeforeEach
    public void setUp() {
        approvalRequestUpdatedNotificationSender = new ApprovalRequestUpdatedNotificationSender(
            webappReplacementService,
            offerReplacementService,
            vesselApiRestService,
            offerApiRestService,
            notificationService
        );
    }

    @Test
    public void shouldCreateNotification() {
        String offerId = "123-456";
        String offerNumber = "CL123";
        String controllerEmailAddress = "<EMAIL>";
        String customerId = "987";
        String vesselId = "654";
        String vesselName = "MS Classica";
        String responseMessage = "Antwort";

        ApprovalRequest approvalRequest = ApprovalRequest.builder()
            .offerId(offerId)
            .controllerEmailAddress(controllerEmailAddress)
            .responseMessage(responseMessage)
            .build();

        String templateName = "templateName";
        String subject = "subject";
        String buyerReference = "Test 123";

        var offer = OfferMessage.builder()
            .id(offerId)
            .customerId(customerId)
            .vesselId(vesselId)
            .offerNumber(offerNumber)
            .buyerReference(buyerReference)
            .type("LUBES")
            .build();

        Mockito.when(offerApiRestService.getOffer(offerId)).thenReturn(offer);

        VesselMessage vessel = VesselMessage.builder()
            .name(vesselName)
            .mailNotificationSettings(
                MailNotificationSettings.builder()
                    .orderUpdateSettings(MailNotificationSettings.NotificationSettings.builder().build())
                    .build()
            )
            .build();

        Mockito.when(vesselApiRestService.getVessel(vesselId)).thenReturn(vessel);

        Mockito.when(
            webappReplacementService.create(OfferMessageReplacementOfferMapper.create(offer), ReceiverType.CUSTOMER)
        ).thenReturn(Maps.newHashMap("webapp_url", "https://staging.closelink.net"));

        Mockito.when(offerReplacementService.create(OfferMessageReplacementOfferMapper.create(offer))).thenReturn(
            Maps.newHashMap("offer_port_name", "Hamburg")
        );

        approvalRequestUpdatedNotificationSender.sendNotification(approvalRequest, templateName, subject);

        final Map<String, String> mailReplacements = new HashMap<>();
        mailReplacements.put("webapp_url", "https://staging.closelink.net");
        mailReplacements.put("offer_port_name", "Hamburg");
        mailReplacements.put("approval_request_controller_email_address", controllerEmailAddress);
        mailReplacements.put("approval_request_response_message", responseMessage);

        Notification notification = Notification.builder()
            .template(templateName)
            .subject(subject)
            .sendMail(true)
            .hidden(true)
            .offerId(offerId)
            .receiverId(customerId)
            .receiverType(ReceiverType.CUSTOMER)
            .recipients(List.of())
            .senderType(SenderType.SYSTEM)
            .mailReplacements(mailReplacements)
            .subjectReplacements(new Object[] { vesselName, offerNumber, buyerReference })
            .category(NotificationCategory.OFFER_UPDATE)
            .build();

        verify(notificationService).notify(notification);
    }
}
