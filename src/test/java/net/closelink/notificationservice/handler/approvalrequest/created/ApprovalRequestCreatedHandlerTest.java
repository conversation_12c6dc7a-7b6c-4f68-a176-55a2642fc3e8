package net.closelink.notificationservice.handler.approvalrequest.created;

import static org.mockito.Mockito.verify;

import java.util.Arrays;
import java.util.Collections;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestCreatedEventMessage;
import net.closelink.cenqueue.objects.ApprovalRequest;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.Recipient;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.approval.created.ApprovalRequestCreatedHandler;
import net.closelink.notificationservice.handler.approval.replacement.ApprovalRequestCreatedReplacementSupportService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.offer.OfferApiRestService;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;
import net.closelink.notificationservice.service.rest.offerapprovalrequests.OfferApprovalRequestService;
import net.closelink.notificationservice.service.rest.offerapprovalrequests.OfferApprovalRequestsMessage;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ApprovalRequestCreatedHandlerTest {

    @InjectMocks
    private ApprovalRequestCreatedHandler approvalRequestCreatedHandler;

    @Mock
    private NotificationService notificationService;

    @Mock
    private ApprovalRequestCreatedReplacementSupportService approvalRequestCreatedReplacementSupportService;

    @Mock
    private OfferApprovalRequestService offerApprovalRequestService;

    @Mock
    private OfferApiRestService offerService;

    @AfterEach
    public void after() {
        Mockito.verifyNoMoreInteractions(notificationService);
        Mockito.verifyNoMoreInteractions(approvalRequestCreatedReplacementSupportService);
    }

    @Test
    public void shouldCallNotificationServiceForOffersWithSingleApprovalRequest() {
        var approvalRequestCreatedEventMessage = ApprovalRequestCreatedEventMessage.builder()
            .approvalRequest(
                ApprovalRequest.builder().offerId("offer-1").controllerEmailAddress("<EMAIL>").build()
            )
            .approverEmailAddress("<EMAIL>")
            .build();
        var event = DataCreator.createEvent(approvalRequestCreatedEventMessage);
        var replacements = DataCreator.createStringMap();
        var subjectReplacements = DataCreator.createStringArray();
        var approvalRequest = approvalRequestCreatedEventMessage.getApprovalRequest();
        var offerMessage = OfferMessage.builder().id(approvalRequest.getOfferId()).type("LUBES").build();

        Mockito.when(offerService.getOffer(approvalRequest.getOfferId())).thenReturn(offerMessage);

        Mockito.when(
            offerApprovalRequestService.getApprovalRequestsForOfferId(approvalRequest.getOfferId())
        ).thenReturn(
            Collections.singletonList(OfferApprovalRequestsMessage.ApprovalRequestMessage.builder().id("123").build())
        );
        Mockito.when(
            approvalRequestCreatedReplacementSupportService.createReplacements(
                ArgumentMatchers.any(ApprovalRequestCreatedEventMessage.class),
                ArgumentMatchers.any(OfferMessage.class)
            )
        ).thenReturn(replacements);

        Mockito.when(
            approvalRequestCreatedReplacementSupportService.createSubjectReplacements(offerMessage)
        ).thenReturn(subjectReplacements);

        approvalRequestCreatedHandler.handle(event);

        Recipient recipient = Recipient.builder()
            .emailAddress(approvalRequestCreatedEventMessage.getApproverEmailAddress())
            .type(Recipient.Type.TO)
            .build();

        Notification notification = Notification.builder()
            .template("Approval_Controller_Initial")
            .subject("approval.created.subject")
            .message("approval.created.message")
            .sendMail(true)
            .hidden(true)
            .receiverType(ReceiverType.CUSTOM)
            .recipients(Collections.singletonList(recipient))
            .offerId(approvalRequest.getOfferId())
            .senderId(approvalRequest.getCustomerId())
            .senderType(SenderType.CUSTOMER)
            .mailReplacements(replacements)
            .subjectReplacements(subjectReplacements)
            .build();

        verify(approvalRequestCreatedReplacementSupportService).createSubjectReplacements(offerMessage);

        verify(approvalRequestCreatedReplacementSupportService).createReplacements(
            ArgumentMatchers.any(ApprovalRequestCreatedEventMessage.class),
            ArgumentMatchers.any(OfferMessage.class)
        );
        verify(notificationService).notify(notification);
    }

    @Test
    public void shouldCallNotificationServiceForOffersWithMultipleApprovalRequests() {
        var approvalRequestCreatedEventMessage = ApprovalRequestCreatedEventMessage.builder()
            .approvalRequest(
                ApprovalRequest.builder().offerId("offer-1").controllerEmailAddress("<EMAIL>").build()
            )
            .approverEmailAddress("<EMAIL>")
            .build();
        var event = DataCreator.createEvent(approvalRequestCreatedEventMessage);
        var replacements = DataCreator.createStringMap();
        var subjectReplacements = DataCreator.createStringArray();
        var approvalRequest = approvalRequestCreatedEventMessage.getApprovalRequest();

        var offerMessage = OfferMessage.builder().id(approvalRequest.getOfferId()).type("LUBES").build();

        Mockito.when(offerService.getOffer(approvalRequest.getOfferId())).thenReturn(offerMessage);
        Mockito.when(
            offerApprovalRequestService.getApprovalRequestsForOfferId(approvalRequest.getOfferId())
        ).thenReturn(
            Arrays.asList(
                OfferApprovalRequestsMessage.ApprovalRequestMessage.builder().id("123").build(),
                OfferApprovalRequestsMessage.ApprovalRequestMessage.builder().id("456").build()
            )
        );

        Mockito.when(
            approvalRequestCreatedReplacementSupportService.createReplacements(
                ArgumentMatchers.any(ApprovalRequestCreatedEventMessage.class),
                ArgumentMatchers.any(OfferMessage.class)
            )
        ).thenReturn(replacements);

        Mockito.when(
            approvalRequestCreatedReplacementSupportService.createSubjectReplacements(offerMessage)
        ).thenReturn(subjectReplacements);

        approvalRequestCreatedHandler.handle(event);

        Recipient recipient = Recipient.builder()
            .emailAddress(approvalRequestCreatedEventMessage.getApproverEmailAddress())
            .type(Recipient.Type.TO)
            .build();

        Notification notification = Notification.builder()
            .template("Approval_Controller_Regain")
            .subject("approval.created.subject")
            .message("approval.created.message")
            .sendMail(true)
            .hidden(true)
            .receiverType(ReceiverType.CUSTOM)
            .recipients(Collections.singletonList(recipient))
            .offerId(approvalRequest.getOfferId())
            .senderId(approvalRequest.getCustomerId())
            .senderType(SenderType.CUSTOMER)
            .mailReplacements(replacements)
            .subjectReplacements(subjectReplacements)
            .build();

        verify(approvalRequestCreatedReplacementSupportService).createSubjectReplacements(offerMessage);
        verify(approvalRequestCreatedReplacementSupportService).createReplacements(
            ArgumentMatchers.any(ApprovalRequestCreatedEventMessage.class),
            ArgumentMatchers.any(OfferMessage.class)
        );
        verify(notificationService).notify(notification);
    }
}
