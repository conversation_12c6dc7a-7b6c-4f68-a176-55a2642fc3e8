package net.closelink.notificationservice.handler.approvalrequest.updated;

import java.util.Arrays;
import java.util.Collections;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestUpdatedEventMessage;
import net.closelink.cenqueue.objects.ApprovalRequest;
import net.closelink.notificationservice.handler.approval.updated.ApprovalRequestUpdatedApprovedHandler;
import net.closelink.notificationservice.handler.approval.updated.ApprovalRequestUpdatedNotificationSender;
import net.closelink.notificationservice.service.rest.offer.OfferApiRestService;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;
import net.closelink.notificationservice.service.rest.offerapprovalrequests.OfferApprovalRequestService;
import net.closelink.notificationservice.service.rest.offerapprovalrequests.OfferApprovalRequestsMessage;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class ApprovalRequestUpdatedApprovedHandlerTest {

    private ApprovalRequestUpdatedApprovedHandler approvalRequestUpdatedDeclinedHandler;

    @Mock
    private ApprovalRequestUpdatedNotificationSender approvalRequestUpdatedNotificationSender;

    @Mock
    private OfferApprovalRequestService offerApprovalRequestService;

    @Mock
    private OfferApiRestService offerService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        approvalRequestUpdatedDeclinedHandler = new ApprovalRequestUpdatedApprovedHandler(
            approvalRequestUpdatedNotificationSender,
            offerService,
            offerApprovalRequestService
        );
    }

    @AfterEach
    public void after() {
        Mockito.verifyNoMoreInteractions(approvalRequestUpdatedNotificationSender);
    }

    @Test
    public void shouldCallApprovalRequestUpdatedNotificationSenderForOffersWithSingleApprovalRequest() {
        ApprovalRequest approvalRequest = ApprovalRequest.builder().offerId("123").build();

        ApprovalRequestUpdatedEventMessage approvalRequestUpdatedEventMessage =
            ApprovalRequestUpdatedEventMessage.builder().approvalRequest(approvalRequest).build();

        var offerMessage = OfferMessage.builder().id(approvalRequest.getOfferId()).type("LUBES").build();

        Mockito.when(offerService.getOffer(approvalRequest.getOfferId())).thenReturn(offerMessage);
        Mockito.when(
            offerApprovalRequestService.getApprovalRequestsForOfferId(approvalRequest.getOfferId())
        ).thenReturn(
            Collections.singletonList(OfferApprovalRequestsMessage.ApprovalRequestMessage.builder().id("123").build())
        );

        approvalRequestUpdatedDeclinedHandler.handle(approvalRequestUpdatedEventMessage);

        Mockito.verify(approvalRequestUpdatedNotificationSender).sendNotification(
            approvalRequest,
            "Approval_Customer_Approved",
            "approvalRequest.approved.subject"
        );
    }

    @Test
    public void shouldCallApprovalRequestUpdatedNotificationSenderForOffersWithMultipleApprovalRequests() {
        ApprovalRequest approvalRequest = ApprovalRequest.builder().offerId("123").build();

        ApprovalRequestUpdatedEventMessage approvalRequestUpdatedEventMessage =
            ApprovalRequestUpdatedEventMessage.builder().approvalRequest(approvalRequest).build();

        var offerMessage = OfferMessage.builder().id(approvalRequest.getOfferId()).type("LUBES").build();

        Mockito.when(offerService.getOffer(approvalRequest.getOfferId())).thenReturn(offerMessage);
        Mockito.when(
            offerApprovalRequestService.getApprovalRequestsForOfferId(approvalRequest.getOfferId())
        ).thenReturn(
            Arrays.asList(
                OfferApprovalRequestsMessage.ApprovalRequestMessage.builder().id("123").build(),
                OfferApprovalRequestsMessage.ApprovalRequestMessage.builder().id("456").build()
            )
        );

        approvalRequestUpdatedDeclinedHandler.handle(approvalRequestUpdatedEventMessage);

        Mockito.verify(approvalRequestUpdatedNotificationSender).sendNotification(
            approvalRequest,
            "Approval_Customer_Regain_Approved",
            "approvalRequest.regain.approved.subject"
        );
    }
}
