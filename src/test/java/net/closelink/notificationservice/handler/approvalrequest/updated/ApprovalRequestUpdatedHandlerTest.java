package net.closelink.notificationservice.handler.approvalrequest.updated;

import static org.mockito.ArgumentCaptor.forClass;

import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestUpdatedEventMessage;
import net.closelink.cenqueue.objects.ApprovalRequest;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.approval.updated.ApprovalRequestUpdatedApprovedHandler;
import net.closelink.notificationservice.handler.approval.updated.ApprovalRequestUpdatedDeclinedHandler;
import net.closelink.notificationservice.handler.approval.updated.ApprovalRequestUpdatedHandler;
import net.closelink.notificationservice.service.rest.offer.OfferApiRestService;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ApprovalRequestUpdatedHandlerTest {

    @InjectMocks
    private ApprovalRequestUpdatedHandler approvalRequestUpdatedHandler;

    @Mock
    private ApprovalRequestUpdatedApprovedHandler approvalRequestUpdatedApprovedHandler;

    @Mock
    private ApprovalRequestUpdatedDeclinedHandler approvalRequestUpdatedDeclinedHandler;

    @Mock
    private OfferApiRestService offerService;

    @AfterEach
    public void after() {
        Mockito.verifyNoMoreInteractions(approvalRequestUpdatedApprovedHandler);
        Mockito.verifyNoMoreInteractions(approvalRequestUpdatedDeclinedHandler);
    }

    @Test
    public void shouldCallApprovedHandler() {
        ApprovalRequestUpdatedEventMessage approvalRequestUpdatedEventMessage = makeApprovalRequestUpdatedEventMessage(
            "APPROVED"
        );
        Event event = DataCreator.createEvent(approvalRequestUpdatedEventMessage);

        var offerId = approvalRequestUpdatedEventMessage.getApprovalRequest().getOfferId();
        var offerMessage = OfferMessage.builder().id(offerId).type("LUBES").build();

        Mockito.when(offerService.getOffer(offerId)).thenReturn(offerMessage);

        approvalRequestUpdatedHandler.handle(event);

        final ArgumentCaptor<ApprovalRequestUpdatedEventMessage> capture = forClass(
            ApprovalRequestUpdatedEventMessage.class
        );

        Mockito.verify(approvalRequestUpdatedApprovedHandler).handle(capture.capture());

        Assertions.assertThat(capture.getValue().getApprovalRequest().getState()).isEqualTo("APPROVED");
    }

    @Test
    public void shouldCallDeclinedHandler() {
        ApprovalRequestUpdatedEventMessage approvalRequestUpdatedEventMessage = makeApprovalRequestUpdatedEventMessage(
            "DECLINED"
        );
        Event event = DataCreator.createEvent(approvalRequestUpdatedEventMessage);

        var offerId = approvalRequestUpdatedEventMessage.getApprovalRequest().getOfferId();
        var offerMessage = OfferMessage.builder().id(offerId).type("LUBES").build();

        Mockito.when(offerService.getOffer(offerId)).thenReturn(offerMessage);

        approvalRequestUpdatedHandler.handle(event);

        final ArgumentCaptor<ApprovalRequestUpdatedEventMessage> capture = forClass(
            ApprovalRequestUpdatedEventMessage.class
        );

        Mockito.verify(approvalRequestUpdatedDeclinedHandler).handle(capture.capture());

        Assertions.assertThat(capture.getValue().getApprovalRequest().getState()).isEqualTo("DECLINED");
    }

    @Test
    public void shouldDoNothing() {
        ApprovalRequestUpdatedEventMessage approvalRequestUpdatedEventMessage = makeApprovalRequestUpdatedEventMessage(
            "WITHDRAWN"
        );
        Event event = DataCreator.createEvent(approvalRequestUpdatedEventMessage);

        var offerId = approvalRequestUpdatedEventMessage.getApprovalRequest().getOfferId();
        var offerMessage = OfferMessage.builder().id(offerId).type("LUBES").build();

        Mockito.when(offerService.getOffer(offerId)).thenReturn(offerMessage);

        approvalRequestUpdatedHandler.handle(event);
    }

    private ApprovalRequestUpdatedEventMessage makeApprovalRequestUpdatedEventMessage(String state) {
        ApprovalRequest approvalRequest = ApprovalRequest.builder().state(state).build();

        return ApprovalRequestUpdatedEventMessage.builder().approvalRequest(approvalRequest).build();
    }
}
