package net.closelink.notificationservice.handler.keyportcall;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import net.closelink.cenqueue.distributor.factory.EventFactory;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.keyportcall.KeyPortCallNotificationEventMessage;
import net.closelink.cenqueue.domainvalue.EventType;
import net.closelink.cenqueue.objects.KeyPortCall;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.Recipient;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.port.PortApiRestService;
import net.closelink.notificationservice.service.rest.port.domain.CountryMessage;
import net.closelink.notificationservice.service.rest.port.domain.PortMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.MailNotificationSettings;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class KeyPortCallNotificationCreatedHandlerTest {

    @Mock
    private WebAppReplacementService webappReplacementService;

    @Mock
    private NotificationService notificationService;

    @Mock
    private CustomerApiRestService customerApiRestService;

    @Mock
    private VesselApiRestService vesselApiRestService;

    @Mock
    private PortApiRestService portApiRestService;

    private KeyPortCallNotificationCreatedHandler handler;

    @BeforeEach
    public void setUp() {
        this.handler = new KeyPortCallNotificationCreatedHandler(
            webappReplacementService,
            notificationService,
            customerApiRestService,
            vesselApiRestService,
            portApiRestService
        );
    }

    @Test
    public void shouldSendNotificationForMultipleKeyPortCallNotifications() {
        String vesselId = "vesselId-1";
        String vesselName = "MS Classica";

        String customerId = "customerId-1";
        String customerName = "Lauterjung";

        HashMap<String, String> vesselDetailPageReplacements = new HashMap<>();
        vesselDetailPageReplacements.put("webapp_url", "https://staging.closelink.net/app/vessel/vesselId-1");

        Mockito.when(webappReplacementService.createVesselDetailPage(vesselId)).thenReturn(
            vesselDetailPageReplacements
        );

        Mockito.when(vesselApiRestService.getVessel(vesselId)).thenReturn(
            VesselMessage.builder()
                .id(vesselId)
                .name(vesselName)
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .keyPortCallSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .recipients(Collections.emptyList())
                                .build()
                        )
                        .build()
                )
                .build()
        );

        Mockito.when(customerApiRestService.getCustomer(customerId)).thenReturn(
            CustomerMessage.builder().name(customerName).build()
        );

        PortMessage portMessage1 = new PortMessage();
        portMessage1.setName("Hamburg");
        portMessage1.setLocCode("DEHAM");
        portMessage1.setId("portId-1");

        CountryMessage countryMessage1 = new CountryMessage();
        countryMessage1.setName("Germany");
        portMessage1.setCountry(countryMessage1);

        PortMessage portMessage2 = new PortMessage();
        portMessage2.setName("Rotterdam");
        portMessage2.setLocCode("NLRTM");
        portMessage2.setId("portId-2");

        CountryMessage countryMessage2 = new CountryMessage();
        countryMessage2.setName("Netherlands");
        portMessage2.setCountry(countryMessage2);

        PortMessage portMessage3 = new PortMessage();
        portMessage3.setName("Cuxhaven");
        portMessage3.setLocCode("DECUX");
        portMessage3.setId("portId-3");

        CountryMessage countryMessage3 = new CountryMessage();
        countryMessage3.setName("Germany");
        portMessage3.setCountry(countryMessage3);

        Mockito.when(portApiRestService.getPorts(Arrays.asList("portId-1", "portId-2", "portId-3"))).thenReturn(
            Arrays.asList(portMessage1, portMessage2, portMessage3)
        );

        KeyPortCall keyPortCall1 = KeyPortCall.builder().portId("portId-1").eta("2021-07-27T13:20:00Z").build();

        KeyPortCall keyPortCall2 = KeyPortCall.builder().portId("portId-2").eta("2021-08-15T13:20:00Z").build();

        KeyPortCall keyPortCall3 = KeyPortCall.builder().portId("portId-3").eta("2021-07-29T13:20:00Z").build();

        KeyPortCallNotificationEventMessage keyPortCallNotificationEventMessage =
            KeyPortCallNotificationEventMessage.builder()
                .keyPortCalls(Arrays.asList(keyPortCall1, keyPortCall2, keyPortCall3))
                .vesselId(vesselId)
                .customerId(customerId)
                .build();

        Event event = EventFactory.createEvent(
            "vessel_" + vesselId,
            EventType.KEY_PORT_CALL_NOTIFICATION_CREATED,
            keyPortCallNotificationEventMessage
        );

        HashMap<String, String> expectedReplacements = new HashMap<>();
        expectedReplacements.put("customer_name", customerName);
        expectedReplacements.put("webapp_url", "https://staging.closelink.net/app/vessel/vesselId-1");
        expectedReplacements.put("vessel_name", vesselName);
        expectedReplacements.put(
            "key_port_call_list",
            "<ul><li>Jul 27, 2021 - Hamburg - Germany (DEHAM)</li><li>Jul 29, 2021 - Cuxhaven - Germany (DECUX)</li><li>Aug 15, 2021 - Rotterdam - Netherlands (NLRTM)</li></ul>"
        );

        handler.handle(event);

        Notification expectedNotification = Notification.builder()
            .sendMail(true)
            .subject("keyPortCallNotification.created.subject")
            .subjectReplacements(new Object[] { vesselName })
            .template("Customer_KeyPortCall_Alert")
            .mailReplacements(expectedReplacements)
            .receiverType(ReceiverType.CUSTOMER)
            .receiverId(keyPortCallNotificationEventMessage.getCustomerId())
            .recipients(Collections.emptyList())
            .senderType(SenderType.SYSTEM)
            .category(NotificationCategory.KEY_PORT_CALL_REMINDER)
            .build();

        ArgumentCaptor<Notification> argumentCaptor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(argumentCaptor.capture());
        Notification actualNotification = argumentCaptor.getValue();

        Assertions.assertThat(actualNotification).usingRecursiveComparison().isEqualTo(expectedNotification);
    }

    @Test
    public void shouldSendNotificationForSingleKeyPortCallNotifications() {
        String vesselId = "vesselId-1";
        String vesselName = "MS Classica";

        String customerId = "customerId-1";
        String customerName = "Lauterjung";

        HashMap<String, String> vesselDetailPageReplacements = new HashMap<>();
        vesselDetailPageReplacements.put("webapp_url", "https://staging.closelink.net/app/vessel/vesselId-1");

        Mockito.when(webappReplacementService.createVesselDetailPage(vesselId)).thenReturn(
            vesselDetailPageReplacements
        );

        Mockito.when(vesselApiRestService.getVessel(vesselId)).thenReturn(
            VesselMessage.builder()
                .id(vesselId)
                .name(vesselName)
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .keyPortCallSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .recipients(Collections.emptyList())
                                .build()
                        )
                        .build()
                )
                .build()
        );

        Mockito.when(customerApiRestService.getCustomer(customerId)).thenReturn(
            CustomerMessage.builder().name(customerName).build()
        );

        PortMessage portMessage1 = new PortMessage();
        portMessage1.setName("Hamburg");
        portMessage1.setLocCode("DEHAM");
        portMessage1.setId("portId-1");

        CountryMessage countryMessage1 = new CountryMessage();
        countryMessage1.setName("Germany");
        portMessage1.setCountry(countryMessage1);

        Mockito.when(portApiRestService.getPorts(Collections.singletonList("portId-1"))).thenReturn(
            Collections.singletonList(portMessage1)
        );

        KeyPortCall keyPortCall1 = KeyPortCall.builder().portId("portId-1").eta("2021-07-27T13:20:00Z").build();

        KeyPortCallNotificationEventMessage keyPortCallNotificationEventMessage =
            KeyPortCallNotificationEventMessage.builder()
                .keyPortCalls(Collections.singletonList(keyPortCall1))
                .vesselId(vesselId)
                .customerId(customerId)
                .build();

        Event event = EventFactory.createEvent(
            "vessel_" + vesselId,
            EventType.KEY_PORT_CALL_NOTIFICATION_CREATED,
            keyPortCallNotificationEventMessage
        );

        HashMap<String, String> expectedReplacements = new HashMap<>();
        expectedReplacements.put("customer_name", customerName);
        expectedReplacements.put("webapp_url", "https://staging.closelink.net/app/vessel/vesselId-1");
        expectedReplacements.put("vessel_name", vesselName);
        expectedReplacements.put("key_port_call_list", "<ul><li>Jul 27, 2021 - Hamburg - Germany (DEHAM)</li></ul>");

        handler.handle(event);

        Notification expectedNotification = Notification.builder()
            .sendMail(true)
            .subject("keyPortCallNotification.created.subject")
            .subjectReplacements(new Object[] { vesselName })
            .template("Customer_KeyPortCall_Alert")
            .mailReplacements(expectedReplacements)
            .receiverType(ReceiverType.CUSTOMER)
            .receiverId(keyPortCallNotificationEventMessage.getCustomerId())
            .recipients(Collections.emptyList())
            .senderType(SenderType.SYSTEM)
            .category(NotificationCategory.KEY_PORT_CALL_REMINDER)
            .build();

        ArgumentCaptor<Notification> argumentCaptor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(argumentCaptor.capture());
        Notification actualNotification = argumentCaptor.getValue();

        Assertions.assertThat(actualNotification).usingRecursiveComparison().isEqualTo(expectedNotification);
    }

    @Test
    public void shouldSendNotificationForMultipleKeyPortCallNotificationsWithOpenOrders() {
        String vesselId = "vesselId-1";
        String vesselName = "MS Classica";

        String customerId = "customerId-1";
        String customerName = "Lauterjung";

        HashMap<String, String> vesselDetailPageReplacements = new HashMap<>();
        vesselDetailPageReplacements.put("webapp_url", "https://staging.closelink.net/app/vessel/vesselId-1");

        Mockito.when(webappReplacementService.createVesselDetailPage(vesselId)).thenReturn(
            vesselDetailPageReplacements
        );

        Mockito.when(vesselApiRestService.getVessel(vesselId)).thenReturn(
            VesselMessage.builder()
                .id(vesselId)
                .name(vesselName)
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .keyPortCallSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .recipients(Collections.emptyList())
                                .build()
                        )
                        .build()
                )
                .build()
        );

        Mockito.when(customerApiRestService.getCustomer(customerId)).thenReturn(
            CustomerMessage.builder().name(customerName).build()
        );

        PortMessage portMessage1 = new PortMessage();
        portMessage1.setName("Hamburg");
        portMessage1.setLocCode("DEHAM");
        portMessage1.setId("portId-1");

        CountryMessage countryMessage1 = new CountryMessage();
        countryMessage1.setName("Germany");
        portMessage1.setCountry(countryMessage1);

        PortMessage portMessage2 = new PortMessage();
        portMessage2.setName("Rotterdam");
        portMessage2.setLocCode("NLRTM");
        portMessage2.setId("portId-2");

        CountryMessage countryMessage2 = new CountryMessage();
        countryMessage2.setName("Netherlands");
        portMessage2.setCountry(countryMessage2);

        PortMessage portMessage3 = new PortMessage();
        portMessage3.setName("Cuxhaven");
        portMessage3.setLocCode("DECUX");
        portMessage3.setId("portId-3");

        CountryMessage countryMessage3 = new CountryMessage();
        countryMessage3.setName("Germany");
        portMessage3.setCountry(countryMessage3);

        Mockito.when(portApiRestService.getPorts(Arrays.asList("portId-1", "portId-2", "portId-3"))).thenReturn(
            Arrays.asList(portMessage1, portMessage2, portMessage3)
        );

        KeyPortCall keyPortCall1 = KeyPortCall.builder()
            .portId("portId-1")
            .eta("2021-07-27T13:20:00Z")
            .orderIds(Collections.singletonList("orderId-1"))
            .build();

        KeyPortCall keyPortCall2 = KeyPortCall.builder().portId("portId-2").eta("2021-08-15T13:20:00Z").build();
        KeyPortCall keyPortCall3 = KeyPortCall.builder()
            .portId("portId-3")
            .eta("2021-07-29T13:20:00Z")
            .orderIds(Collections.singletonList("orderId-2"))
            .build();

        KeyPortCallNotificationEventMessage keyPortCallNotificationEventMessage =
            KeyPortCallNotificationEventMessage.builder()
                .keyPortCalls(Arrays.asList(keyPortCall1, keyPortCall2, keyPortCall3))
                .vesselId(vesselId)
                .customerId(customerId)
                .build();

        Event event = EventFactory.createEvent(
            "vessel_" + vesselId,
            EventType.KEY_PORT_CALL_NOTIFICATION_CREATED,
            keyPortCallNotificationEventMessage
        );

        HashMap<String, String> expectedReplacements = new HashMap<>();
        expectedReplacements.put("customer_name", customerName);
        expectedReplacements.put("webapp_url", "https://staging.closelink.net/app/vessel/vesselId-1");
        expectedReplacements.put("vessel_name", vesselName);
        expectedReplacements.put(
            "key_port_call_list",
            "<ul><li><span style='color: #0001fe'>Jul 27, 2021 - Hamburg - Germany (DEHAM)<br />(pending enquiry/order)</span></li><li><span style='color: #0001fe'>Jul 29, 2021 - Cuxhaven - Germany (DECUX)<br />(pending enquiry/order)</span></li><li>Aug 15, 2021 - Rotterdam - Netherlands (NLRTM)</li></ul>"
        );

        handler.handle(event);

        Notification expectedNotification = Notification.builder()
            .sendMail(true)
            .subject("keyPortCallNotification.created.subject")
            .subjectReplacements(new Object[] { vesselName })
            .template("Customer_KeyPortCall_Alert_PendingOrder")
            .mailReplacements(expectedReplacements)
            .receiverType(ReceiverType.CUSTOMER)
            .receiverId(keyPortCallNotificationEventMessage.getCustomerId())
            .recipients(Collections.emptyList())
            .senderType(SenderType.SYSTEM)
            .category(NotificationCategory.KEY_PORT_CALL_REMINDER)
            .build();

        ArgumentCaptor<Notification> argumentCaptor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(argumentCaptor.capture());
        Notification actualNotification = argumentCaptor.getValue();

        Assertions.assertThat(actualNotification).usingRecursiveComparison().isEqualTo(expectedNotification);
    }

    @Test
    public void shouldSendNotificationForSingleKeyPortCallNotificationsOpenOrders() {
        String vesselId = "vesselId-1";
        String vesselName = "MS Classica";

        String customerId = "customerId-1";
        String customerName = "Lauterjung";

        HashMap<String, String> vesselDetailPageReplacements = new HashMap<>();
        vesselDetailPageReplacements.put("webapp_url", "https://staging.closelink.net/app/vessel/vesselId-1");

        Mockito.when(webappReplacementService.createVesselDetailPage(vesselId)).thenReturn(
            vesselDetailPageReplacements
        );

        Mockito.when(vesselApiRestService.getVessel(vesselId)).thenReturn(
            VesselMessage.builder()
                .id(vesselId)
                .name(vesselName)
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .keyPortCallSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .recipients(Collections.emptyList())
                                .build()
                        )
                        .build()
                )
                .build()
        );

        Mockito.when(customerApiRestService.getCustomer(customerId)).thenReturn(
            CustomerMessage.builder().name(customerName).build()
        );

        PortMessage portMessage1 = new PortMessage();
        portMessage1.setName("Hamburg");
        portMessage1.setLocCode("DEHAM");
        portMessage1.setId("portId-1");

        CountryMessage countryMessage1 = new CountryMessage();
        countryMessage1.setName("Germany");
        portMessage1.setCountry(countryMessage1);

        Mockito.when(portApiRestService.getPorts(Collections.singletonList("portId-1"))).thenReturn(
            Collections.singletonList(portMessage1)
        );

        KeyPortCall keyPortCall1 = KeyPortCall.builder()
            .portId("portId-1")
            .eta("2021-07-27T13:20:00Z")
            .orderIds(Collections.singletonList("orderId-1"))
            .build();

        KeyPortCallNotificationEventMessage keyPortCallNotificationEventMessage =
            KeyPortCallNotificationEventMessage.builder()
                .keyPortCalls(Collections.singletonList(keyPortCall1))
                .vesselId(vesselId)
                .customerId(customerId)
                .build();

        Event event = EventFactory.createEvent(
            "vessel_" + vesselId,
            EventType.KEY_PORT_CALL_NOTIFICATION_CREATED,
            keyPortCallNotificationEventMessage
        );

        HashMap<String, String> expectedReplacements = new HashMap<>();
        expectedReplacements.put("customer_name", customerName);
        expectedReplacements.put("webapp_url", "https://staging.closelink.net/app/vessel/vesselId-1");
        expectedReplacements.put("vessel_name", vesselName);
        expectedReplacements.put(
            "key_port_call_list",
            "<ul><li><span style='color: #0001fe'>Jul 27, 2021 - Hamburg - Germany (DEHAM)<br />(pending enquiry/order)</span></li></ul>"
        );

        handler.handle(event);

        Notification expectedNotification = Notification.builder()
            .sendMail(true)
            .subject("keyPortCallNotification.created.subject")
            .subjectReplacements(new Object[] { vesselName })
            .template("Customer_KeyPortCall_Alert_PendingOrder")
            .mailReplacements(expectedReplacements)
            .receiverType(ReceiverType.CUSTOMER)
            .receiverId(keyPortCallNotificationEventMessage.getCustomerId())
            .recipients(Collections.emptyList())
            .senderType(SenderType.SYSTEM)
            .category(NotificationCategory.KEY_PORT_CALL_REMINDER)
            .build();

        ArgumentCaptor<Notification> argumentCaptor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(argumentCaptor.capture());
        Notification actualNotification = argumentCaptor.getValue();

        Assertions.assertThat(actualNotification).usingRecursiveComparison().isEqualTo(expectedNotification);
    }

    @Test
    public void shouldSendNotificationToOtherVesselMailsNotifications() {
        String vesselId = "vesselId-1";
        String vesselName = "MS Classica";

        String customerId = "customerId-1";
        String customerName = "Lauterjung";

        HashMap<String, String> vesselDetailPageReplacements = new HashMap<>();
        vesselDetailPageReplacements.put("webapp_url", "https://staging.closelink.net/app/vessel/vesselId-1");

        Mockito.when(webappReplacementService.createVesselDetailPage(vesselId)).thenReturn(
            vesselDetailPageReplacements
        );

        Mockito.when(vesselApiRestService.getVessel(vesselId)).thenReturn(
            VesselMessage.builder()
                .id(vesselId)
                .name(vesselName)
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .keyPortCallSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(true)
                                .sendToOthers(true)
                                .recipients(Collections.singletonList("<EMAIL>"))
                                .build()
                        )
                        .build()
                )
                .build()
        );

        Mockito.when(customerApiRestService.getCustomer(customerId)).thenReturn(
            CustomerMessage.builder().name(customerName).build()
        );

        PortMessage portMessage1 = new PortMessage();
        portMessage1.setName("Hamburg");
        portMessage1.setLocCode("DEHAM");
        portMessage1.setId("portId-1");

        CountryMessage countryMessage1 = new CountryMessage();
        countryMessage1.setName("Germany");
        portMessage1.setCountry(countryMessage1);

        Mockito.when(portApiRestService.getPorts(Collections.singletonList("portId-1"))).thenReturn(
            Collections.singletonList(portMessage1)
        );

        KeyPortCall keyPortCall1 = KeyPortCall.builder().portId("portId-1").eta("2021-07-27T13:20:00Z").build();

        KeyPortCallNotificationEventMessage keyPortCallNotificationEventMessage =
            KeyPortCallNotificationEventMessage.builder()
                .keyPortCalls(Collections.singletonList(keyPortCall1))
                .vesselId(vesselId)
                .customerId(customerId)
                .build();

        Event event = EventFactory.createEvent(
            "vessel_" + vesselId,
            EventType.KEY_PORT_CALL_NOTIFICATION_CREATED,
            keyPortCallNotificationEventMessage
        );

        HashMap<String, String> expectedReplacements = new HashMap<>();
        expectedReplacements.put("customer_name", customerName);
        expectedReplacements.put("webapp_url", "https://staging.closelink.net/app/vessel/vesselId-1");
        expectedReplacements.put("vessel_name", vesselName);
        expectedReplacements.put("key_port_call_list", "<ul><li>Jul 27, 2021 - Hamburg - Germany (DEHAM)</li></ul>");

        handler.handle(event);

        Notification expectedNotification = Notification.builder()
            .sendMail(true)
            .subject("keyPortCallNotification.created.subject")
            .subjectReplacements(new Object[] { vesselName })
            .template("Customer_KeyPortCall_Alert")
            .mailReplacements(expectedReplacements)
            .receiverType(ReceiverType.CUSTOM)
            .recipients(Collections.singletonList(Recipient.builder().emailAddress("<EMAIL>").build()))
            .receiverId(keyPortCallNotificationEventMessage.getCustomerId())
            .senderType(SenderType.SYSTEM)
            .category(NotificationCategory.KEY_PORT_CALL_REMINDER)
            .build();

        ArgumentCaptor<Notification> argumentCaptor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(argumentCaptor.capture());
        Notification actualNotification = argumentCaptor.getValue();

        Assertions.assertThat(actualNotification).usingRecursiveComparison().isEqualTo(expectedNotification);
    }
}
