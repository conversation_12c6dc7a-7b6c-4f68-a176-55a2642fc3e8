package net.closelink.notificationservice.handler.lead;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.HashMap;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.service.notification.NotificationService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class LeadCreatedHandlerTest {

    private LeadCreatedHandler leadCreatedHandler;
    private NotificationService notificationService;

    @Before
    public void setUp() {
        this.notificationService = Mockito.mock(NotificationService.class);

        this.leadCreatedHandler = new LeadCreatedHandler(notificationService);
    }

    @Test
    public void shouldCallNotificationServiceIncludingAllValues() {
        String name = "<PERSON>";
        String companyName = "Closelink";
        String businessEmailAddress = "<EMAIL>";
        String phoneNumber = "01123 4123";
        String referrer = "http://localhost:8080";
        String message = "Lirum Larum";

        Event leadCreatedEventMessage = DataCreator.createEvent(
            DataCreator.createLeadCreatedEventMessage(
                name,
                companyName,
                businessEmailAddress,
                phoneNumber,
                referrer,
                message
            )
        );

        leadCreatedHandler.handle(leadCreatedEventMessage);

        final ArgumentCaptor<Notification> messageCapture = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(messageCapture.capture());

        Notification actualNotification = messageCapture.getValue();

        assertTrue(actualNotification.isSendMail());
        assertEquals("leadCreated.subject", actualNotification.getSubject());
        assertEquals("Lead_Created", actualNotification.getTemplate());
        assertEquals(ReceiverType.SYSTEM, actualNotification.getReceiverType());
        assertEquals(SenderType.SYSTEM, actualNotification.getSenderType());

        HashMap expectedReplacementMap = createExpectedReplacementMap(
            name,
            businessEmailAddress,
            companyName,
            phoneNumber,
            message,
            referrer
        );

        assertEquals(expectedReplacementMap, actualNotification.getMailReplacements());
    }

    @Test
    public void shouldCallNotificationNullValues() {
        String name = "Mac Miller";
        String companyName = "Closelink";
        String businessEmailAddress = "<EMAIL>";
        String phoneNumber = null;
        String referrer = "https://localhost:8080";
        String message = null;

        Event leadCreatedEventMessage = DataCreator.createEvent(
            DataCreator.createLeadCreatedEventMessage(
                name,
                companyName,
                businessEmailAddress,
                phoneNumber,
                referrer,
                message
            )
        );

        leadCreatedHandler.handle(leadCreatedEventMessage);

        final ArgumentCaptor<Notification> messageCapture = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(messageCapture.capture());

        Notification actualNotification = messageCapture.getValue();

        assertTrue(actualNotification.isSendMail());
        assertEquals("leadCreated.subject", actualNotification.getSubject());
        assertEquals("Lead_Created", actualNotification.getTemplate());
        assertEquals(ReceiverType.SYSTEM, actualNotification.getReceiverType());
        assertEquals(SenderType.SYSTEM, actualNotification.getSenderType());

        HashMap expectedReplacementMap = createExpectedReplacementMap(
            name,
            businessEmailAddress,
            companyName,
            "N/A",
            "N/A",
            referrer
        );

        assertEquals(expectedReplacementMap, actualNotification.getMailReplacements());
    }

    private HashMap createExpectedReplacementMap(
        String name,
        String businessEmailAddress,
        String companyName,
        String phoneNumber,
        String message,
        String referrer
    ) {
        HashMap<String, String> expectedReplacementMap = new HashMap<>();
        expectedReplacementMap.put("NAME", name);
        expectedReplacementMap.put("BUSINESS_EMAIL_ADDRESS", businessEmailAddress);
        expectedReplacementMap.put("COMPANY_NAME", companyName);
        expectedReplacementMap.put("PHONE_NUMBER", phoneNumber);
        expectedReplacementMap.put("USER_MESSAGE", message);
        expectedReplacementMap.put("REFERRER", referrer);

        return expectedReplacementMap;
    }
}
