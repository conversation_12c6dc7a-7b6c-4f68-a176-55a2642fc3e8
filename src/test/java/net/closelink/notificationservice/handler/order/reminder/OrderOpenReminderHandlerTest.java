package net.closelink.notificationservice.handler.order.reminder;

import static net.closelink.notificationservice.data.DataCreator.createOrderOpenReminderEventMessage;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Map;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.order.OrderOpenReminderEventMessage;
import net.closelink.cenqueue.objects.Order;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.replacement.order.OrderReplacementService;
import net.closelink.notificationservice.replacement.order.domain.ReplacementOrder;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.MailNotificationSettings;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

public class OrderOpenReminderHandlerTest {

    private OrderOpenReminderHandler handler;
    private NotificationService notificationService;
    private VesselApiRestService vesselApiRestService;
    private OrderReplacementService orderReplacementService;
    private WebAppReplacementService webappReplacementService;
    private Order order;
    private Map<String, String> replacements;

    @Before
    public void setUp() {
        this.notificationService = Mockito.mock(NotificationService.class);
        this.vesselApiRestService = Mockito.mock(VesselApiRestService.class);
        this.orderReplacementService = Mockito.mock(OrderReplacementService.class);
        this.webappReplacementService = Mockito.mock(WebAppReplacementService.class);
        this.handler = new OrderOpenReminderHandler(
            this.notificationService,
            this.vesselApiRestService,
            this.orderReplacementService,
            this.webappReplacementService
        );

        this.order = DataCreator.createOrder();
        this.replacements = DataCreator.createStringMap();
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.notificationService);
        Mockito.verifyNoMoreInteractions(this.vesselApiRestService);
        Mockito.verifyNoMoreInteractions(this.orderReplacementService);
        Mockito.verifyNoMoreInteractions(this.webappReplacementService);
    }

    @Test
    public void handle() {
        var vessel = VesselMessage.builder()
            .name("vessel-1")
            .mailNotificationSettings(
                MailNotificationSettings.builder()
                    .orderUpdateSettings(
                        MailNotificationSettings.NotificationSettings.builder()
                            .enabled(true)
                            .sendToOthers(true)
                            .recipients(List.of("<EMAIL>"))
                            .build()
                    )
                    .build()
            )
            .build();
        when(this.orderReplacementService.create(ArgumentMatchers.any(ReplacementOrder.class))).thenReturn(
            this.replacements
        );
        when(this.vesselApiRestService.getVessel(this.order.getVesselId())).thenReturn(vessel);

        final OrderOpenReminderEventMessage eventMessage = createOrderOpenReminderEventMessage(this.order);
        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));

        this.handler.handle(event);

        verifyNotification(vessel);
        verify(this.vesselApiRestService).getVessel(this.order.getVesselId());
        verify(this.orderReplacementService).create(ArgumentMatchers.any(ReplacementOrder.class));
        verify(this.webappReplacementService).create(ArgumentMatchers.any(String.class));
    }

    @Test
    public void shouldNotDoAnythingForOrderTypeFuel() {
        when(this.orderReplacementService.create(ArgumentMatchers.any(ReplacementOrder.class))).thenReturn(
            this.replacements
        );
        when(this.vesselApiRestService.getVessel(this.order.getVesselId())).thenReturn(VesselMessage.builder().build());

        this.order.setType(OrderType.FUEL);
        final OrderOpenReminderEventMessage eventMessage = createOrderOpenReminderEventMessage(this.order);
        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));

        this.handler.handle(event);

        verifyNoInteractions(this.notificationService);
        verifyNoInteractions(this.vesselApiRestService);
        verifyNoInteractions(this.orderReplacementService);
        verifyNoInteractions(this.webappReplacementService);
    }

    private void verifyNotification(VesselMessage vessel) {
        final ArgumentCaptor<Notification> notificationCaptor = ArgumentCaptor.forClass(Notification.class);
        verify(this.notificationService).notify(notificationCaptor.capture());

        final Notification notification = notificationCaptor.getValue();

        assertEquals(ReceiverType.CUSTOM, notification.getReceiverType());
        assertFalse(notification.getRecipients().isEmpty());
        assertEquals(this.order.getCustomerId(), notification.getReceiverId());
        assertEquals(this.order.getId(), notification.getOrderId());
        assertTrue(notification.isSendMail());
        assertTrue(notification.isHidden());
        assertEquals(SenderType.SYSTEM, notification.getSenderType());
        assertEquals(NotificationCategory.OPEN_ENQUIRY_REMINDER, notification.getCategory());
        assertNotNull(notification.getMessage());
        assertNotNull(notification.getSubject());
        assertNotNull(notification.getTemplate());
        assertNotNull(notification.getMailReplacements());
        assertEquals(this.replacements, notification.getMailReplacements());
        assertEquals(vessel.getName(), notification.getSubjectReplacements()[0]);
        assertEquals(this.order.getOrderNumber(), notification.getSubjectReplacements()[1]);
    }
}
