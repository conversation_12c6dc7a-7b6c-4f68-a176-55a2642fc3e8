package net.closelink.notificationservice.handler.order.created;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Map;
import net.closelink.cenqueue.domainobject.order.OrderCreatedEventMessage;
import net.closelink.cenqueue.objects.Order;
import net.closelink.cenqueue.types.OrderState;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.replacement.order.OrderReplacementService;
import net.closelink.notificationservice.replacement.order.domain.ReplacementOrder;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.MailNotificationSettings;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

public class OrderDraftCreatedHandlerTest {

    private OrderDraftCreatedHandler handler;
    private NotificationService notificationService;
    private VesselApiRestService vesselApiRestService;
    private OrderReplacementService OrderReplacementService;
    private WebAppReplacementService webappReplacementService;
    private Order order;
    private Map<String, String> replacements;

    @Before
    public void setUp() {
        this.notificationService = Mockito.mock(NotificationService.class);
        this.vesselApiRestService = Mockito.mock(VesselApiRestService.class);
        this.OrderReplacementService = Mockito.mock(OrderReplacementService.class);
        this.webappReplacementService = Mockito.mock(WebAppReplacementService.class);
        this.handler = new OrderDraftCreatedHandler(
            this.notificationService,
            this.vesselApiRestService,
            this.OrderReplacementService,
            this.webappReplacementService
        );

        this.order = DataCreator.createOrder();
        this.order.setState(OrderState.DRAFT);
        this.replacements = DataCreator.createStringMap();
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.notificationService);
        Mockito.verifyNoMoreInteractions(this.vesselApiRestService);
        Mockito.verifyNoMoreInteractions(this.OrderReplacementService);
        Mockito.verifyNoMoreInteractions(this.webappReplacementService);
    }

    @Test
    public void shouldSendOutEmailForIntegrationOrders() {
        order.setSource("INTEGRATION");
        var vessel = VesselMessage.builder()
            .name("vessel-1")
            .mailNotificationSettings(
                MailNotificationSettings.builder()
                    .orderUpdateSettings(
                        MailNotificationSettings.NotificationSettings.builder()
                            .enabled(true)
                            .sendToOthers(true)
                            .recipients(List.of("<EMAIL>"))
                            .build()
                    )
                    .build()
            )
            .build();
        when(this.OrderReplacementService.create(ArgumentMatchers.any(ReplacementOrder.class))).thenReturn(
            this.replacements
        );
        when(this.vesselApiRestService.getVessel(this.order.getVesselId())).thenReturn(vessel);

        final OrderCreatedEventMessage eventMessage = DataCreator.createOrderCreatedEventMessage(this.order);

        this.handler.handle(eventMessage);

        final ArgumentCaptor<Notification> notificationCaptor = ArgumentCaptor.forClass(Notification.class);
        verify(this.notificationService).notify(notificationCaptor.capture());

        final Notification notification = notificationCaptor.getValue();

        assertEquals(ReceiverType.CUSTOM, notification.getReceiverType());
        assertFalse(notification.getRecipients().isEmpty());
        assertEquals(this.order.getId(), notification.getOrderId());
        assertEquals(this.order.getCustomerId(), notification.getReceiverId());
        assertTrue(notification.isSendMail());
        assertTrue(notification.isHidden());
        assertEquals(SenderType.SYSTEM, notification.getSenderType());
        assertNotNull(notification.getSubject());
        assertNotNull(notification.getTemplate());
        assertNotNull(notification.getMailReplacements());
        assertEquals(this.replacements, notification.getMailReplacements());
        assertEquals(this.order.getBuyerReference(), notification.getSubjectReplacements()[0]);
        assertEquals(vessel.getName(), notification.getSubjectReplacements()[1]);

        verify(this.vesselApiRestService).getVessel(this.order.getVesselId());
        verify(this.OrderReplacementService).create(ArgumentMatchers.any(ReplacementOrder.class));
        verify(this.webappReplacementService).createDraft(ArgumentMatchers.any(String.class));
    }

    @Test
    public void shouldNotSendOutEmailRegularOrders() {
        order.setSource(null);

        when(this.OrderReplacementService.create(ArgumentMatchers.any(ReplacementOrder.class))).thenReturn(
            this.replacements
        );
        when(this.vesselApiRestService.getVessel(this.order.getVesselId())).thenReturn(VesselMessage.builder().build());

        final OrderCreatedEventMessage eventMessage = DataCreator.createOrderCreatedEventMessage(this.order);

        this.handler.handle(eventMessage);

        verifyNoMoreInteractions(this.notificationService);
        verifyNoMoreInteractions(this.vesselApiRestService);
        verifyNoMoreInteractions(this.OrderReplacementService);
        verifyNoMoreInteractions(this.webappReplacementService);
    }
}
