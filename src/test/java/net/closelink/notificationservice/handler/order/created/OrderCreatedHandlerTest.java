package net.closelink.notificationservice.handler.order.created;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.order.OrderCreatedEventMessage;
import net.closelink.cenqueue.objects.Order;
import net.closelink.cenqueue.types.OrderState;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.notificationservice.data.DataCreator;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class OrderCreatedHandlerTest {

    private OrderCreatedHandler handler;
    private OrderDraftCreatedHandler orderDraftCreatedHandler;

    @Before
    public void setUp() {
        this.orderDraftCreatedHandler = Mockito.mock(OrderDraftCreatedHandler.class);
        this.handler = new OrderCreatedHandler(this.orderDraftCreatedHandler);
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.orderDraftCreatedHandler);
    }

    @Test
    public void handle() {
        Order order = DataCreator.createOrder();
        order.setState(OrderState.DRAFT);

        final OrderCreatedEventMessage eventMessage = DataCreator.createOrderCreatedEventMessage(order);
        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));

        this.handler.handle(event);

        verify(this.orderDraftCreatedHandler).handle(eventMessage);
    }

    @Test
    public void handleOpen() {
        Order order = DataCreator.createOrder();
        order.setState(OrderState.OPEN);

        final OrderCreatedEventMessage eventMessage = DataCreator.createOrderCreatedEventMessage(order);
        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));

        this.handler.handle(event);
    }

    @Test
    public void shouldNotDoAnythingForOrderTypeFuel() {
        Order order = DataCreator.createOrder();
        order.setState(OrderState.DRAFT);
        order.setType(OrderType.FUEL);

        final OrderCreatedEventMessage eventMessage = DataCreator.createOrderCreatedEventMessage(order);
        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));

        this.handler.handle(event);

        verifyNoInteractions(this.orderDraftCreatedHandler);
    }
}
