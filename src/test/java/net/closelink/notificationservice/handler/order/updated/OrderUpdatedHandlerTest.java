package net.closelink.notificationservice.handler.order.updated;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.order.OrderUpdatedEventMessage;
import net.closelink.cenqueue.domainvalue.order.OrderUpdatedEventType;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.exception.NoHandlerFoundException;
import net.closelink.notificationservice.handler.order.updated.support.OrderUpdatedEvent;
import net.closelink.notificationservice.handler.order.updated.support.OrderUpdatedEventTypeHandler;
import net.closelink.notificationservice.handler.order.updated.support.OrderUpdatedHandlerRegistry;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentMatchers;

public class OrderUpdatedHandlerTest {

    private OrderUpdatedHandler service;
    private OrderUpdatedHandlerRegistry handlerRegistry;

    @Before
    public void setUp() {
        this.handlerRegistry = mock(OrderUpdatedHandlerRegistry.class);
        this.service = new OrderUpdatedHandler(this.handlerRegistry);
    }

    @After
    public void afterTest() {
        verifyNoMoreInteractions(this.handlerRegistry);
    }

    @Test
    public void handleTest() {
        final OrderUpdatedEventType updatedEventType = DataCreator.createOrderUpdatedEventType();
        final OrderUpdatedEventMessage eventMessage = DataCreator.createOrderUpdatedEventMessage(updatedEventType);

        final Event event = DataCreator.createEvent(eventMessage);
        final OrderUpdatedEventTypeHandler eventHandler = mock(OrderUpdatedEventTypeHandler.class);

        when(this.handlerRegistry.get(eventMessage.getEventType())).thenReturn(eventHandler);

        this.service.handle(event);

        verify(eventHandler).handle(ArgumentMatchers.any(OrderUpdatedEvent.class));
        verifyNoMoreInteractions(eventHandler);
        verify(this.handlerRegistry).get(eventMessage.getEventType());
    }

    @Test
    public void shouldNotCallHandlerForFuelOrderType() {
        final OrderUpdatedEventType updatedEventType = DataCreator.createOrderUpdatedEventType();
        final OrderUpdatedEventMessage eventMessage = DataCreator.createOrderUpdatedEventMessage(updatedEventType);
        eventMessage.getOrder().setType(OrderType.FUEL);

        final Event event = DataCreator.createEvent(eventMessage);
        final OrderUpdatedEventTypeHandler eventHandler = mock(OrderUpdatedEventTypeHandler.class);

        when(this.handlerRegistry.get(eventMessage.getEventType())).thenReturn(eventHandler);

        this.service.handle(event);

        verifyNoInteractions(eventHandler);
        verifyNoInteractions(this.handlerRegistry);
    }

    @Test(expected = NoHandlerFoundException.class)
    public void handleFailTest() {
        final OrderUpdatedEventType updatedEventType = DataCreator.createOrderUpdatedEventType();
        final OrderUpdatedEventMessage eventMessage = DataCreator.createOrderUpdatedEventMessage(updatedEventType);

        final Event event = DataCreator.createEvent(eventMessage);
        when(this.handlerRegistry.get(eventMessage.getEventType())).thenReturn(null);

        try {
            this.service.handle(event);
        } catch (final Exception e) {
            throw e;
        } finally {
            verify(this.handlerRegistry).get(eventMessage.getEventType());
        }
    }
}
