package net.closelink.notificationservice.handler.demand;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import net.closelink.cenqueue.distributor.factory.EventFactory;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.demand.StockDemandNotificationEventMessage;
import net.closelink.cenqueue.domainvalue.EventType;
import net.closelink.cenqueue.objects.StockDemand;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.Recipient;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.coredata.CoreDataService;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.product.ProductApiRestService;
import net.closelink.notificationservice.service.rest.product.domain.ProductMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.MailNotificationSettings;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import net.closelink.notificationservice.service.rest.vesseltank.VesselTankApiRestService;
import net.closelink.notificationservice.service.rest.vesseltank.VesselTanksMessage;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class StockSafetyReserveDemandNotificationCreatedHandlerTest {

    @Mock
    public WebAppReplacementService webappReplacementService;

    @Mock
    public VesselApiRestService vesselApiRestService;

    @Mock
    public CustomerApiRestService customerApiRestService;

    @Mock
    public NotificationService notificationService;

    @Mock
    private VesselTankApiRestService vesselTankApiRestService;

    @Mock
    private ProductApiRestService productApiRestService;

    @Mock
    private CoreDataService coreDataService;

    private StockSafetyReserveDemandNotificationCreatedHandler stockSafetyReserveDemandNotificationCreatedHandler;

    @BeforeEach
    public void setUp() {
        this.stockSafetyReserveDemandNotificationCreatedHandler =
            new StockSafetyReserveDemandNotificationCreatedHandler(
                notificationService,
                customerApiRestService,
                vesselApiRestService,
                vesselTankApiRestService,
                productApiRestService,
                webappReplacementService,
                coreDataService
            );
    }

    @Test
    public void shouldSendStockDemandNotification() {
        String vesselId = "vesselId-1";
        List<String> vesselTankIds = Arrays.asList("vesselTankId-1", "vesselTankId-2");
        String vesselName = "MS Classica";

        String customerId = "customerId-1";
        String customerName = "Lauterjung";

        HashMap<String, String> vesselDetailPageReplacements = new HashMap<>();
        vesselDetailPageReplacements.put("webapp_url", "https://staging.closelink.net/app/vessel/vesselId-1");

        Mockito.when(webappReplacementService.createVesselDetailPage(vesselId)).thenReturn(
            vesselDetailPageReplacements
        );

        Mockito.when(customerApiRestService.getCustomer(customerId)).thenReturn(
            CustomerMessage.builder().id(customerId).name(customerName).build()
        );

        Mockito.when(vesselApiRestService.getVessel(vesselId)).thenReturn(
            VesselMessage.builder()
                .id(vesselId)
                .name(vesselName)
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .stockWarningSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(true)
                                .sendToOthers(true)
                                .recipients(Collections.singletonList("<EMAIL>"))
                                .build()
                        )
                        .build()
                )
                .build()
        );

        Mockito.when(vesselTankApiRestService.getVesselTanks(vesselTankIds)).thenReturn(
            Arrays.asList(
                VesselTanksMessage.VesselTankMessage.builder()
                    .id(vesselTankIds.get(0))
                    .name("Cat Name 1")
                    .category("Category 1")
                    .defaultProductId("defaultProduct-1")
                    .build(),
                VesselTanksMessage.VesselTankMessage.builder()
                    .id(vesselTankIds.get(1))
                    .category("Category 2")
                    .defaultProductId("defaultProduct-2")
                    .build()
            )
        );

        Mockito.when(
            productApiRestService.getProducts(new HashSet<>(Arrays.asList("defaultProduct-1", "defaultProduct-2")))
        ).thenReturn(
            new HashSet<>(
                Arrays.asList(
                    ProductMessage.builder().id("defaultProduct-1").name("Cyltech 100").group("CYLINDER_OIL").build(),
                    ProductMessage.builder().id("defaultProduct-2").name("Cyltech 70").group("ANCILLARY_OIL").build()
                )
            )
        );

        Mockito.when(coreDataService.getHumanReadableValueForVesselTankCategory("Category 1")).thenReturn(null);
        Mockito.when(coreDataService.getHumanReadableValueForVesselTankCategory("Category 2")).thenReturn("Cat 2");

        Mockito.when(coreDataService.getHumanReadableValueForProductGroup("CYLINDER_OIL")).thenReturn("Cylinder Oil");
        Mockito.when(coreDataService.getHumanReadableValueForProductGroup("ANCILLARY_OIL")).thenReturn("Ancillary Oil");

        StockDemandNotificationEventMessage stockDemandNotificationEventMessage =
            StockDemandNotificationEventMessage.builder()
                .vesselId(vesselId)
                .customerId(customerId)
                .stockDemands(
                    Arrays.asList(
                        StockDemand.builder().vesselTankId(vesselTankIds.get(0)).build(),
                        StockDemand.builder().vesselTankId(vesselTankIds.get(1)).build()
                    )
                )
                .build();

        Event event = EventFactory.createEvent(
            "vessel_" + vesselId,
            EventType.KEY_PORT_CALL_NOTIFICATION_CREATED,
            stockDemandNotificationEventMessage
        );

        HashMap<String, String> expectedReplacements = new HashMap<>();
        expectedReplacements.put("customer_name", customerName);
        expectedReplacements.put("webapp_url", "https://staging.closelink.net/app/vessel/vesselId-1");
        expectedReplacements.put("vessel_name", vesselName);
        expectedReplacements.put(
            "stock_demand_list",
            "<ul><li>Cat Name 1 - Cylinder Oil (Cyltech 100)</li><li>Cat 2 - Ancillary Oil (Cyltech 70)</li></ul>"
        );

        stockSafetyReserveDemandNotificationCreatedHandler.handle(event);

        Notification expectedNotification = Notification.builder()
            .sendMail(true)
            .subject("stockDemand.safetyReserve.subject")
            .subjectReplacements(new Object[] { vesselName })
            .template("Customer_Safety_Reserve_Alert")
            .mailReplacements(expectedReplacements)
            .receiverType(ReceiverType.CUSTOM)
            .recipients(Collections.singletonList(Recipient.builder().emailAddress("<EMAIL>").build()))
            .receiverId(stockDemandNotificationEventMessage.getCustomerId())
            .senderType(SenderType.SYSTEM)
            .category(NotificationCategory.SAFETY_RESERVE_LEVEL_REMINDER)
            .build();

        ArgumentCaptor<Notification> argumentCaptor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(argumentCaptor.capture());
        Notification actualNotification = argumentCaptor.getValue();

        Assertions.assertThat(actualNotification).usingRecursiveComparison().isEqualTo(expectedNotification);
    }

    @Test
    public void shouldSendStockDemandNotificationAndNotRepeatTheTankLines() {
        String vesselId = "vesselId-1";
        List<String> vesselTankIds = Arrays.asList("vesselTankId-1", "vesselTankId-2");
        String vesselName = "MS Classica";

        String customerId = "customerId-1";
        String customerName = "Lauterjung";

        HashMap<String, String> vesselDetailPageReplacements = new HashMap<>();
        vesselDetailPageReplacements.put("webapp_url", "https://staging.closelink.net/app/vessel/vesselId-1");

        Mockito.when(webappReplacementService.createVesselDetailPage(vesselId)).thenReturn(
            vesselDetailPageReplacements
        );

        Mockito.when(customerApiRestService.getCustomer(customerId)).thenReturn(
            CustomerMessage.builder().id(customerId).name(customerName).build()
        );

        Mockito.when(vesselApiRestService.getVessel(vesselId)).thenReturn(
            VesselMessage.builder()
                .id(vesselId)
                .name(vesselName)
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .stockWarningSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(true)
                                .sendToOthers(true)
                                .recipients(Collections.singletonList("<EMAIL>"))
                                .build()
                        )
                        .build()
                )
                .build()
        );

        Mockito.when(vesselTankApiRestService.getVesselTanks(Mockito.any())).thenReturn(
            Arrays.asList(
                VesselTanksMessage.VesselTankMessage.builder()
                    .id(vesselTankIds.get(0))
                    .name("Cat Name 1")
                    .category("Category 1")
                    .defaultProductId("defaultProduct-1")
                    .build(),
                VesselTanksMessage.VesselTankMessage.builder()
                    .id(vesselTankIds.get(1))
                    .category("Category 2")
                    .defaultProductId("defaultProduct-2")
                    .build()
            )
        );

        Mockito.when(
            productApiRestService.getProducts(new HashSet<>(Arrays.asList("defaultProduct-1", "defaultProduct-2")))
        ).thenReturn(
            new HashSet<>(
                Arrays.asList(
                    ProductMessage.builder().id("defaultProduct-1").name("Cyltech 100").group("CYLINDER_OIL").build(),
                    ProductMessage.builder().id("defaultProduct-2").name("Cyltech 70").group("ANCILLARY_OIL").build()
                )
            )
        );

        Mockito.when(coreDataService.getHumanReadableValueForVesselTankCategory("Category 1")).thenReturn(null);
        Mockito.when(coreDataService.getHumanReadableValueForVesselTankCategory("Category 2")).thenReturn("Cat 2");

        Mockito.when(coreDataService.getHumanReadableValueForProductGroup("CYLINDER_OIL")).thenReturn("Cylinder Oil");
        Mockito.when(coreDataService.getHumanReadableValueForProductGroup("ANCILLARY_OIL")).thenReturn("Ancillary Oil");

        StockDemandNotificationEventMessage stockDemandNotificationEventMessage =
            StockDemandNotificationEventMessage.builder()
                .vesselId(vesselId)
                .customerId(customerId)
                .stockDemands(
                    Arrays.asList(
                        StockDemand.builder().vesselTankId(vesselTankIds.get(0)).build(),
                        StockDemand.builder().vesselTankId(vesselTankIds.get(1)).build(),
                        StockDemand.builder().vesselTankId(vesselTankIds.get(0)).build(),
                        StockDemand.builder().vesselTankId(vesselTankIds.get(1)).build(),
                        StockDemand.builder().vesselTankId(vesselTankIds.get(0)).build(),
                        StockDemand.builder().vesselTankId(vesselTankIds.get(1)).build()
                    )
                )
                .build();

        Event event = EventFactory.createEvent(
            "vessel_" + vesselId,
            EventType.KEY_PORT_CALL_NOTIFICATION_CREATED,
            stockDemandNotificationEventMessage
        );

        HashMap<String, String> expectedReplacements = new HashMap<>();
        expectedReplacements.put("customer_name", customerName);
        expectedReplacements.put("webapp_url", "https://staging.closelink.net/app/vessel/vesselId-1");
        expectedReplacements.put("vessel_name", vesselName);
        expectedReplacements.put(
            "stock_demand_list",
            "<ul><li>Cat Name 1 - Cylinder Oil (Cyltech 100)</li><li>Cat 2 - Ancillary Oil (Cyltech 70)</li></ul>"
        );

        stockSafetyReserveDemandNotificationCreatedHandler.handle(event);

        Notification expectedNotification = Notification.builder()
            .sendMail(true)
            .subject("stockDemand.safetyReserve.subject")
            .subjectReplacements(new Object[] { vesselName })
            .template("Customer_Safety_Reserve_Alert")
            .mailReplacements(expectedReplacements)
            .receiverType(ReceiverType.CUSTOM)
            .recipients(Collections.singletonList(Recipient.builder().emailAddress("<EMAIL>").build()))
            .receiverId(stockDemandNotificationEventMessage.getCustomerId())
            .senderType(SenderType.SYSTEM)
            .category(NotificationCategory.SAFETY_RESERVE_LEVEL_REMINDER)
            .build();

        ArgumentCaptor<Notification> argumentCaptor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(argumentCaptor.capture());
        Notification actualNotification = argumentCaptor.getValue();

        Assertions.assertThat(actualNotification).usingRecursiveComparison().isEqualTo(expectedNotification);
    }
}
