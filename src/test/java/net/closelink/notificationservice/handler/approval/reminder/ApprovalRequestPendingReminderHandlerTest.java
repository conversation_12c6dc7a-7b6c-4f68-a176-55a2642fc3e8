package net.closelink.notificationservice.handler.approval.reminder;

import static org.mockito.Mockito.verify;

import java.util.Collections;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestPendingReminderEventMessage;
import net.closelink.cenqueue.objects.ApprovalRequest;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.Recipient;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.approval.replacement.ApprovalRequestCreatedReplacementSupportService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.offer.OfferApiRestService;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ApprovalRequestPendingReminderHandlerTest {

    @InjectMocks
    private ApprovalRequestPendingReminderHandler approvalRequestPendingReminderHandler;

    @Mock
    private NotificationService notificationService;

    @Mock
    private ApprovalRequestCreatedReplacementSupportService approvalRequestCreatedReplacementSupportService;

    @Mock
    private OfferApiRestService offerApiRestService;

    @AfterEach
    public void after() {
        Mockito.verifyNoMoreInteractions(notificationService);
        Mockito.verifyNoMoreInteractions(approvalRequestCreatedReplacementSupportService);
    }

    @Test
    public void shouldCallApprovedHandler() {
        var approvalRequestPendingReminderEventMessage = ApprovalRequestPendingReminderEventMessage.builder()
            .approvalRequest(
                ApprovalRequest.builder().offerId("offer-1").controllerEmailAddress("<EMAIL>").build()
            )
            .approverEmailAddress("<EMAIL>")
            .build();
        var event = DataCreator.createEvent(approvalRequestPendingReminderEventMessage);
        var replacements = DataCreator.createStringMap();
        var subjectReplacements = DataCreator.createStringArray();
        var approvalRequest = approvalRequestPendingReminderEventMessage.getApprovalRequest();

        Mockito.when(
            approvalRequestCreatedReplacementSupportService.createReplacements(
                ArgumentMatchers.any(ApprovalRequestPendingReminderEventMessage.class),
                ArgumentMatchers.any(OfferMessage.class)
            )
        ).thenReturn(replacements);

        var offerMessage = OfferMessage.builder().id(approvalRequest.getOfferId()).type("LUBES").build();

        Mockito.when(offerApiRestService.getOffer(approvalRequest.getOfferId())).thenReturn(offerMessage);

        Mockito.when(
            approvalRequestCreatedReplacementSupportService.createSubjectReplacements(offerMessage)
        ).thenReturn(subjectReplacements);

        approvalRequestPendingReminderHandler.handle(event);

        Recipient recipient = Recipient.builder()
            .emailAddress(approvalRequestPendingReminderEventMessage.getApproverEmailAddress())
            .type(Recipient.Type.TO)
            .build();

        Notification notification = Notification.builder()
            .template("Approval_Controller_Reminder")
            .subject("approval.reminder.subject")
            .message("approval.reminder.message")
            .sendMail(true)
            .hidden(true)
            .receiverType(ReceiverType.CUSTOM)
            .recipients(Collections.singletonList(recipient))
            .offerId(approvalRequest.getOfferId())
            .senderId(approvalRequest.getCustomerId())
            .senderType(SenderType.CUSTOMER)
            .mailReplacements(replacements)
            .subjectReplacements(subjectReplacements)
            .build();

        verify(approvalRequestCreatedReplacementSupportService).createSubjectReplacements(offerMessage);
        verify(approvalRequestCreatedReplacementSupportService).createReplacements(
            ArgumentMatchers.any(ApprovalRequestPendingReminderEventMessage.class),
            ArgumentMatchers.any(OfferMessage.class)
        );
        verify(notificationService).notify(notification);
    }
}
