package net.closelink.notificationservice.handler.price;

import static net.closelink.notificationservice.data.DataCreator.createPriceListUploadedEventMessage;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.EventTrigger;
import net.closelink.cenqueue.domainobject.price.PriceListUploadedEventMessage;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.service.notification.NotificationService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class PriceListUploadedHandlerTest {

    private PriceListUploadedHandler handler;
    private NotificationService notificationService;

    @BeforeEach
    public void setUp() {
        this.notificationService = mock(NotificationService.class);
        this.handler = new PriceListUploadedHandler(this.notificationService);
    }

    @AfterEach
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.notificationService);
    }

    @Test
    public void shouldSendNotificationForCustomers() {
        final PriceListUploadedEventMessage eventMessage = createPriceListUploadedEventMessage();
        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));
        event.setEventTrigger(EventTrigger.builder().companyType(UserType.CUSTOMER).build());

        this.handler.handle(event);

        final ArgumentCaptor<Notification> notificationCaptor = ArgumentCaptor.forClass(Notification.class);
        verify(this.notificationService).notify(notificationCaptor.capture());

        final Notification notification = notificationCaptor.getValue();

        Assertions.assertThat(notification.getSenderType()).isEqualTo(SenderType.SYSTEM);
        Assertions.assertThat(notification.getReceiverType()).isEqualTo(ReceiverType.SYSTEM);
        Assertions.assertThat(notification.isSendMail()).isTrue();
        Assertions.assertThat(notification.getMessage()).isEqualTo("system.upload.message");
        Assertions.assertThat(notification.getSubject()).isEqualTo("system.upload.subject");
        Assertions.assertThat(notification.getTemplate()).isEqualTo("system");
        Assertions.assertThat(notification.getMailReplacements()).isNotEmpty();
    }

    @ParameterizedTest
    @EnumSource(value = UserType.class, mode = EnumSource.Mode.EXCLUDE, names = { "CUSTOMER" })
    public void shouldNotSendNotificationExceptForCustomers(UserType userType) {
        final PriceListUploadedEventMessage eventMessage = createPriceListUploadedEventMessage();
        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));
        event.setEventTrigger(EventTrigger.builder().companyType(userType).build());

        this.handler.handle(event);

        verifyNoInteractions(this.notificationService);
    }
}
