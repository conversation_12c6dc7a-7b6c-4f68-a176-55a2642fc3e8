package net.closelink.notificationservice.handler.passwordreset;

import java.util.HashMap;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.passwordreset.PasswordResetRequestCreatedMessage;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.*;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.user.UserApiRestService;
import net.closelink.notificationservice.service.rest.user.domain.UserMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class PasswordResetRequestCreatedHandlerTest {

    private UserApiRestService userApiRestService = Mockito.mock(UserApiRestService.class);
    private NotificationService notificationService = Mockito.mock(NotificationService.class);
    private PasswordResetRequestCreatedHandler passwordResetRequestCreatedHandler;

    @BeforeEach
    public void setUp() {
        this.passwordResetRequestCreatedHandler = new PasswordResetRequestCreatedHandler(
            userApiRestService,
            notificationService
        );
    }

    @Test
    public void shouldNotify() {
        PasswordResetRequestCreatedMessage passwordResetRequestCreatedMessage =
            PasswordResetRequestCreatedMessage.builder()
                .userId("123")
                .passwordResetLink("https://closelink.net")
                .build();

        Event event = DataCreator.createEvent(passwordResetRequestCreatedMessage);

        Mockito.when(userApiRestService.getUser("123")).thenReturn(
            UserMessage.builder().firstname("first").lastname("last").id("123").build()
        );

        passwordResetRequestCreatedHandler.handle(event);

        HashMap<String, String> mailReplacements = new HashMap<>();
        mailReplacements.put("user_first_name", "first");
        mailReplacements.put("user_last_name", "last");
        mailReplacements.put("password_reset_link", "https://closelink.net");

        Mockito.verify(notificationService).notify(
            Notification.builder()
                .receiverType(ReceiverType.USER)
                .receiverId("123")
                .senderType(SenderType.SYSTEM)
                .sendMail(true)
                .hidden(true)
                .subject("passwordResetRequest.created.subject")
                .template("Password_Change_Request_User")
                .mailReplacements(mailReplacements)
                .build()
        );
    }
}
