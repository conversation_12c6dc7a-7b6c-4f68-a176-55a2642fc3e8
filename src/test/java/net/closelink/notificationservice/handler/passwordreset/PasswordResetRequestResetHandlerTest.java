package net.closelink.notificationservice.handler.passwordreset;

import java.util.HashMap;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.passwordreset.PasswordResetRequestResetMessage;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.user.UserApiRestService;
import net.closelink.notificationservice.service.rest.user.domain.UserMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class PasswordResetRequestResetHandlerTest {

    private UserApiRestService userApiRestService = Mockito.mock(UserApiRestService.class);
    private NotificationService notificationService = Mockito.mock(NotificationService.class);
    private PasswordResetRequestResetHandler passwordResetRequestResetHandler;

    @BeforeEach
    public void setUp() {
        this.passwordResetRequestResetHandler = new PasswordResetRequestResetHandler(
            userApiRestService,
            notificationService
        );
    }

    @Test
    public void shouldNotify() {
        PasswordResetRequestResetMessage passwordResetRequestCreatedMessage = PasswordResetRequestResetMessage.builder()
            .userId("123")
            .build();

        Event event = DataCreator.createEvent(passwordResetRequestCreatedMessage);

        Mockito.when(userApiRestService.getUser("123")).thenReturn(
            UserMessage.builder().firstname("first").lastname("last").id("123").build()
        );

        passwordResetRequestResetHandler.handle(event);

        HashMap<String, String> mailReplacements = new HashMap<>();
        mailReplacements.put("user_first_name", "first");
        mailReplacements.put("user_last_name", "last");

        Mockito.verify(notificationService).notify(
            Notification.builder()
                .receiverType(ReceiverType.USER)
                .receiverId("123")
                .senderType(SenderType.SYSTEM)
                .sendMail(true)
                .hidden(true)
                .subject("passwordResetRequest.reset.subject")
                .template("Password_Changed_User")
                .mailReplacements(mailReplacements)
                .build()
        );
    }
}
