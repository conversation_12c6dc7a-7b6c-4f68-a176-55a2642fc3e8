package net.closelink.notificationservice.handler.vesselrequisition;

import java.util.Collections;
import java.util.HashMap;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.vesselrequisition.VesselRequisitionCreatedEventMessage;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Attachment;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.Recipient;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class VesselRequisitionCreatedHandlerTest {

    private final VesselApiRestService vesselApiRestService = Mockito.mock(VesselApiRestService.class);
    private final CustomerApiRestService customerApiRestService = Mockito.mock(CustomerApiRestService.class);
    private final NotificationService notificationService = Mockito.mock(NotificationService.class);
    private VesselRequisitionCreatedHandler vesselRequisitionCreatedHandler;

    @BeforeEach
    public void setUp() {
        this.vesselRequisitionCreatedHandler = new VesselRequisitionCreatedHandler(
            vesselApiRestService,
            customerApiRestService,
            notificationService
        );
    }

    @Test
    public void shouldDoNothingIfEmailFlagIsNotEnabled() {
        VesselRequisitionCreatedEventMessage vesselRequisitionCreatedEventMessage =
            VesselRequisitionCreatedEventMessage.builder().sendEmail(false).build();

        Event event = DataCreator.createEvent(vesselRequisitionCreatedEventMessage);

        vesselRequisitionCreatedHandler.handle(event);

        Mockito.verifyNoInteractions(vesselApiRestService);
        Mockito.verifyNoInteractions(customerApiRestService);
        Mockito.verifyNoInteractions(notificationService);
    }

    @Test
    public void shouldNotify() {
        VesselRequisitionCreatedEventMessage vesselRequisitionCreatedEventMessage =
            VesselRequisitionCreatedEventMessage.builder()
                .sendEmail(true)
                .vesselId("123")
                .fileName("Vessel Requisition.xlsx")
                .fileUrl("https://closelink.net")
                .build();

        Event event = DataCreator.createEvent(vesselRequisitionCreatedEventMessage);

        Mockito.when(vesselApiRestService.getVessel("123")).thenReturn(
            VesselMessage.builder().customerId("456").email("<EMAIL>").name("MS Classica").build()
        );

        Mockito.when(customerApiRestService.getCustomer("456")).thenReturn(
            CustomerMessage.builder().name("Customer Name").id("456").build()
        );

        vesselRequisitionCreatedHandler.handle(event);

        HashMap<String, String> mailReplacements = new HashMap<>();
        mailReplacements.put("customer_name", "Customer Name");
        mailReplacements.put("vessel_name", "MS Classica");

        Mockito.verify(notificationService).notify(
            Notification.builder()
                .receiverType(ReceiverType.CUSTOM)
                .senderType(SenderType.CUSTOMER)
                .senderId("456")
                .sendMail(true)
                .hidden(true)
                .subject("vesselRequisition.created.subject")
                .subjectReplacements(new Object[] { "Customer Name" })
                .template("VesselRequisitionCreated")
                .mailReplacements(mailReplacements)
                .recipients(
                    Collections.singletonList(
                        Recipient.builder().emailAddress("<EMAIL>").name("MS Classica").build()
                    )
                )
                .attachments(
                    Collections.singletonList(
                        Attachment.builder().url("https://closelink.net").name("Vessel Requisition.xlsx").build()
                    )
                )
                .build()
        );
    }
}
