package net.closelink.notificationservice.handler.vesselrequisition;

import java.util.Collections;
import java.util.HashMap;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.vesselrequisition.VesselRequisitionImportedEventMessage;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.*;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class VesselRequisitionImportedHandlerTest {

    private final VesselApiRestService vesselApiRestService = Mockito.mock(VesselApiRestService.class);
    private final NotificationService notificationService = Mockito.mock(NotificationService.class);
    private VesselRequisitionImportedHandler vesselRequisitionImportedHandler;

    @BeforeEach
    public void setUp() {
        this.vesselRequisitionImportedHandler = new VesselRequisitionImportedHandler(
            vesselApiRestService,
            notificationService
        );
    }

    @Test
    public void shouldDoNothingIfEmailNotAvailable() {
        VesselRequisitionImportedEventMessage vesselRequisitionImportedEventMessage =
            VesselRequisitionImportedEventMessage.builder().vesselId("123").orderId("4345").build();

        Event event = DataCreator.createEvent(vesselRequisitionImportedEventMessage);

        Mockito.when(vesselApiRestService.getVessel("123")).thenReturn(
            VesselMessage.builder().customerId("0283").email(null).name("MS Classica").build()
        );

        vesselRequisitionImportedHandler.handle(event);

        Mockito.verify(vesselApiRestService).getVessel("123");
        Mockito.verifyNoInteractions(notificationService);
    }

    @Test
    public void shouldNotify() {
        VesselRequisitionImportedEventMessage vesselRequisitionImportedEventMessage =
            VesselRequisitionImportedEventMessage.builder().vesselId("123").orderId("4345").build();

        Event event = DataCreator.createEvent(vesselRequisitionImportedEventMessage);

        Mockito.when(vesselApiRestService.getVessel("123")).thenReturn(
            VesselMessage.builder().customerId("0283").email("<EMAIL>").name("MS Classica").build()
        );

        vesselRequisitionImportedHandler.handle(event);

        HashMap<String, String> mailReplacements = new HashMap<>();
        mailReplacements.put("vessel_name", "MS Classica");

        Mockito.verify(notificationService).notify(
            Notification.builder()
                .receiverType(ReceiverType.CUSTOM)
                .senderType(SenderType.CUSTOMER)
                .senderId("0283")
                .sendMail(true)
                .hidden(true)
                .subject("vesselRequisition.imported.subject")
                .template("VesselRequisitionImported")
                .mailReplacements(mailReplacements)
                .recipients(
                    Collections.singletonList(
                        Recipient.builder().emailAddress("<EMAIL>").name("MS Classica").build()
                    )
                )
                .build()
        );
    }
}
