package net.closelink.notificationservice.handler.user;

import static net.closelink.notificationservice.data.DataCreator.createUserInvitedEventMessage;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.user.UserInvitedEventMessage;
import net.closelink.cenqueue.objects.User;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.exception.NoHandlerFoundException;
import net.closelink.notificationservice.service.notification.NotificationService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.Mockito;

class UserInvitedHandlerTest {

    private UserInvitedHandler handler;
    private NotificationService notificationService;
    private AdminUserInvitedNotificationCreator adminUserInvitedNotificationCreator;
    private GeneralUserInvitedNotificationCreator generalUserInvitedNotificationCreator;

    @BeforeEach
    void setUp() {
        this.notificationService = mock(NotificationService.class);
        this.adminUserInvitedNotificationCreator = mock(AdminUserInvitedNotificationCreator.class);
        this.generalUserInvitedNotificationCreator = mock(GeneralUserInvitedNotificationCreator.class);
        this.handler = new UserInvitedHandler(
            generalUserInvitedNotificationCreator,
            adminUserInvitedNotificationCreator,
            this.notificationService
        );
    }

    @AfterEach
    void afterTest() {
        Mockito.verifyNoMoreInteractions(this.notificationService);
    }

    @Test
    void shouldThrowForSystemUserType() {
        final User user = DataCreator.createUser(UserType.CUSTOMER);

        final UserInvitedEventMessage eventMessage = createUserInvitedEventMessage(user);
        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));
        event.getEventTrigger().setCompanyType(UserType.SYSTEM);

        Assertions.assertThatThrownBy(() -> this.handler.handle(event)).isInstanceOf(NoHandlerFoundException.class);
    }

    @Test
    void shouldUseAdminUserInvitedNotificationCreator() {
        final User user = DataCreator.createUser(UserType.CUSTOMER);

        final UserInvitedEventMessage eventMessage = createUserInvitedEventMessage(user);
        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));
        event.getEventTrigger().setCompanyType(UserType.ADMIN);

        Notification notification = Notification.builder()
            .receiverType(ReceiverType.USER)
            .receiverId(user.getId())
            .sendMail(true)
            .message("message")
            .subject("subject")
            .template("template")
            .senderId(user.getGroupId())
            .senderType(SenderType.SYSTEM)
            .build();

        Mockito.when(adminUserInvitedNotificationCreator.supports(UserType.ADMIN)).thenReturn(true);
        Mockito.when(adminUserInvitedNotificationCreator.createNotification(any())).thenReturn(notification);

        this.handler.handle(event);

        verify(generalUserInvitedNotificationCreator).supports(UserType.ADMIN);
        verifyNoMoreInteractions(generalUserInvitedNotificationCreator);

        verify(adminUserInvitedNotificationCreator).createNotification(any());

        verify(this.notificationService).notify(notification);
    }

    @ParameterizedTest
    @EnumSource(value = UserType.class, names = { "SUPPLIER", "CUSTOMER" }, mode = EnumSource.Mode.INCLUDE)
    void shouldUseGeneralUserInvitedNotificationCreator(UserType userType) {
        final User user = DataCreator.createUser(UserType.CUSTOMER);

        final UserInvitedEventMessage eventMessage = createUserInvitedEventMessage(user);
        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));
        event.getEventTrigger().setCompanyType(userType);

        Notification notification = Notification.builder()
            .receiverType(ReceiverType.USER)
            .receiverId(user.getId())
            .sendMail(true)
            .message("message")
            .subject("subject")
            .template("template")
            .senderId(user.getGroupId())
            .senderType(SenderType.SYSTEM)
            .build();

        Mockito.when(generalUserInvitedNotificationCreator.supports(userType)).thenReturn(true);
        Mockito.when(generalUserInvitedNotificationCreator.createNotification(any())).thenReturn(notification);

        this.handler.handle(event);

        verifyNoInteractions(adminUserInvitedNotificationCreator);

        verify(generalUserInvitedNotificationCreator).createNotification(any());

        verify(this.notificationService).notify(notification);
    }
}
