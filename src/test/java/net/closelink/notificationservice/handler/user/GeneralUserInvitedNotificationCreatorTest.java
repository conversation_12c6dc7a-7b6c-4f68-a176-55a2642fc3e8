package net.closelink.notificationservice.handler.user;

import static net.closelink.notificationservice.data.DataCreator.createUserInvitedEventMessage;

import java.util.HashMap;
import net.closelink.cenqueue.domainobject.user.UserInvitedEventMessage;
import net.closelink.cenqueue.objects.User;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

class GeneralUserInvitedNotificationCreatorTest {

    private GeneralUserInvitedNotificationCreator generalUserInvitedNotificationCreator =
        new GeneralUserInvitedNotificationCreator();

    @Test
    void shouldCreateNotificationForSupplier() {
        String userId = "123-567";
        String groupId = "987-123";
        String firstName = "Steve";
        String lastName = "Jobs";

        User user = DataCreator.createUser(UserType.SUPPLIER);
        user.setId(userId);
        user.setGroupId(groupId);
        user.setFirstname(firstName);
        user.setLastname(lastName);

        final UserInvitedEventMessage eventMessage = createUserInvitedEventMessage(user);

        String onboardingLink = "https://hsv.de";
        eventMessage.setOnboardingLink(onboardingLink);

        Notification notification = generalUserInvitedNotificationCreator.createNotification(eventMessage);

        Assertions.assertThat(notification.getReceiverType()).isEqualTo(ReceiverType.USER);
        Assertions.assertThat(notification.getReceiverId()).isEqualTo(userId);
        Assertions.assertThat(notification.isSendMail()).isTrue();
        Assertions.assertThat(notification.getMessage()).isEqualTo("user.onboarding.message");
        Assertions.assertThat(notification.getSubject()).isEqualTo("user.onboarding.subject");
        Assertions.assertThat(notification.getTemplate()).isEqualTo("User_Onboarding");
        Assertions.assertThat(notification.getSenderId()).isEqualTo(groupId);
        Assertions.assertThat(notification.getSenderType()).isEqualTo(SenderType.SUPPLIER_GROUP);

        HashMap<String, String> expectedMailReplacements = new HashMap<>();
        expectedMailReplacements.put("onboarding_link", onboardingLink);

        Assertions.assertThat(notification.getMailReplacements()).isEqualTo(expectedMailReplacements);
    }

    @Test
    void shouldCreateNotificationForCustomer() {
        String userId = "123-567";
        String groupId = "987-123";
        String firstName = "Steve";
        String lastName = "Jobs";

        User user = DataCreator.createUser(UserType.CUSTOMER);
        user.setId(userId);
        user.setGroupId(groupId);
        user.setFirstname(firstName);
        user.setLastname(lastName);

        final UserInvitedEventMessage eventMessage = createUserInvitedEventMessage(user);

        String onboardingLink = "https://hsv.de";
        eventMessage.setOnboardingLink(onboardingLink);

        Notification notification = generalUserInvitedNotificationCreator.createNotification(eventMessage);

        Assertions.assertThat(notification.getReceiverType()).isEqualTo(ReceiverType.USER);
        Assertions.assertThat(notification.getReceiverId()).isEqualTo(userId);
        Assertions.assertThat(notification.isSendMail()).isTrue();
        Assertions.assertThat(notification.getMessage()).isEqualTo("user.onboarding.message");
        Assertions.assertThat(notification.getSubject()).isEqualTo("user.onboarding.subject");
        Assertions.assertThat(notification.getTemplate()).isEqualTo("User_Onboarding");
        Assertions.assertThat(notification.getSenderId()).isEqualTo(groupId);
        Assertions.assertThat(notification.getSenderType()).isEqualTo(SenderType.CUSTOMER_GROUP);

        HashMap<String, String> expectedMailReplacements = new HashMap<>();
        expectedMailReplacements.put("onboarding_link", onboardingLink);

        Assertions.assertThat(notification.getMailReplacements()).isEqualTo(expectedMailReplacements);
    }

    @ParameterizedTest
    @EnumSource(value = UserType.class, names = { "SUPPLIER", "CUSTOMER" }, mode = EnumSource.Mode.INCLUDE)
    void shouldSupportSpecificTypes(UserType userType) {
        Assertions.assertThat(generalUserInvitedNotificationCreator.supports(userType)).isTrue();
    }

    @ParameterizedTest
    @EnumSource(value = UserType.class, names = { "ADMIN", "SYSTEM" }, mode = EnumSource.Mode.INCLUDE)
    void shouldNotSupportSpecificTypes(UserType userType) {
        Assertions.assertThat(generalUserInvitedNotificationCreator.supports(userType)).isFalse();
    }
}
