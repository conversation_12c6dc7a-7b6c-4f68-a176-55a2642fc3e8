package net.closelink.notificationservice.handler.offer.reminder;

import static net.closelink.notificationservice.data.DataCreator.createOrderDeliveryReminderEventMessage;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import java.util.List;
import java.util.Map;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.offer.OfferDeliveryReminderEventMessage;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.replacement.offer.OfferReplacementService;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementOffer;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.MailNotificationSettings;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

public class OfferDeliveryReminderHandlerTest {

    private OfferDeliveryReminderHandler handler;
    private NotificationService notificationService;
    private VesselApiRestService vesselApiRestService;
    private OfferReplacementService orderReplacementService;
    private WebAppReplacementService webappReplacementService;
    private Offer offer;
    private Map<String, String> replacements;

    @Before
    public void setUp() {
        this.notificationService = Mockito.mock(NotificationService.class);
        this.vesselApiRestService = Mockito.mock(VesselApiRestService.class);
        this.orderReplacementService = Mockito.mock(OfferReplacementService.class);
        this.webappReplacementService = Mockito.mock(WebAppReplacementService.class);
        this.handler = new OfferDeliveryReminderHandler(
            this.notificationService,
            this.vesselApiRestService,
            this.orderReplacementService,
            this.webappReplacementService
        );

        this.offer = DataCreator.createOffer();
        this.replacements = DataCreator.createStringMap();
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.notificationService);
        Mockito.verifyNoMoreInteractions(this.vesselApiRestService);
        Mockito.verifyNoMoreInteractions(this.orderReplacementService);
        Mockito.verifyNoMoreInteractions(this.webappReplacementService);
    }

    @Test
    public void handle() {
        var vessel = VesselMessage.builder()
            .name("vessel-1")
            .mailNotificationSettings(
                MailNotificationSettings.builder()
                    .orderUpdateSettings(
                        MailNotificationSettings.NotificationSettings.builder()
                            .enabled(true)
                            .sendToOthers(true)
                            .recipients(List.of("<EMAIL>"))
                            .build()
                    )
                    .build()
            )
            .build();
        Mockito.when(this.orderReplacementService.create(ArgumentMatchers.any(ReplacementOffer.class))).thenReturn(
            this.replacements
        );
        Mockito.when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(vessel);

        final OfferDeliveryReminderEventMessage eventMessage = createOrderDeliveryReminderEventMessage(this.offer);
        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));

        this.handler.handle(event);

        verifyNotification(vessel);
        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.orderReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(any(ReplacementOffer.class), eq(ReceiverType.CUSTOMER));
    }

    @Test
    public void shouldNotDoAnythingForOrderTypeFuel() {
        this.offer.setType(OrderType.FUEL);
        final OfferDeliveryReminderEventMessage eventMessage = createOrderDeliveryReminderEventMessage(this.offer);
        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));

        this.handler.handle(event);
        verifyNoInteractions(this.vesselApiRestService);
        verifyNoInteractions(this.orderReplacementService);
        verifyNoInteractions(this.webappReplacementService);
    }

    private void verifyNotification(VesselMessage vessel) {
        final ArgumentCaptor<Notification> notificationCaptor = ArgumentCaptor.forClass(Notification.class);
        verify(this.notificationService).notify(notificationCaptor.capture());

        final Notification notification = notificationCaptor.getValue();

        assertEquals(ReceiverType.CUSTOM, notification.getReceiverType());
        assertFalse(notification.getRecipients().isEmpty());
        assertEquals(this.offer.getCustomerId(), notification.getReceiverId());
        assertTrue(notification.isSendMail());
        assertTrue(notification.isHidden());
        assertEquals(SenderType.SYSTEM, notification.getSenderType());
        assertNotNull(notification.getMessage());
        assertNotNull(notification.getSubject());
        assertNotNull(notification.getTemplate());
        assertNotNull(notification.getMailReplacements());
        assertEquals(this.replacements, notification.getMailReplacements());
        assertEquals(vessel.getName(), notification.getSubjectReplacements()[0]);
        assertEquals(this.offer.getOfferNumber(), notification.getSubjectReplacements()[1]);
    }
}
