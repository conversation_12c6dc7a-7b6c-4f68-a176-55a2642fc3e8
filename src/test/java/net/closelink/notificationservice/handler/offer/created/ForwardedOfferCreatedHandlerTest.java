package net.closelink.notificationservice.handler.offer.created;

import static net.closelink.notificationservice.data.DataCreator.createEvent;
import static net.closelink.notificationservice.data.DataCreator.createOffer;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.offer.OfferUpdatedEventMessage;
import net.closelink.cenqueue.domainvalue.offer.OfferUpdatedEventType;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Attachment;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.replacement.subject.supplier.SupplierSubjectReplacementService;
import net.closelink.notificationservice.service.attachment.ForwardedEnquiryAttachmentCreator;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

class ForwardedOfferCreatedHandlerTest {

    private final CustomerStateHandlerSupport customerStateHandlerSupport = Mockito.mock(
        CustomerStateHandlerSupport.class
    );

    private final SupplierSubjectReplacementService subjectReplacementService = Mockito.mock(
        SupplierSubjectReplacementService.class
    );

    private final ForwardedEnquiryAttachmentCreator forwardedOrderQuoteNotificationCreator = Mockito.mock(
        ForwardedEnquiryAttachmentCreator.class
    );

    private ForwardedOfferCreatedHandler handler;

    @BeforeEach
    void setUp() {
        this.handler = new ForwardedOfferCreatedHandler(
            customerStateHandlerSupport,
            subjectReplacementService,
            forwardedOrderQuoteNotificationCreator
        );
    }

    @Test
    void shouldNotifyWithoutBuyerRef() {
        OfferUpdatedEvent offerUpdatedEvent = createOfferUpdatedEvent();
        offerUpdatedEvent.getEventMessage().getOffer().setBuyerReference(null);
        Offer offer = offerUpdatedEvent.getEventMessage().getOffer();

        Notification.NotificationBuilder notificationBuilder = DataCreator.createNotificationBuilder();

        Mockito.when(customerStateHandlerSupport.createNotificationBuilder(offerUpdatedEvent)).thenReturn(
            notificationBuilder
        );

        Object[] subjectReplacements = { "MS Classica", "Closelink", "CL123" };
        Mockito.when(subjectReplacementService.createSubjectReplacements(offer)).thenReturn(subjectReplacements);

        var attachment = Attachment.builder().name("Attachment").url("https://hsv.de").build();
        var offerAttachments = offer.getFileIds().stream().map(fileId -> Attachment.builder().fileId(fileId).build());
        var combinedAttachments = Stream.concat(Stream.of(attachment), offerAttachments).collect(Collectors.toList());

        Mockito.when(forwardedOrderQuoteNotificationCreator.createAttachmentForOffer(offer)).thenReturn(
            combinedAttachments
        );

        handler.handle(offerUpdatedEvent);

        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(customerStateHandlerSupport).notify(captor.capture());

        Notification actualNotification = captor.getValue();
        Assertions.assertThat(actualNotification.getMessage()).isEqualTo("draftEnquiry.message");
        Assertions.assertThat(actualNotification.getSubject()).isEqualTo("forwarded.newEnquiry.subject");
        Assertions.assertThat(actualNotification.getSubjectReplacements()).isEqualTo(subjectReplacements);
        Assertions.assertThat(actualNotification.getTemplate()).isEqualTo("Supplier_New_Enquiry_Forwarded");
        Assertions.assertThat(actualNotification.getAttachments()).isEqualTo(combinedAttachments);
    }

    @Test
    void shouldNotifyWithBuyerRef() {
        OfferUpdatedEvent offerUpdatedEvent = createOfferUpdatedEvent();
        offerUpdatedEvent.getEventMessage().getOffer().setBuyerReference("Test 123");
        Offer offer = offerUpdatedEvent.getEventMessage().getOffer();

        Notification.NotificationBuilder notificationBuilder = DataCreator.createNotificationBuilder();

        Mockito.when(customerStateHandlerSupport.createNotificationBuilder(offerUpdatedEvent)).thenReturn(
            notificationBuilder
        );

        Object[] subjectReplacements = { "MS Classica", "Closelink", "CL123" };
        Mockito.when(subjectReplacementService.createSubjectReplacements(offer)).thenReturn(subjectReplacements);

        var attachment = Attachment.builder().name("Attachment").url("https://hsv.de").build();
        var offerAttachments = offer.getFileIds().stream().map(fileId -> Attachment.builder().fileId(fileId).build());
        var combinedAttachments = Stream.concat(Stream.of(attachment), offerAttachments).collect(Collectors.toList());

        Mockito.when(forwardedOrderQuoteNotificationCreator.createAttachmentForOffer(offer)).thenReturn(
            combinedAttachments
        );

        handler.handle(offerUpdatedEvent);

        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(customerStateHandlerSupport).notify(captor.capture());

        Notification actualNotification = captor.getValue();
        Assertions.assertThat(actualNotification.getMessage()).isEqualTo("draftEnquiry.message");
        Assertions.assertThat(actualNotification.getSubject()).isEqualTo("forwarded.newEnquiry.subjectBuyerRef");
        Assertions.assertThat(actualNotification.getSubjectReplacements()).isEqualTo(subjectReplacements);
        Assertions.assertThat(actualNotification.getTemplate()).isEqualTo("Supplier_New_Enquiry_Forwarded");
        Assertions.assertThat(actualNotification.getAttachments()).isEqualTo(combinedAttachments);
    }

    private OfferUpdatedEvent createOfferUpdatedEvent() {
        OfferUpdatedEventMessage updateEvent = DataCreator.createOfferUpdatedEventMessage(
            OfferUpdatedEventType.STATE_CHANGED
        );
        Offer offer = createOffer(EnquiryType.FORWARDED);
        LocalDateTime enquiredDate = LocalDateTime.of(2018, 11, 3, 13, 37);

        offer.setDateEnquired(enquiredDate.toInstant(ZoneOffset.UTC).toEpochMilli());

        updateEvent.setOffer(offer);

        Event event = createEvent(updateEvent);

        return OfferUpdatedEvent.builder().event(event).eventMessage(updateEvent).build();
    }
}
