package net.closelink.notificationservice.handler.offer.updated.state.common.invoiced;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import net.closelink.cenqueue.domainobject.offer.OfferUpdatedEventMessage;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class GeneralOrderInvoicedHandlerTest {

    private static final String CUSTOMER_MESSAGE = "customerInvoiced.message";
    private static final String CUSTOMER_SUBJECT = "customerInvoiced.subject";
    private static final String CUSTOMER_SUBJECT_BUYER_REF = "customerInvoicedBuyerRef.subject";
    private static final String CUSTOMER_TEMPLATE = "Order_Customer_Order_Invoiced";

    @Mock
    private SupplierStateHandlerSupport supplierStateHandlerSupport;

    @InjectMocks
    private GeneralOrderInvoicedHandler handler;

    @Test
    public void handleTestWithoutBuyerRef() {
        var offer = Offer.builder().build();
        var event = OfferUpdatedEvent.builder()
            .eventMessage(OfferUpdatedEventMessage.builder().offer(offer).build())
            .build();
        when(supplierStateHandlerSupport.createNotificationBuilder(event)).thenReturn(Notification.builder());

        var resultEvent = handler.handle(any(), event);

        var supplierStateHandlerCaptor = ArgumentCaptor.forClass(Notification.class);

        assertEquals(resultEvent, event);
        verify(supplierStateHandlerSupport).notify(supplierStateHandlerCaptor.capture());
        var customerNotification = supplierStateHandlerCaptor.getValue();

        assertEquals(CUSTOMER_MESSAGE, customerNotification.getMessage());
        assertEquals(CUSTOMER_TEMPLATE, customerNotification.getTemplate());
        assertEquals(CUSTOMER_SUBJECT, customerNotification.getSubject());
    }

    @Test
    public void handleTestWithBuyerRef() {
        var offer = Offer.builder().buyerReference("buyerReference").build();
        var event = OfferUpdatedEvent.builder()
            .eventMessage(OfferUpdatedEventMessage.builder().offer(offer).build())
            .build();
        when(supplierStateHandlerSupport.createNotificationBuilder(event)).thenReturn(Notification.builder());

        var resultEvent = handler.handle(any(), event);

        var supplierStateHandlerCaptor = ArgumentCaptor.forClass(Notification.class);

        assertEquals(resultEvent, event);
        verify(supplierStateHandlerSupport).notify(supplierStateHandlerCaptor.capture());
        var customerNotification = supplierStateHandlerCaptor.getValue();

        assertEquals(CUSTOMER_MESSAGE, customerNotification.getMessage());
        assertEquals(CUSTOMER_TEMPLATE, customerNotification.getTemplate());
        assertEquals(CUSTOMER_SUBJECT_BUYER_REF, customerNotification.getSubject());
    }
}
