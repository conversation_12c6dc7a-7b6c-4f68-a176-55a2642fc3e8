package net.closelink.notificationservice.handler.offer.updated.fields.handler

import io.kotest.core.spec.style.DescribeSpec
import io.mockk.clearAllMocks
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import net.closelink.cenqueue.domainobject.Event
import net.closelink.cenqueue.domainobject.EventTrigger
import net.closelink.cenqueue.domainobject.offer.OfferUpdatedEventMessage
import net.closelink.cenqueue.objects.Offer
import net.closelink.cenqueue.types.EnquiryType
import net.closelink.cenqueue.types.OfferState
import net.closelink.cenqueue.types.UserType
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent

class BuyerRefChangedHandlerTest :
    DescribeSpec({
        val baseActivityLogServiceMock = mockk<BaseActivityLogService>()
        val testee = BuyerRefChangedHandler(baseActivityLogServiceMock)

        describe("BuyerRefChangedHandlerTests") {
            beforeEach { clearAllMocks() }
            describe("Happy Paths") {
                it("just works") {
                    // Given
                    val offer =
                        Offer.builder()
                            .id("id1")
                            .buyerReference("buyer ref 1")
                            .enquiryType(EnquiryType.FORWARDED)
                            .state(OfferState.ENQUIRY)
                            .build()
                    val event =
                        Event.builder()
                            .eventTrigger(
                                EventTrigger.builder().companyType(UserType.CUSTOMER).build()
                            )
                            .build()
                    val eventMessage =
                        OfferUpdatedEventMessage.builder().stateChanged(true).offer(offer).build()

                    val offerUpdatedEvent =
                        OfferUpdatedEvent.builder().event(event).eventMessage(eventMessage).build()

                    justRun {
                        baseActivityLogServiceMock.createActivityLog(
                            offerUpdatedEvent,
                            any(),
                            any(),
                        )
                    }

                    // When
                    testee.handle(offerUpdatedEvent)

                    // Then
                    verify {
                        baseActivityLogServiceMock.createActivityLog(
                            offerUpdatedEvent,
                            any(),
                            any(),
                        )
                    }
                }
            }
        }
    })
