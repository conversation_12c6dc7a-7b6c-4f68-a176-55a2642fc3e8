package net.closelink.notificationservice.handler.offer.updated.state.customer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.*;

import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.EventTrigger;
import net.closelink.cenqueue.domainobject.offer.OfferUpdatedEventMessage;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.replacement.offer.OfferReplacementService;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import net.closelink.notificationservice.support.RecipientsSupportService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

@ExtendWith(MockitoExtension.class)
class DefaultCustomerStateHandlerSupportTest {

    private DefaultCustomerStateHandlerSupport defaultCustomerStateHandlerSupport;

    private VesselApiRestService vesselApiRestService;

    private CustomerApiRestService customerApiRestService;

    private SupplierApiRestService supplierApiRestService;

    @BeforeEach
    public void init() {
        this.vesselApiRestService = mock();
        this.customerApiRestService = mock();
        this.supplierApiRestService = mock();
        this.defaultCustomerStateHandlerSupport = new DefaultCustomerStateHandlerSupport(
            mock(),
            vesselApiRestService,
            customerApiRestService,
            mock(),
            mock(),
            mock(),
            new RecipientsSupportService(supplierApiRestService, customerApiRestService)
        );
    }

    @Test
    public void shouldUseSupplierReceiver() {
        when(vesselApiRestService.getVessel("vessel-1")).thenReturn(VesselMessage.builder().build());
        when(customerApiRestService.getCustomer("customer-1")).thenReturn(CustomerMessage.builder().build());
        var offer = Offer.builder()
            .customerId("customer-1")
            .vesselId("vessel-1")
            .enquiryType(EnquiryType.ASSIGNED)
            .build();
        var notification = defaultCustomerStateHandlerSupport
            .createNotificationBuilder(
                OfferUpdatedEvent.builder()
                    .event(
                        Event.builder()
                            .eventTrigger(EventTrigger.builder().companyType(UserType.CUSTOMER).build())
                            .build()
                    )
                    .eventMessage(OfferUpdatedEventMessage.builder().offer(offer).build())
                    .build()
            )
            .build();

        assertEquals(ReceiverType.SUPPLIER, notification.getReceiverType());
        assertNull(notification.getRecipients());
    }

    @Test
    public void shouldSetSupplierAndCustomerReceiversForForwarded() {
        when(vesselApiRestService.getVessel("vessel-1")).thenReturn(VesselMessage.builder().build());
        String supplierMail = "<EMAIL>";
        String supplierName = "Super Supplier";
        when(supplierApiRestService.getSupplier("supplier-1")).thenReturn(
            SupplierMessage.builder().name(supplierName).email(supplierMail).build()
        );
        String customerMail = "<EMAIL>";
        String customerName = "Super Customer";
        when(customerApiRestService.getCustomer("customer-1")).thenReturn(
            CustomerMessage.builder().name(customerName).email(customerMail).type("LUBES").build()
        );
        var offer = Offer.builder()
            .customerId("customer-1")
            .supplierId("supplier-1")
            .vesselId("vessel-1")
            .enquiryType(EnquiryType.FORWARDED)
            .build();
        var notification = defaultCustomerStateHandlerSupport
            .createNotificationBuilder(
                OfferUpdatedEvent.builder()
                    .event(
                        Event.builder()
                            .eventTrigger(EventTrigger.builder().companyType(UserType.CUSTOMER).build())
                            .build()
                    )
                    .eventMessage(OfferUpdatedEventMessage.builder().offer(offer).build())
                    .build()
            )
            .build();

        assertEquals(ReceiverType.CUSTOM, notification.getReceiverType());
        assertEquals(notification.getRecipients().size(), 2);
        assertEquals(notification.getRecipients().get(0).getName(), supplierName);
        assertEquals(notification.getRecipients().get(0).getEmailAddress(), supplierMail);
        assertEquals(notification.getRecipients().get(1).getName(), customerName);
        assertEquals(notification.getRecipients().get(1).getEmailAddress(), customerMail);
    }
}
