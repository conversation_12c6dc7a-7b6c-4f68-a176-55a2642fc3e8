package net.closelink.notificationservice.handler.offer.created;

import static net.closelink.notificationservice.data.DataCreator.createEvent;
import static net.closelink.notificationservice.data.DataCreator.createOffer;

import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.offer.OfferUpdatedEventMessage;
import net.closelink.cenqueue.domainvalue.offer.OfferUpdatedEventType;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class OfferCreatedHandlerTest {

    private final SpotOfferCreatedHandler spotOfferCreatedHandler = Mockito.mock(SpotOfferCreatedHandler.class);

    private final AssignedOfferCreatedHandler assignedOfferCreatedHandler = Mockito.mock(
        AssignedOfferCreatedHandler.class
    );

    private final ForwardedOfferCreatedHandler forwardedOfferCreatedHandler = Mockito.mock(
        ForwardedOfferCreatedHandler.class
    );

    private OfferCreatedHandler offerCreatedHandler;

    @BeforeEach
    public void setUp() {
        offerCreatedHandler = new OfferCreatedHandler(
            spotOfferCreatedHandler,
            assignedOfferCreatedHandler,
            forwardedOfferCreatedHandler
        );
    }

    @Test
    public void shouldUseSpotOfferCreatedHandler() {
        OfferUpdatedEventMessage updateEvent = DataCreator.createOfferUpdatedEventMessage(
            OfferUpdatedEventType.STATE_CHANGED
        );
        Offer offer = createOffer(EnquiryType.SPOT);
        updateEvent.setOffer(offer);

        Event event = createEvent(updateEvent);

        offerCreatedHandler.handle(event);

        Mockito.verify(spotOfferCreatedHandler).handle(Mockito.any(OfferUpdatedEvent.class));
        Mockito.verifyNoInteractions(assignedOfferCreatedHandler);
        Mockito.verifyNoInteractions(forwardedOfferCreatedHandler);
    }

    @Test
    public void shouldUseAssignedOfferCreatedHandler() {
        OfferUpdatedEventMessage updateEvent = DataCreator.createOfferUpdatedEventMessage(
            OfferUpdatedEventType.STATE_CHANGED
        );
        Offer offer = createOffer(EnquiryType.ASSIGNED);
        updateEvent.setOffer(offer);

        Event event = createEvent(updateEvent);

        offerCreatedHandler.handle(event);

        Mockito.verify(assignedOfferCreatedHandler).handle(Mockito.any(OfferUpdatedEvent.class));
        Mockito.verifyNoInteractions(spotOfferCreatedHandler);
        Mockito.verifyNoInteractions(forwardedOfferCreatedHandler);
    }

    @Test
    public void shouldUseForwardedOfferCreationHandler() {
        OfferUpdatedEventMessage updateEvent = DataCreator.createOfferUpdatedEventMessage(
            OfferUpdatedEventType.STATE_CHANGED
        );
        Offer offer = createOffer(EnquiryType.FORWARDED);
        updateEvent.setOffer(offer);

        Event event = createEvent(updateEvent);

        offerCreatedHandler.handle(event);

        Mockito.verifyNoInteractions(assignedOfferCreatedHandler);
        Mockito.verifyNoInteractions(spotOfferCreatedHandler);
        Mockito.verify(forwardedOfferCreatedHandler).handle(Mockito.any(OfferUpdatedEvent.class));
    }

    @Test
    public void shouldNotDoAnythingForOrderTypeFuel() {
        OfferUpdatedEventMessage updateEvent = DataCreator.createOfferUpdatedEventMessage(
            OfferUpdatedEventType.STATE_CHANGED
        );
        Offer offer = createOffer(EnquiryType.ASSIGNED);
        offer.setType(OrderType.FUEL);
        updateEvent.setOffer(offer);

        Event event = createEvent(updateEvent);

        offerCreatedHandler.handle(event);

        Mockito.verifyNoInteractions(assignedOfferCreatedHandler);
        Mockito.verifyNoInteractions(spotOfferCreatedHandler);
        Mockito.verifyNoInteractions(forwardedOfferCreatedHandler);
    }
}
