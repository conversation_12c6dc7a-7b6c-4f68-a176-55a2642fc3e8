package net.closelink.notificationservice.handler.offer.updated.state.customer.quote;

import static org.junit.Assert.assertEquals;

import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerHandlerTest;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.Before;
import org.junit.Test;

public class QuoteOrderSpotHandlerTest extends CustomerHandlerTest {

    private static final String MESSAGE_OFFER = "quoteOrderTrade.message";
    private static final String TEMPLATE_OFFER = "Order_Supplier_Enquiry_To_Order_Trade";

    private QuoteOrderSpotHandler quoteOrderSpotHandler;

    @Override
    @Before
    public void setUp() {
        super.setUp();
        this.quoteOrderSpotHandler = new QuoteOrderSpotHandler(this.customerStateHandlerSupport);
    }

    @Test
    public void shouldNotifyWithoutBuyerRef() {
        this.event.getEventMessage().getOffer().setBuyerReference(null);

        final OfferUpdatedEvent resultEvent = this.quoteOrderSpotHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(MESSAGE_OFFER, notification.getMessage());
        assertEquals(TEMPLATE_OFFER, notification.getTemplate());
        assertEquals("quoteOrderTrade.subject", notification.getSubject());
    }

    @Test
    public void shouldNotifyWithBuyerRef() {
        this.event.getEventMessage().getOffer().setBuyerReference("Test 123");

        final OfferUpdatedEvent resultEvent = this.quoteOrderSpotHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(MESSAGE_OFFER, notification.getMessage());
        assertEquals(TEMPLATE_OFFER, notification.getTemplate());
        assertEquals("quoteOrderTradeBuyerRef.subject", notification.getSubject());
    }
}
