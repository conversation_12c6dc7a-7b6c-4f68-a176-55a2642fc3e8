package net.closelink.notificationservice.handler.offer.updated.state.customer.all;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;

import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerHandlerTest;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class GeneralCustomerCanceledHandlerTest extends CustomerHandlerTest {

    private static final String MESSAGE = "customerCancel.message";
    private static final String TEMPLATE = "Order_Supplier_Order_Canceled";

    private GeneralCustomerCanceledHandler generalCustomerCanceledHandler;

    @Override
    @Before
    public void setUp() {
        super.setUp();

        this.generalCustomerCanceledHandler = new GeneralCustomerCanceledHandler(this.customerStateHandlerSupport);
    }

    @Test
    public void handleTestWithoutBuyerRef() {
        final String cancelReason = DataCreator.createString();
        this.offer.setBuyerReference(null);
        Mockito.when(this.customerStateHandlerSupport.createCancelMessageReplacements(this.offer)).thenReturn(
            new String[] { cancelReason }
        );

        final OfferUpdatedEvent resultEvent = this.generalCustomerCanceledHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(cancelReason, notification.getMessageReplacements()[0]);
        assertEquals(MESSAGE, notification.getMessage());
        assertEquals(TEMPLATE, notification.getTemplate());
        assertEquals("customerCancel.subject", notification.getSubject());

        verify(this.customerStateHandlerSupport).createCancelMessageReplacements(this.offer);
    }

    @Test
    public void handleTestWithBuyerRef() {
        final String cancelReason = DataCreator.createString();
        this.offer.setBuyerReference("Test 123");
        Mockito.when(this.customerStateHandlerSupport.createCancelMessageReplacements(this.offer)).thenReturn(
            new String[] { cancelReason }
        );

        final OfferUpdatedEvent resultEvent = this.generalCustomerCanceledHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(cancelReason, notification.getMessageReplacements()[0]);
        assertEquals(MESSAGE, notification.getMessage());
        assertEquals(TEMPLATE, notification.getTemplate());
        assertEquals("customerCancelBuyerRef.subject", notification.getSubject());

        verify(this.customerStateHandlerSupport).createCancelMessageReplacements(this.offer);
    }
}
