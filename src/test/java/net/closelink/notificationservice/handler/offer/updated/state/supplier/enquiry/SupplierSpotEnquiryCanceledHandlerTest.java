package net.closelink.notificationservice.handler.offer.updated.state.supplier.enquiry;

import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;

import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierHandlerTest;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class SupplierSpotEnquiryCanceledHandlerTest extends SupplierHandlerTest {

    private static final String MESSAGE_ORDER = "supplierEnquiryCancelledTrade.message";
    private static final String TEMPLATE_ORDER = "Order_Customer_Enquiry_Rejected_Trade";

    private SupplierSpotEnquiryCanceledHandler supplierSpotEnquiryCanceledHandler;

    @BeforeEach
    public void setUp() {
        super.setUp();

        this.supplierSpotEnquiryCanceledHandler = new SupplierSpotEnquiryCanceledHandler(
            this.supplierStateHandlerSupport
        );
    }

    @Test
    void shouldSendNotificationWithoutBuyerRef() {
        DataCreator.makeSpotOrder(this.offer);
        this.offer.setBuyerReference(null);
        final String cancelReason = DataCreator.createString();
        Mockito.when(this.supplierStateHandlerSupport.createCancelMessageReplacements(this.offer)).thenReturn(
            new String[] { cancelReason }
        );

        final Object[] subjectReplacements = new Object[] { DataCreator.createString() };
        Mockito.when(this.supplierStateHandlerSupport.createSpotSubjectReplacements(this.offer)).thenReturn(
            subjectReplacements
        );

        final OfferUpdatedEvent resultEvent =
            this.supplierSpotEnquiryCanceledHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(cancelReason, notification.getMessageReplacements()[0]);
        assertEquals(MESSAGE_ORDER, notification.getMessage());
        assertEquals(TEMPLATE_ORDER, notification.getTemplate());
        assertEquals("supplierEnquiryCancelledTrade.subject", notification.getSubject());
        assertEquals(this.offer.getDateQuoted() != null, notification.isSendMail());
        assertArrayEquals(subjectReplacements, notification.getSubjectReplacements());

        verify(this.supplierStateHandlerSupport).createSpotSubjectReplacements(this.offer);
        verify(this.supplierStateHandlerSupport).createCancelMessageReplacements(this.offer);
    }

    @Test
    void shouldSendNotificationWithBuyerRef() {
        DataCreator.makeSpotOrder(this.offer);
        this.offer.setBuyerReference("Test 123");
        final String cancelReason = DataCreator.createString();
        Mockito.when(this.supplierStateHandlerSupport.createCancelMessageReplacements(this.offer)).thenReturn(
            new String[] { cancelReason }
        );

        final Object[] subjectReplacements = new Object[] { DataCreator.createString() };
        Mockito.when(this.supplierStateHandlerSupport.createSpotSubjectReplacements(this.offer)).thenReturn(
            subjectReplacements
        );

        final OfferUpdatedEvent resultEvent =
            this.supplierSpotEnquiryCanceledHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(cancelReason, notification.getMessageReplacements()[0]);
        assertEquals(MESSAGE_ORDER, notification.getMessage());
        assertEquals(TEMPLATE_ORDER, notification.getTemplate());
        assertEquals("supplierEnquiryCancelledTradeBuyerRef.subject", notification.getSubject());
        assertEquals(this.offer.getDateQuoted() != null, notification.isSendMail());
        assertArrayEquals(subjectReplacements, notification.getSubjectReplacements());

        verify(this.supplierStateHandlerSupport).createSpotSubjectReplacements(this.offer);
        verify(this.supplierStateHandlerSupport).createCancelMessageReplacements(this.offer);
    }
}
