package net.closelink.notificationservice.handler.offer.updated.fields.handler.item;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.objects.Item;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.data.MessageSourceCreator;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.service.rest.product.ProductApiRestService;
import net.closelink.notificationservice.service.rest.product.domain.ProductMessage;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.context.MessageSource;

public class ItemPhysicalSupplierNameChangedHandlerTest {

    private ItemPhysicalSupplierNameChangedHandler handler;
    private BaseActivityLogService activityLogService;
    private ProductApiRestService apiRestService;
    private final MessageSource messageSource = MessageSourceCreator.createMessageSource();

    @Before
    public void setUp() {
        this.activityLogService = Mockito.mock(BaseActivityLogService.class);
        this.apiRestService = Mockito.mock(ProductApiRestService.class);

        this.handler = new ItemPhysicalSupplierNameChangedHandler(
            this.activityLogService,
            this.apiRestService,
            messageSource
        );
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.activityLogService);
        Mockito.verifyNoMoreInteractions(this.apiRestService);
    }

    @Test
    public void handleTest() {
        Item oldValue = DataCreator.createItem();
        Item newValue = DataCreator.createItem();

        ProductMessage productMessage = DataCreator.createProductMessage();
        productMessage.setSulphurContent("HS");

        OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(GsonCoder.decode(newValue));
        updateEvent.getEventMessage().setOldValue(GsonCoder.decode(oldValue));

        when(this.apiRestService.getProduct(newValue.getProductId())).thenReturn(productMessage);

        this.handler.handle(updateEvent);

        ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.apiRestService).getProduct(newValue.getProductId());

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.itemphysicalsupplier"),
            replacementsCapture.capture()
        );

        assertEquals(productMessage.getName() + " " + "max. 3.5 % S", replacementsCapture.getValue()[0]);
        assertEquals(oldValue.getPhysicalSupplierName(), replacementsCapture.getValue()[1]);
        assertEquals(newValue.getPhysicalSupplierName(), replacementsCapture.getValue()[2]);
    }

    @Test
    public void handleTestWithoutTranslation() {
        Item oldValue = DataCreator.createItem();
        Item newValue = DataCreator.createItem();

        ProductMessage productMessage = DataCreator.createProductMessage();

        OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(GsonCoder.decode(newValue));
        updateEvent.getEventMessage().setOldValue(GsonCoder.decode(oldValue));

        when(this.apiRestService.getProduct(newValue.getProductId())).thenReturn(productMessage);

        this.handler.handle(updateEvent);

        ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.apiRestService).getProduct(newValue.getProductId());

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.itemphysicalsupplier"),
            replacementsCapture.capture()
        );

        assertEquals(productMessage.getName(), replacementsCapture.getValue()[0]);
        assertEquals(oldValue.getPhysicalSupplierName(), replacementsCapture.getValue()[1]);
        assertEquals(newValue.getPhysicalSupplierName(), replacementsCapture.getValue()[2]);
    }
}
