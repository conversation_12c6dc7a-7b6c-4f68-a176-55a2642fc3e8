package net.closelink.notificationservice.handler.offer.updated;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.offer.OfferUpdatedEventMessage;
import net.closelink.cenqueue.domainvalue.offer.OfferUpdatedEventType;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.exception.NoHandlerFoundException;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedHandlerRegistry;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentMatchers;

public class OfferUpdatedHandlerTest {

    private OfferUpdatedHandler service;
    private OfferUpdatedHandlerRegistry handlerRegistry;

    @Before
    public void setUp() {
        this.handlerRegistry = mock(OfferUpdatedHandlerRegistry.class);
        this.service = new OfferUpdatedHandler(this.handlerRegistry);
    }

    @After
    public void afterTest() {
        verifyNoMoreInteractions(this.handlerRegistry);
    }

    @Test
    public void handleTest() {
        final OfferUpdatedEventType updatedEventType = DataCreator.createOfferUpdatedEventType();
        final OfferUpdatedEventMessage eventMessage = DataCreator.createOfferUpdatedEventMessage(updatedEventType);

        final Event event = DataCreator.createEvent(eventMessage);
        final OfferUpdatedEventTypeHandler eventHandler = mock(OfferUpdatedEventTypeHandler.class);

        when(this.handlerRegistry.get(eventMessage.getEventType())).thenReturn(eventHandler);

        this.service.handle(event);

        verify(eventHandler).handle(ArgumentMatchers.any(OfferUpdatedEvent.class));
        verifyNoMoreInteractions(eventHandler);
        verify(this.handlerRegistry).get(eventMessage.getEventType());
    }

    @Test
    public void shouldNotDoAnythingForOrderTypeFuel() {
        final OfferUpdatedEventType updatedEventType = DataCreator.createOfferUpdatedEventType();
        final OfferUpdatedEventMessage eventMessage = DataCreator.createOfferUpdatedEventMessage(updatedEventType);
        eventMessage.getOffer().setType(OrderType.FUEL);

        final Event event = DataCreator.createEvent(eventMessage);
        final OfferUpdatedEventTypeHandler eventHandler = mock(OfferUpdatedEventTypeHandler.class);

        when(this.handlerRegistry.get(eventMessage.getEventType())).thenReturn(eventHandler);

        this.service.handle(event);
        verifyNoInteractions(eventHandler);
        verifyNoInteractions(this.handlerRegistry);
    }

    @Test(expected = NoHandlerFoundException.class)
    public void handleFailTest() {
        final OfferUpdatedEventType updatedEventType = DataCreator.createOfferUpdatedEventType();
        final OfferUpdatedEventMessage eventMessage = DataCreator.createOfferUpdatedEventMessage(updatedEventType);

        final Event event = DataCreator.createEvent(eventMessage);
        when(this.handlerRegistry.get(eventMessage.getEventType())).thenReturn(null);

        try {
            this.service.handle(event);
        } finally {
            verify(this.handlerRegistry).get(eventMessage.getEventType());
        }
    }
}
