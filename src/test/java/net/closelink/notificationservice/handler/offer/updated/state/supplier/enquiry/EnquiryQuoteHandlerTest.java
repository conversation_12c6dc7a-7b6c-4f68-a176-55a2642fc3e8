package net.closelink.notificationservice.handler.offer.updated.state.supplier.enquiry;

import de.tschumacher.simplestatemachine.domain.StateChange;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class EnquiryQuoteHandlerTest {

    private EnquiryQuoteAssignedHandler enquiryQuoteAssignedHandler = Mockito.mock(EnquiryQuoteAssignedHandler.class);
    private EnquiryQuoteSpotHandler enquiryQuoteSpotHandler = Mockito.mock(EnquiryQuoteSpotHandler.class);
    private EnquiryQuoteHandler handler;
    private OfferUpdatedEvent event;
    private StateChange<OfferState> stateChange;

    @BeforeEach
    void setUp() {
        Offer offer = DataCreator.createOffer();
        this.event = DataCreator.createOfferUpdatedEvent(offer);
        this.stateChange = DataCreator.createOfferStateChange();
        this.handler = new EnquiryQuoteHandler(this.enquiryQuoteAssignedHandler, this.enquiryQuoteSpotHandler);
    }

    @Test
    void shouldCallQuoteAssignedHandler() {
        Mockito.when(this.enquiryQuoteAssignedHandler.handle(this.stateChange, this.event)).thenReturn(this.event);

        OfferUpdatedEvent event = handler.handle(this.stateChange, this.event);

        Mockito.verify(this.enquiryQuoteAssignedHandler).handle(this.stateChange, this.event);

        Assertions.assertThat(event).isEqualTo(this.event);
    }
}
