package net.closelink.notificationservice.handler.offer.updated.state.customer.all;

import de.tschumacher.simplestatemachine.domain.StateChange;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class CustomerCanceledHandlerTest {

    private GeneralCustomerCanceledHandler generalCustomerCanceledHandler = Mockito.mock(
        GeneralCustomerCanceledHandler.class
    );
    private ForwardedCustomerCanceledHandler forwardedCustomerCanceledHandler = Mockito.mock(
        ForwardedCustomerCanceledHandler.class
    );
    private CustomerCanceledHandler customerCanceledHandler;
    private OfferUpdatedEvent event;
    private StateChange<OfferState> stateChange;

    @BeforeEach
    void setUp() {
        Offer offer = DataCreator.createOffer();
        this.event = DataCreator.createOfferUpdatedEvent(offer);
        this.stateChange = DataCreator.createOfferStateChange();

        this.customerCanceledHandler = new CustomerCanceledHandler(
            this.generalCustomerCanceledHandler,
            this.forwardedCustomerCanceledHandler
        );
    }

    @Test
    void shouldCallGenericHandler() {
        this.event.getEventMessage().getOffer().setEnquiryType(EnquiryType.SPOT);
        Mockito.when(this.generalCustomerCanceledHandler.handle(this.stateChange, this.event)).thenReturn(this.event);

        OfferUpdatedEvent event = customerCanceledHandler.handle(this.stateChange, this.event);

        Mockito.verify(this.generalCustomerCanceledHandler).handle(this.stateChange, this.event);

        Assertions.assertThat(event).isEqualTo(this.event);
    }
}
