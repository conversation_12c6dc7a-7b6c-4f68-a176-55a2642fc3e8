package net.closelink.notificationservice.handler.offer.updated.fields.handler.samplekit;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.objects.Samplekit;
import net.closelink.cenqueue.values.Money;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.utils.Formatter;
import org.joda.money.BigMoney;
import org.joda.money.CurrencyUnit;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class SamplekitValueChangedHandlerTest {

    private SamplekitValueChangedHandler service;
    private BaseActivityLogService activityLogService;

    @Before
    public void setUp() {
        this.activityLogService = Mockito.mock(BaseActivityLogService.class);

        this.service = new SamplekitValueChangedHandler(this.activityLogService);
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.activityLogService);
    }

    @Test
    public void handleTest() {
        final Samplekit newValue = DataCreator.createSamplekit();
        final Samplekit oldValue = DataCreator.createSamplekit();

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(GsonCoder.decode(newValue));
        updateEvent.getEventMessage().setOldValue(GsonCoder.decode(oldValue));

        this.service.handle(updateEvent);

        final ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.samplekitvalue"),
            replacementsCapture.capture()
        );

        assertEquals(newValue.getName(), replacementsCapture.getValue()[0]);
        assertEquals(formatMoney(oldValue.getValue()), replacementsCapture.getValue()[1]);
        assertEquals(formatMoney(newValue.getValue()), replacementsCapture.getValue()[2]);
    }

    private String formatMoney(final Money value) {
        return Formatter.formatPrice(createMoney(value));
    }

    private static BigMoney createMoney(Money money) {
        if (money == null) return null;
        return BigMoney.of(CurrencyUnit.of(money.getCurrency()), money.getValue());
    }
}
