package net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge;

import java.util.HashMap;
import net.closelink.cenqueue.objects.Surcharge;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.service.rest.coredata.CoreDataService;
import net.closelink.notificationservice.service.rest.coredata.EnumMessage;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class SurchargeNameFormatterTest {

    private SurchargeNameFormatter surchargeNameFormatter;
    private CoreDataService coreDataService;

    @BeforeEach
    public void setUp() {
        this.coreDataService = Mockito.mock(CoreDataService.class);

        surchargeNameFormatter = new SurchargeNameFormatter(coreDataService);
    }

    @Test
    public void shouldReturnNameIfTypeCustom() {
        String surchargeName = "Surcharge Name";

        Surcharge surcharge = DataCreator.createSurcharge();
        surcharge.setName(surchargeName);
        surcharge.setSurchargeType("CUSTOM");

        String displayName = surchargeNameFormatter.getDisplayName(surcharge);
        Assertions.assertThat(displayName).isEqualTo(surchargeName);
    }

    @Test
    public void shouldReturnTypeIfTypeOtherThanCustom() {
        Surcharge surcharge = DataCreator.createSurcharge();
        surcharge.setSurchargeType("PORT_FEE");

        HashMap<String, EnumMessage.DetailEnumMessage> enumMessage = new HashMap<>();

        enumMessage.put("PORT_FEE", EnumMessage.DetailEnumMessage.builder().humanReadableValue("Port Fee").build());

        Mockito.when(coreDataService.getHumanReadableValueForSurchargeTypeMessage()).thenReturn(enumMessage);

        String displayName = surchargeNameFormatter.getDisplayName(surcharge);
        Assertions.assertThat(displayName).isEqualTo("Port Fee");
    }
}
