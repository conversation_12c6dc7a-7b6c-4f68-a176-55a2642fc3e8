package net.closelink.notificationservice.handler.offer.updated.fields.activity;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import java.util.Map;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.customer.CustomerActivityLogNotifyService;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.supplier.SupplierActivityLogNotifyService;
import net.closelink.notificationservice.handler.offer.updated.fields.translation.TranslationSupportService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.replacement.offer.OfferReplacementService;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementOffer;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierMessage;
import net.closelink.test.mockito.MockitoHelper;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class BaseActivityLogServiceTest {

    private BaseActivityLogService service;

    private CustomerApiRestService customerApiRestService;
    private SupplierApiRestService supplierApiRestService;
    private CustomerActivityLogNotifyService customerActivityLogService;
    private SupplierActivityLogNotifyService supplierActivityLogService;
    private TranslationSupportService translationSupportService;
    private OfferReplacementService offerReplacementService;
    private WebAppReplacementService webappReplacementService;

    @Before
    public void setUp() {
        this.customerApiRestService = Mockito.mock(CustomerApiRestService.class);
        this.supplierApiRestService = Mockito.mock(SupplierApiRestService.class);
        this.customerActivityLogService = Mockito.mock(CustomerActivityLogNotifyService.class);
        this.supplierActivityLogService = Mockito.mock(SupplierActivityLogNotifyService.class);
        this.translationSupportService = Mockito.mock(TranslationSupportService.class);
        this.offerReplacementService = Mockito.mock(OfferReplacementService.class);
        this.webappReplacementService = Mockito.mock(WebAppReplacementService.class);

        this.service = new DefaultBaseActivityLogService(
            this.customerApiRestService,
            this.supplierApiRestService,
            this.customerActivityLogService,
            this.supplierActivityLogService,
            this.translationSupportService,
            this.offerReplacementService,
            this.webappReplacementService
        );
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.customerApiRestService);
        Mockito.verifyNoMoreInteractions(this.supplierApiRestService);
        Mockito.verifyNoMoreInteractions(this.customerActivityLogService);
        Mockito.verifyNoMoreInteractions(this.supplierActivityLogService);
        Mockito.verifyNoMoreInteractions(this.translationSupportService);
        Mockito.verifyNoMoreInteractions(this.offerReplacementService);
        Mockito.verifyNoMoreInteractions(this.webappReplacementService);
    }

    @Test
    public void createCustomerActivityLogTest() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().getOffer().setDateCreated("2018-11-03T13:37:00Z");
        updateEvent.getEvent().getEventTrigger().setCompanyType(UserType.SUPPLIER);

        final SupplierMessage supplier = DataCreator.createSupplierMessage();
        final String message = DataCreator.createString();
        final Object[] replacements = new Object[] { DataCreator.createString() };
        final Map<String, String> mailReplacements = DataCreator.createStringMap();

        when(
            this.supplierApiRestService.getSupplier(updateEvent.getEvent().getEventTrigger().getCompanyId())
        ).thenReturn(supplier);

        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(mailReplacements);
        MockitoHelper.mockInputValueAsReturnValue(this.translationSupportService.translate(replacements));

        this.service.createActivityLog(updateEvent, message, replacements);

        final ArgumentCaptor<Notification.NotificationBuilder> notificationCapture = forClass(
            Notification.NotificationBuilder.class
        );

        Mockito.verify(this.customerActivityLogService).notify(eq(updateEvent), notificationCapture.capture());

        final Notification notification = notificationCapture.getValue().build();
        assertEquals(message, notification.getMessage());
        assertEquals(updateEvent.getEventMessage().getOffer().getId(), notification.getOfferId());
        assertEquals(supplier.getName(), notification.getMessageReplacements()[0]);
        assertEquals(replacements[0], notification.getMessageReplacements()[1]);
        assertEquals(mailReplacements, notification.getMailReplacements());
        assertEquals(notification.getCategory(), NotificationCategory.OFFER_UPDATE);

        Mockito.verify(this.supplierApiRestService).getSupplier(
            updateEvent.getEvent().getEventTrigger().getCompanyId()
        );
        Mockito.verify(this.translationSupportService).translate(replacements);
        Mockito.verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        Mockito.verify(this.webappReplacementService).create(any(ReplacementOffer.class), eq(ReceiverType.CUSTOMER));
    }

    @Test
    public void createSupplierActivityLogTest() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().getOffer().setDateCreated("2018-11-03T13:37:00Z");
        updateEvent.getEvent().getEventTrigger().setCompanyType(UserType.CUSTOMER);

        final CustomerMessage customer = DataCreator.createCustomerMessage();
        final String message = DataCreator.createString();
        final Object[] replacements = new Object[] { DataCreator.createString() };
        final Map<String, String> mailReplacements = DataCreator.createStringMap();

        when(
            this.customerApiRestService.getCustomer(updateEvent.getEvent().getEventTrigger().getCompanyId())
        ).thenReturn(customer);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(mailReplacements);
        MockitoHelper.mockInputValueAsReturnValue(this.translationSupportService.translate(replacements));

        this.service.createActivityLog(updateEvent, message, replacements);

        final ArgumentCaptor<Notification.NotificationBuilder> notificationCapture = forClass(
            Notification.NotificationBuilder.class
        );

        Mockito.verify(this.supplierActivityLogService).notify(eq(updateEvent), notificationCapture.capture());

        final Notification notification = notificationCapture.getValue().build();
        assertEquals(message, notification.getMessage());
        assertEquals(updateEvent.getEventMessage().getOffer().getId(), notification.getOfferId());
        assertEquals(customer.getName(), notification.getMessageReplacements()[0]);
        assertEquals(replacements[0], notification.getMessageReplacements()[1]);
        assertEquals(mailReplacements, notification.getMailReplacements());
        assertEquals(notification.getCategory(), NotificationCategory.OFFER_UPDATE);

        Mockito.verify(this.customerApiRestService).getCustomer(
            updateEvent.getEvent().getEventTrigger().getCompanyId()
        );
        Mockito.verify(this.translationSupportService).translate(replacements);
        Mockito.verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        Mockito.verify(this.webappReplacementService).create(any(ReplacementOffer.class), eq(ReceiverType.SUPPLIER));
    }
}
