package net.closelink.notificationservice.handler.offer.updated.state.supplier.enquiry;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;

import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierHandlerTest;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class SupplierAssignedEnquiryCanceledHandlerTest extends SupplierHandlerTest {

    private static final String MESSAGE_OFFER = "supplierEnquiryCancelled.message";
    private static final String TEMPLATE_OFFER = "Order_Customer_Enquiry_Rejected";

    private SupplierAssignedEnquiryCanceledHandler supplierAssignedEnquiryCanceledHandler;

    @BeforeEach
    public void setUp() {
        super.setUp();

        this.supplierAssignedEnquiryCanceledHandler = new SupplierAssignedEnquiryCanceledHandler(
            this.supplierStateHandlerSupport
        );
    }

    @Test
    void shouldSendNotificationWithoutBuyerRef() {
        final String cancelReason = DataCreator.createString();
        this.offer.setBuyerReference(null);
        Mockito.when(this.supplierStateHandlerSupport.createCancelMessageReplacements(this.offer)).thenReturn(
            new String[] { cancelReason }
        );

        final OfferUpdatedEvent resultEvent =
            this.supplierAssignedEnquiryCanceledHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(cancelReason, notification.getMessageReplacements()[0]);
        assertEquals(MESSAGE_OFFER, notification.getMessage());
        assertEquals(TEMPLATE_OFFER, notification.getTemplate());
        assertEquals("supplierEnquiryCancelled.subject", notification.getSubject());

        verify(this.supplierStateHandlerSupport).createCancelMessageReplacements(this.offer);
    }

    @Test
    void shouldSendNotificationWithBuyerRef() {
        final String cancelReason = DataCreator.createString();
        this.offer.setBuyerReference("Test 123");
        Mockito.when(this.supplierStateHandlerSupport.createCancelMessageReplacements(this.offer)).thenReturn(
            new String[] { cancelReason }
        );

        final OfferUpdatedEvent resultEvent =
            this.supplierAssignedEnquiryCanceledHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(cancelReason, notification.getMessageReplacements()[0]);
        assertEquals(MESSAGE_OFFER, notification.getMessage());
        assertEquals(TEMPLATE_OFFER, notification.getTemplate());
        assertEquals("supplierEnquiryCancelledBuyerRef.subject", notification.getSubject());

        verify(this.supplierStateHandlerSupport).createCancelMessageReplacements(this.offer);
    }
}
