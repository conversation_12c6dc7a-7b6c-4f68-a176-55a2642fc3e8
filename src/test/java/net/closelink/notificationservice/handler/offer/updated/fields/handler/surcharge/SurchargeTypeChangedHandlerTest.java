package net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.objects.Surcharge;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.service.rest.coredata.CoreDataService;
import net.closelink.notificationservice.service.rest.coredata.EnumMessage;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class SurchargeTypeChangedHandlerTest {

    private SurchargeTypeChangedHandler service;
    private BaseActivityLogService activityLogService;
    private CoreDataService coreDataService;
    private final HashMap<String, EnumMessage.DetailEnumMessage> surchargeTypeEnumMap = new HashMap<>();

    @Before
    public void setUp() {
        this.activityLogService = Mockito.mock(BaseActivityLogService.class);
        this.coreDataService = Mockito.mock(CoreDataService.class);

        this.service = new SurchargeTypeChangedHandler(this.activityLogService, coreDataService);
        surchargeTypeEnumMap.put(
            "BARGE_CHARGE",
            EnumMessage.DetailEnumMessage.builder().humanReadableValue("Barge Charge").build()
        );
        surchargeTypeEnumMap.put(
            "SHORT_NOTICE",
            EnumMessage.DetailEnumMessage.builder().humanReadableValue("Short Notice").build()
        );
        surchargeTypeEnumMap.put(
            "CUSTOM",
            EnumMessage.DetailEnumMessage.builder().humanReadableValue("Custom").build()
        );
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.activityLogService);
    }

    @Test
    public void shouldHandleTypeChangeTest() {
        final Surcharge oldValue = DataCreator.createSurcharge();
        oldValue.setSurchargeType("BARGE_CHARGE");
        final Surcharge newValue = DataCreator.createSurcharge();
        newValue.setSurchargeType("SHORT_NOTICE");

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setOldValue(GsonCoder.decode(oldValue));
        updateEvent.getEventMessage().setNewValue(GsonCoder.decode(newValue));
        when(coreDataService.getHumanReadableValueForSurchargeTypeMessage()).thenReturn(surchargeTypeEnumMap);

        this.service.handle(updateEvent);

        final ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.surchagename"),
            replacementsCapture.capture()
        );

        assertEquals("Barge Charge", replacementsCapture.getValue()[0]);
        assertEquals("Short Notice", replacementsCapture.getValue()[1]);
    }

    @Test
    public void shouldHandleNonCustomToCustomTypeChangeTest() {
        final Surcharge oldValue = DataCreator.createSurcharge();
        oldValue.setSurchargeType("BARGE_CHARGE");
        final Surcharge newValue = DataCreator.createSurcharge();
        newValue.setSurchargeType("CUSTOM");

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setOldValue(GsonCoder.decode(oldValue));
        updateEvent.getEventMessage().setNewValue(GsonCoder.decode(newValue));
        when(coreDataService.getHumanReadableValueForSurchargeTypeMessage()).thenReturn(surchargeTypeEnumMap);

        this.service.handle(updateEvent);

        final ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.surchagename"),
            replacementsCapture.capture()
        );

        assertEquals("Barge Charge", replacementsCapture.getValue()[0]);
        assertEquals(newValue.getName(), replacementsCapture.getValue()[1]);
    }

    @Test
    public void shouldHandleCustomToNonCustomTypeChangeTest() {
        final Surcharge oldValue = DataCreator.createSurcharge();
        oldValue.setSurchargeType("CUSTOM");
        final Surcharge newValue = DataCreator.createSurcharge();
        newValue.setSurchargeType("BARGE_CHARGE");

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setOldValue(GsonCoder.decode(oldValue));
        updateEvent.getEventMessage().setNewValue(GsonCoder.decode(newValue));
        when(coreDataService.getHumanReadableValueForSurchargeTypeMessage()).thenReturn(surchargeTypeEnumMap);

        this.service.handle(updateEvent);

        final ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.surchagename"),
            replacementsCapture.capture()
        );

        assertEquals(oldValue.getName(), replacementsCapture.getValue()[0]);
        assertEquals("Barge Charge", replacementsCapture.getValue()[1]);
    }
}
