package net.closelink.notificationservice.handler.offer.updated.state.common.delivered;

import static org.junit.Assert.assertEquals;

import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierHandlerTest;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.delivered.GeneralSupplierDeliveredConfirmedHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.Before;
import org.junit.Test;

public class GeneralDeliveredHandlerTest extends SupplierHandlerTest {

    private static final String MESSAGE = "deliveredConfirm.message";
    private static final String TEMPLATE = "Order_Customer_Order_Supplier_Changes";

    private GeneralSupplierDeliveredConfirmedHandler handler;

    @Override
    @Before
    public void setUp() {
        super.setUp();
        this.handler = new GeneralSupplierDeliveredConfirmedHandler(this.supplierStateHandlerSupport);
    }

    @Test
    public void handleTestWithoutBuyerRef() {
        this.event.getEventMessage().getOffer().setBuyerReference(null);
        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(MESSAGE, notification.getMessage());
        assertEquals(TEMPLATE, notification.getTemplate());
        assertEquals("deliveredConfirm.subject", notification.getSubject());
    }

    @Test
    public void handleTestWithBuyerRef() {
        this.event.getEventMessage().getOffer().setBuyerReference("Test 123");
        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(MESSAGE, notification.getMessage());
        assertEquals(TEMPLATE, notification.getTemplate());
        assertEquals("deliveredConfirmBuyerRef.subject", notification.getSubject());
    }
}
