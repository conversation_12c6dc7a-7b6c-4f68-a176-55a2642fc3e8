package net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;

import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerHandlerTest;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class EnquiryCanceledSpotHandlerTest extends CustomerHandlerTest {

    private static final String ORDER_QUOTE_MESSAGE = "quoteQuoteCancelled.message";
    private static final String ORDER_QUOTE_TEMPLATE = "Order_Supplier_Quote_Canceled_Trade";

    private static final String ORDER_ENQUIRY_MESSAGE = "enquiryEnquiryCancelled.message";
    private static final String ORDER_ENQUIRY_TEMPLATE = "Order_Supplier_Enquiry_Canceled_Trade";

    private EnquiryCanceledSpotHandler enquiryCanceledSpotHandler;

    @BeforeEach
    public void setUp() {
        super.setUp();

        enquiryCanceledSpotHandler = new EnquiryCanceledSpotHandler(this.customerStateHandlerSupport);
    }

    @Test
    void shouldSendNotificationForQuotedSpotOrderWithoutBuyerRef() {
        DataCreator.makeSpotOrder(this.offer);
        this.offer.setBuyerReference(null);
        final String cancelReason = DataCreator.createString();
        Mockito.when(this.customerStateHandlerSupport.createCancelMessageReplacements(this.offer)).thenReturn(
            new String[] { cancelReason }
        );

        final OfferUpdatedEvent resultEvent = this.enquiryCanceledSpotHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(cancelReason, notification.getMessageReplacements()[0]);

        assertEquals(ORDER_QUOTE_MESSAGE, notification.getMessage());
        assertEquals(ORDER_QUOTE_TEMPLATE, notification.getTemplate());
        assertEquals("quoteQuoteCancelled.subject", notification.getSubject());

        verify(this.customerStateHandlerSupport).createCancelMessageReplacements(this.offer);
    }

    @Test
    void shouldSendNotificationForQuotedSpotOrderWithBuyerRef() {
        DataCreator.makeSpotOrder(this.offer);
        this.offer.setBuyerReference("Test 123");
        final String cancelReason = DataCreator.createString();
        Mockito.when(this.customerStateHandlerSupport.createCancelMessageReplacements(this.offer)).thenReturn(
            new String[] { cancelReason }
        );

        final OfferUpdatedEvent resultEvent = this.enquiryCanceledSpotHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(cancelReason, notification.getMessageReplacements()[0]);

        assertEquals(ORDER_QUOTE_MESSAGE, notification.getMessage());
        assertEquals(ORDER_QUOTE_TEMPLATE, notification.getTemplate());
        assertEquals("quoteQuoteCancelledBuyerRef.subject", notification.getSubject());

        verify(this.customerStateHandlerSupport).createCancelMessageReplacements(this.offer);
    }

    @Test
    void shouldSendNotificationForOrderEnquiryWithoutBuyerRef() {
        DataCreator.makeSpotOrder(this.offer);
        this.offer.setDateQuoted(null);
        this.offer.setBuyerReference(null);
        final String cancelReason = DataCreator.createString();
        Mockito.when(this.customerStateHandlerSupport.createCancelMessageReplacements(this.offer)).thenReturn(
            new String[] { cancelReason }
        );

        final OfferUpdatedEvent resultEvent = this.enquiryCanceledSpotHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(cancelReason, notification.getMessageReplacements()[0]);

        assertEquals(ORDER_ENQUIRY_MESSAGE, notification.getMessage());
        assertEquals(ORDER_ENQUIRY_TEMPLATE, notification.getTemplate());
        assertEquals("enquiryEnquiryCancelled.subject", notification.getSubject());

        verify(this.customerStateHandlerSupport).createCancelMessageReplacements(this.offer);
    }

    @Test
    void shouldSendNotificationForOrderEnquiryWithBuyerRef() {
        DataCreator.makeSpotOrder(this.offer);
        this.offer.setDateQuoted(null);
        this.offer.setBuyerReference("Test 123");
        final String cancelReason = DataCreator.createString();
        Mockito.when(this.customerStateHandlerSupport.createCancelMessageReplacements(this.offer)).thenReturn(
            new String[] { cancelReason }
        );

        final OfferUpdatedEvent resultEvent = this.enquiryCanceledSpotHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(cancelReason, notification.getMessageReplacements()[0]);

        assertEquals(ORDER_ENQUIRY_MESSAGE, notification.getMessage());
        assertEquals(ORDER_ENQUIRY_TEMPLATE, notification.getTemplate());
        assertEquals("enquiryEnquiryCancelledBuyerRef.subject", notification.getSubject());

        verify(this.customerStateHandlerSupport).createCancelMessageReplacements(this.offer);
    }
}
