package net.closelink.notificationservice.handler.offer.updated.fields.translation;

import static org.junit.Assert.assertEquals;

import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.data.MessageSourceCreator;
import org.junit.Before;
import org.junit.Test;
import org.springframework.context.MessageSource;
import org.springframework.context.support.ResourceBundleMessageSource;

public class TranslationSupportServiceTest {

    private TranslationSupportService service;
    private MessageSource messageSource;

    @Before
    public void setUp() {
        this.messageSource = MessageSourceCreator.createMessageSource();

        this.service = new DefaultTranslationSupportService(this.messageSource);
    }

    @Test
    public void translateOne() {
        final Object value = DataCreator.createString();

        final Object translateValue = this.service.translate(value);

        assertEquals(value, translateValue);
    }

    @Test
    public void translateNullOne() {
        final Object value = null;

        final Object translateValue = this.service.translate(value);

        assertEquals("n/a", translateValue);
    }

    @Test
    public void translateMulti() {
        final Object[] value = new Object[] { DataCreator.createString(), DataCreator.createString() };

        final Object[] translateValue = this.service.translate(value);

        assertEquals(value[0], translateValue[0]);
        assertEquals(value[1], translateValue[1]);
    }

    @Test
    public void translateNullMulti() {
        final Object[] value = new Object[] { null, null };

        final Object[] translateValue = this.service.translate(value);

        assertEquals("n/a", translateValue[0]);
        assertEquals("n/a", translateValue[1]);
    }
}
