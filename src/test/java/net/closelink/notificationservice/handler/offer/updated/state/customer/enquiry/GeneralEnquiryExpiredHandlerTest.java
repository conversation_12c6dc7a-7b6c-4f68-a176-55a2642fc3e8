package net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry;

import static org.junit.Assert.assertEquals;

import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerHandlerTest;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.Before;
import org.junit.Test;

public class GeneralEnquiryExpiredHandlerTest extends CustomerHandlerTest {

    private static final String MESSAGE = "enquiryEnquiryExpired.message";
    private static final String TEMPLATE = "Order_Supplier_Enquiry_Enquiry_Expired";

    private GeneralEnquiryExpiredHandler handler;

    @Override
    @Before
    public void setUp() {
        super.setUp();
        this.handler = new GeneralEnquiryExpiredHandler(this.customerStateHandlerSupport);
    }

    @Test
    public void handleTestWithoutBuyerRef() {
        this.event.getEventMessage().getOffer().setBuyerReference(null);

        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(MESSAGE, notification.getMessage());
        assertEquals(TEMPLATE, notification.getTemplate());
        assertEquals("enquiryEnquiryExpired.subject", notification.getSubject());
    }

    @Test
    public void handleTestWithBuyerRef() {
        this.event.getEventMessage().getOffer().setBuyerReference("Test 123");

        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(MESSAGE, notification.getMessage());
        assertEquals(TEMPLATE, notification.getTemplate());
        assertEquals("enquiryEnquiryExpiredBuyerRef.subject", notification.getSubject());
    }
}
