package net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.objects.Surcharge;
import net.closelink.cenqueue.values.Money;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.utils.Formatter;
import org.joda.money.BigMoney;
import org.joda.money.CurrencyUnit;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class SurchargeValueChangedHandlerTest {

    private SurchargeValueChangedHandler service;
    private BaseActivityLogService activityLogService;
    private SurchargeNameFormatter surchargeNameFormatter;

    @Before
    public void setUp() {
        this.activityLogService = Mockito.mock(BaseActivityLogService.class);
        this.surchargeNameFormatter = Mockito.mock(SurchargeNameFormatter.class);

        this.service = new SurchargeValueChangedHandler(this.activityLogService, this.surchargeNameFormatter);
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.activityLogService);
    }

    @Test
    public void handleTest() {
        final Surcharge newValue = DataCreator.createSurcharge();
        final Surcharge oldValue = DataCreator.createSurcharge();

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(GsonCoder.decode(newValue));
        updateEvent.getEventMessage().setOldValue(GsonCoder.decode(oldValue));

        when(this.surchargeNameFormatter.getDisplayName(newValue)).thenReturn(newValue.getName());

        this.service.handle(updateEvent);

        final ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.surchargevalue"),
            replacementsCapture.capture()
        );

        assertEquals(newValue.getName(), replacementsCapture.getValue()[0]);
        assertEquals(formatMoney(oldValue.getValue()), replacementsCapture.getValue()[1]);
        assertEquals(formatMoney(newValue.getValue()), replacementsCapture.getValue()[2]);
    }

    private String formatMoney(final Money value) {
        return Formatter.formatPrice(createMoney(value));
    }

    private static BigMoney createMoney(Money money) {
        if (money == null) return null;
        return BigMoney.of(CurrencyUnit.of(money.getCurrency()), money.getValue());
    }
}
