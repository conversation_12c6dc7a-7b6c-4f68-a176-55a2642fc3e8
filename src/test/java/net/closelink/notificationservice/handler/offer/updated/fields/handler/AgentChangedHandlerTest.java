package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class AgentChangedHandlerTest {

    private AgentChangedHandler service;
    private BaseActivityLogService activityLogService;

    @Before
    public void setUp() {
        this.activityLogService = Mockito.mock(BaseActivityLogService.class);

        this.service = new AgentChangedHandler(this.activityLogService);
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.activityLogService);
    }

    @Test
    public void handleTest() {
        final String newValue = DataCreator.createString();
        final String oldValue = DataCreator.createString();

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(newValue);
        updateEvent.getEventMessage().setOldValue(oldValue);

        this.service.handle(updateEvent);

        final ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.agent"),
            replacementsCapture.capture()
        );

        assertEquals(oldValue, replacementsCapture.getValue()[0]);
        assertEquals(newValue, replacementsCapture.getValue()[1]);
    }
}
