package net.closelink.notificationservice.handler.offer.updated.fields.handler.samplekit;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.objects.Samplekit;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class SamplekitQuantityChangedHandlerTest {

    private SamplekitQuantityChangedHandler service;
    private BaseActivityLogService activityLogService;

    @Before
    public void setUp() {
        this.activityLogService = Mockito.mock(BaseActivityLogService.class);

        this.service = new SamplekitQuantityChangedHandler(this.activityLogService);
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.activityLogService);
    }

    @Test
    public void handleTest() {
        final Samplekit newValue = DataCreator.createSamplekit();
        final Samplekit oldValue = DataCreator.createSamplekit();

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(GsonCoder.decode(newValue));
        updateEvent.getEventMessage().setOldValue(GsonCoder.decode(oldValue));

        this.service.handle(updateEvent);

        final ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.samplekitquantity"),
            replacementsCapture.capture()
        );

        assertEquals(newValue.getName(), replacementsCapture.getValue()[0]);
        assertEquals(oldValue.getQuantity(), replacementsCapture.getValue()[1]);
        assertEquals(newValue.getQuantity(), replacementsCapture.getValue()[2]);
    }
}
