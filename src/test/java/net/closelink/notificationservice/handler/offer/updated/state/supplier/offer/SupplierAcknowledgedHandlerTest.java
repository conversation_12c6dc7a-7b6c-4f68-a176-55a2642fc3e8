package net.closelink.notificationservice.handler.offer.updated.state.supplier.offer;

import static org.junit.Assert.assertEquals;

import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierHandlerTest;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.order.GeneralSupplierAcknowledgedHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.Before;
import org.junit.Test;

public class SupplierAcknowledgedHandlerTest extends SupplierHandlerTest {

    private GeneralSupplierAcknowledgedHandler handler;

    @Override
    @Before
    public void setUp() {
        super.setUp();
        this.handler = new GeneralSupplierAcknowledgedHandler(this.supplierStateHandlerSupport);
    }

    @Test
    public void handleTestWithoutBuyerRef() {
        this.offer.setBuyerReference(null);

        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals("acknowledgedOffer.message", notification.getMessage());
        assertEquals("Order_Customer_Order_Acknowledged", notification.getTemplate());
        assertEquals("acknowledgedOffer.subject", notification.getSubject());
    }

    @Test
    public void handleTestWithBuyerRef() {
        this.offer.setBuyerReference("Lirum Larum");

        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals("acknowledgedOffer.message", notification.getMessage());
        assertEquals("Order_Customer_Order_Acknowledged", notification.getTemplate());
        assertEquals("acknowledgedOfferBuyerRef.subject", notification.getSubject());
    }
}
