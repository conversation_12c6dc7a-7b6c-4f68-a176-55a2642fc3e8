package net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry;

import static org.junit.Assert.assertEquals;

import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerHandlerTest;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.Before;
import org.junit.Test;

public class AssignedEnquiryOrderHandlerTest extends CustomerHandlerTest {

    private static final String MESSAGE = "enquiryOrder.message";
    private static final String TEMPLATE = "Order_Supplier_Enquiry_To_Order";

    private AssignedEnquiryOrderHandler handler;

    @Override
    @Before
    public void setUp() {
        super.setUp();
        this.handler = new AssignedEnquiryOrderHandler(this.customerStateHandlerSupport);
    }

    @Test
    public void handleWithoutBuyerRefTest() {
        this.event.getEventMessage().getOffer().setBuyerReference(null);
        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(MESSAGE, notification.getMessage());
        assertEquals(TEMPLATE, notification.getTemplate());
        assertEquals("enquiryOrder.subject", notification.getSubject());
    }

    @Test
    public void handleWithBuyerRefTest() {
        this.event.getEventMessage().getOffer().setBuyerReference("Test 123");
        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(MESSAGE, notification.getMessage());
        assertEquals(TEMPLATE, notification.getTemplate());
        assertEquals("enquiryOrderBuyerRef.subject", notification.getSubject());
    }
}
