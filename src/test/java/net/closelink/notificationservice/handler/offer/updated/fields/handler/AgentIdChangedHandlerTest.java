package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class AgentIdChangedHandlerTest {

    private AgentIdChangedHandler handler;
    private BaseActivityLogService activityLogService;

    @Before
    public void setUp() {
        this.activityLogService = Mockito.mock(BaseActivityLogService.class);

        this.handler = new AgentIdChangedHandler(this.activityLogService);
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.activityLogService);
    }

    @Test
    public void shouldCallActivityLogServiceForNewAgent() {
        final String newValue = DataCreator.createString();
        final String oldValue = null;

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(newValue);
        updateEvent.getEventMessage().setOldValue(oldValue);

        this.handler.handle(updateEvent);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.agentId.added"),
            eq(new Object[] { oldValue, newValue })
        );
    }

    @Test
    public void shouldCallActivityLogServiceForUpdatedAgent() {
        final String newValue = DataCreator.createString();
        final String oldValue = DataCreator.createString();

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(newValue);
        updateEvent.getEventMessage().setOldValue(oldValue);

        this.handler.handle(updateEvent);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.agentId.updated"),
            eq(new Object[] { oldValue, newValue })
        );
    }

    @Test
    public void shouldCallActivityLogServiceForRemovedAgent() {
        final String newValue = null;
        final String oldValue = DataCreator.createString();

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(newValue);
        updateEvent.getEventMessage().setOldValue(oldValue);

        this.handler.handle(updateEvent);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.agentId.removed"),
            eq(new Object[] { oldValue, newValue })
        );
    }
}
