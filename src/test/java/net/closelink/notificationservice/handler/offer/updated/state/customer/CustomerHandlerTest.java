package net.closelink.notificationservice.handler.offer.updated.state.customer;

import static org.mockito.Mockito.verify;

import de.tschumacher.simplestatemachine.domain.StateChange;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.Notification.NotificationBuilder;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.After;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class CustomerHandlerTest {

    protected CustomerStateHandlerSupport customerStateHandlerSupport;
    protected OfferUpdatedEvent event;
    protected StateChange<OfferState> stateChange;
    protected NotificationBuilder notificationBuilder;
    protected Offer offer;

    public void setUp() {
        this.customerStateHandlerSupport = Mockito.mock(CustomerStateHandlerSupport.class);
        this.offer = DataCreator.createOffer();
        this.event = DataCreator.createOfferUpdatedEvent(this.offer);
        this.stateChange = DataCreator.createOfferStateChange();
        this.notificationBuilder = DataCreator.createNotificationBuilder();

        Mockito.when(this.customerStateHandlerSupport.createNotificationBuilder(this.event)).thenReturn(
            this.notificationBuilder
        );
    }

    @After
    public void afterTest() {
        verify(this.customerStateHandlerSupport).createNotificationBuilder(this.event);
        Mockito.verifyNoMoreInteractions(this.customerStateHandlerSupport);
    }

    protected Notification verifyNotify() {
        final ArgumentCaptor<Notification> notificationCaptor = ArgumentCaptor.forClass(Notification.class);

        verify(this.customerStateHandlerSupport).notify(notificationCaptor.capture());

        return notificationCaptor.getValue();
    }
}
