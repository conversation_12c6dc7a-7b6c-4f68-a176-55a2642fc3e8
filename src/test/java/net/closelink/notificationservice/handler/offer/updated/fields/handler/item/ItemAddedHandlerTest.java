package net.closelink.notificationservice.handler.offer.updated.fields.handler.item;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.objects.Item;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.service.rest.product.ProductApiRestService;
import net.closelink.notificationservice.service.rest.product.domain.ProductMessage;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class ItemAddedHandlerTest {

    private ItemAddedHandler service;
    private BaseActivityLogService activityLogService;
    private ProductApiRestService ApiRestService;

    @Before
    public void setUp() {
        this.activityLogService = Mockito.mock(BaseActivityLogService.class);
        this.ApiRestService = Mockito.mock(ProductApiRestService.class);

        this.service = new ItemAddedHandler(this.activityLogService, this.ApiRestService);
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.activityLogService);
        Mockito.verifyNoMoreInteractions(this.ApiRestService);
    }

    @Test
    public void handleTest() {
        final Item newValue = DataCreator.createItem();
        final ProductMessage productMessage = DataCreator.createProductMessage();

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(GsonCoder.decode(newValue));

        when(this.ApiRestService.getProduct(newValue.getProductId())).thenReturn(productMessage);

        this.service.handle(updateEvent);

        final ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.ApiRestService).getProduct(newValue.getProductId());
        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.itemadded"),
            replacementsCapture.capture()
        );

        assertEquals(productMessage.getName(), replacementsCapture.getValue()[0]);
    }
}
