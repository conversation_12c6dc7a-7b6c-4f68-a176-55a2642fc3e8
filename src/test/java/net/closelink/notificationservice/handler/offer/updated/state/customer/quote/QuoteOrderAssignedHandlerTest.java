package net.closelink.notificationservice.handler.offer.updated.state.customer.quote;

import static org.junit.Assert.assertEquals;

import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerHandlerTest;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.Before;
import org.junit.Test;

public class QuoteOrderAssignedHandlerTest extends CustomerHandlerTest {

    private static final String MESSAGE_ORDER = "quoteOrder.message";
    private static final String TEMPLATE_ORDER = "Order_Supplier_Enquiry_To_Order";

    private QuoteOrderAssignedHandler quoteOrderAssignedHandler;

    @Override
    @Before
    public void setUp() {
        super.setUp();
        this.quoteOrderAssignedHandler = new QuoteOrderAssignedHandler(this.customerStateHandlerSupport);
    }

    @Test
    public void shouldNotifyWithoutBuyerRef() {
        DataCreator.makeSpotOrder(this.offer);
        this.offer.setBuyerReference(null);

        final OfferUpdatedEvent resultEvent = this.quoteOrderAssignedHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(MESSAGE_ORDER, notification.getMessage());
        assertEquals(TEMPLATE_ORDER, notification.getTemplate());
        assertEquals("quoteOrder.subject", notification.getSubject());
    }

    @Test
    public void shouldNotifyWithBuyerRef() {
        DataCreator.makeSpotOrder(this.offer);
        this.offer.setBuyerReference("Test 123");

        final OfferUpdatedEvent resultEvent = this.quoteOrderAssignedHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(MESSAGE_ORDER, notification.getMessage());
        assertEquals(TEMPLATE_ORDER, notification.getTemplate());
        assertEquals("quoteOrderBuyerRef.subject", notification.getSubject());
    }
}
