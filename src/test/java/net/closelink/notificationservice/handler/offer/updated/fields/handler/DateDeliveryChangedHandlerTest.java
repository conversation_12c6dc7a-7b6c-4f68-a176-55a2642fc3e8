package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static net.closelink.notificationservice.util.DateUtilKt.parseOffsetDateTimeOrNull;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class DateDeliveryChangedHandlerTest {

    private DateDeliveryChangedHandler service;
    private BaseActivityLogService activityLogService;

    @Before
    public void setUp() {
        this.activityLogService = mock(BaseActivityLogService.class);

        this.service = new DateDeliveryChangedHandler(this.activityLogService);
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.activityLogService);
    }

    @Test
    public void handleTest() {
        final OffsetDateTime newValue = DataCreator.createOffsetDateTime();
        final OffsetDateTime oldValue = DataCreator.createOffsetDateTime();

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(GsonCoder.decode(newValue));
        updateEvent.getEventMessage().setOldValue(GsonCoder.decode(oldValue));

        this.service.handle(updateEvent);

        final ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.deliverydate"),
            replacementsCapture.capture()
        );

        assertEquals(
            newValue.truncatedTo(ChronoUnit.SECONDS),
            OffsetDateTime.parse(replacementsCapture.getValue()[0].toString()).truncatedTo(ChronoUnit.SECONDS)
        );
        assertEquals(
            oldValue.truncatedTo(ChronoUnit.SECONDS),
            OffsetDateTime.parse(replacementsCapture.getValue()[1].toString()).truncatedTo(ChronoUnit.SECONDS)
        );
    }
}
