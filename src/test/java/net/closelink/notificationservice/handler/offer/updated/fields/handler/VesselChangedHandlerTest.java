package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static net.closelink.notificationservice.data.DataCreator.createVesselMessage;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class VesselChangedHandlerTest {

    private VesselChangedHandler service;
    private BaseActivityLogService activityLogService;
    private VesselApiRestService ApiRestService;

    @Before
    public void setUp() {
        this.activityLogService = Mockito.mock(BaseActivityLogService.class);
        this.ApiRestService = Mockito.mock(VesselApiRestService.class);

        this.service = new VesselChangedHandler(this.activityLogService, this.ApiRestService);
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.activityLogService);
        Mockito.verifyNoMoreInteractions(this.ApiRestService);
    }

    @Test
    public void handleTest() {
        final VesselMessage newValue = createVesselMessage();
        final VesselMessage oldValue = createVesselMessage();

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(newValue.getId());
        updateEvent.getEventMessage().setOldValue(oldValue.getId());

        when(this.ApiRestService.getVessel(newValue.getId())).thenReturn(newValue);
        when(this.ApiRestService.getVessel(oldValue.getId())).thenReturn(oldValue);

        this.service.handle(updateEvent);

        final ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.ApiRestService).getVessel(newValue.getId());
        verify(this.ApiRestService).getVessel(oldValue.getId());
        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.vessel"),
            replacementsCapture.capture()
        );

        assertEquals(oldValue.getName(), replacementsCapture.getValue()[0]);
        assertEquals(newValue.getName(), replacementsCapture.getValue()[1]);
    }
}
