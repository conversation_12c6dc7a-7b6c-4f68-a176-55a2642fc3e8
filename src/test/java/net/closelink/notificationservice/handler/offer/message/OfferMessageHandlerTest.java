package net.closelink.notificationservice.handler.offer.message;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.Map;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.mapper.EnumMapper;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.data.MessageSourceCreator;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.replacement.offer.OfferReplacementService;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementOffer;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.notification.NotificationPreferenceService;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.message.MessageApiRestService;
import net.closelink.notificationservice.service.rest.message.domain.MessageMessage;
import net.closelink.notificationservice.service.rest.message.domain.MessageReceiverType;
import net.closelink.notificationservice.service.rest.message.domain.RecipientMessage;
import net.closelink.notificationservice.service.rest.order.OrderApiRestService;
import net.closelink.notificationservice.service.rest.order.domain.OrderMessage;
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.MailNotificationSettings;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.context.MessageSource;

public class OfferMessageHandlerTest {

    private OfferMessageHandler service;
    private MessageApiRestService messageApiRestService;
    private OrderApiRestService orderApiRestService;
    private VesselApiRestService vesselApiRestService;
    private CustomerApiRestService customerApiRestService;
    private SupplierApiRestService supplierApiRestService;
    private OfferReplacementService offerReplacementService;
    private WebAppReplacementService webappReplacementService;
    private NotificationPreferenceService notificationPreferenceService;
    private MessageMessage message;
    private Offer offer;
    private Map<String, String> replacements;
    private VesselMessage vessel;
    private SupplierMessage supplier;
    private CustomerMessage customer;
    private OrderMessage order;

    @Before
    public void setUp() {
        this.messageApiRestService = Mockito.mock(MessageApiRestService.class);
        this.orderApiRestService = Mockito.mock(OrderApiRestService.class);
        this.vesselApiRestService = Mockito.mock(VesselApiRestService.class);
        this.customerApiRestService = Mockito.mock(CustomerApiRestService.class);
        this.supplierApiRestService = Mockito.mock(SupplierApiRestService.class);
        this.offerReplacementService = Mockito.mock(OfferReplacementService.class);
        this.webappReplacementService = Mockito.mock(WebAppReplacementService.class);
        this.notificationPreferenceService = Mockito.mock(NotificationPreferenceService.class);
        MessageSource messageSource = MessageSourceCreator.createMessageSource();

        this.service = new OfferMessageHandler(
            messageSource,
            this.messageApiRestService,
            this.orderApiRestService,
            this.vesselApiRestService,
            this.customerApiRestService,
            this.supplierApiRestService,
            this.offerReplacementService,
            this.webappReplacementService,
            this.notificationPreferenceService
        );

        this.message = DataCreator.createMessageMessage();
        this.offer = DataCreator.createOffer();
        this.order = DataCreator.createOrderMessage();
        this.replacements = DataCreator.createStringMap();
        this.vessel = DataCreator.createVesselMessage();
        this.supplier = DataCreator.createSupplierMessage();
        this.customer = DataCreator.createCustomerMessage();

        when(this.messageApiRestService.getMessage(this.message.getId())).thenReturn(this.message);
        when(notificationPreferenceService.shouldSendNotification(any(), any(), any())).thenReturn(true);
    }

    @After
    public void afterTest() {
        verify(this.messageApiRestService).getMessage(this.message.getId());
        Mockito.verifyNoMoreInteractions(this.messageApiRestService);
        Mockito.verifyNoMoreInteractions(this.orderApiRestService);
        Mockito.verifyNoMoreInteractions(this.vesselApiRestService);
        Mockito.verifyNoMoreInteractions(this.customerApiRestService);
        Mockito.verifyNoMoreInteractions(this.supplierApiRestService);
        Mockito.verifyNoMoreInteractions(this.offerReplacementService);
        Mockito.verifyNoMoreInteractions(this.webappReplacementService);
    }

    @Test
    public void sendOfferMessageToSupplierTestWithoutBuyerRef() {
        this.message.setReceiverType(MessageReceiverType.SUPPLIER);
        this.offer.setState(OfferState.ORDER);
        this.offer.setOrderId(null);
        this.offer.setBuyerReference(null);

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);

        this.service.handle(event);

        verifyOrderSendMessage("New_Messages_Supplier_Customer_Order", createSupplierSubject(false));

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(
            any(ReplacementOffer.class),
            eq(EnumMapper.map(ReceiverType.class, this.message.getReceiverType()))
        );
        verify(this.customerApiRestService).getCustomer(this.offer.getCustomerId());
    }

    @Test
    public void sendOfferMessageToSupplierTestWithBuyerRef() {
        this.message.setReceiverType(MessageReceiverType.SUPPLIER);
        this.offer.setState(OfferState.ORDER);
        this.offer.setOrderId(null);
        this.offer.setBuyerReference("Test 123");

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);

        this.service.handle(event);

        verifyOrderSendMessage("New_Messages_Supplier_Customer_Order", createSupplierSubject(true));

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(
            any(ReplacementOffer.class),
            eq(EnumMapper.map(ReceiverType.class, this.message.getReceiverType()))
        );
        verify(this.customerApiRestService).getCustomer(this.offer.getCustomerId());
    }

    @Test
    public void sendOfferMessageToSupplierEnquiryTestWithoutBuyerRef() {
        this.message.setReceiverType(MessageReceiverType.SUPPLIER);
        this.offer.setState(OfferState.ENQUIRY);
        this.offer.setOrderId(null);
        this.offer.setBuyerReference(null);

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);

        this.service.handle(event);

        verifyOrderSendMessage("New_Messages_Supplier_Customer_Enquiry", createSupplierSubject(false));

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(
            any(ReplacementOffer.class),
            eq(EnumMapper.map(ReceiverType.class, this.message.getReceiverType()))
        );
        verify(this.customerApiRestService).getCustomer(this.offer.getCustomerId());
    }

    @Test
    public void sendOfferMessageToSupplierEnquiryTestWithBuyerRef() {
        this.message.setReceiverType(MessageReceiverType.SUPPLIER);
        this.offer.setState(OfferState.ENQUIRY);
        this.offer.setOrderId(null);
        this.offer.setBuyerReference("Test 123");

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);

        this.service.handle(event);

        verifyOrderSendMessage("New_Messages_Supplier_Customer_Enquiry", createSupplierSubject(true));

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(
            any(ReplacementOffer.class),
            eq(EnumMapper.map(ReceiverType.class, this.message.getReceiverType()))
        );
        verify(this.customerApiRestService).getCustomer(this.offer.getCustomerId());
    }

    @Test
    public void sendOfferMessageToSupplierOrderEnquiryTest() {
        this.message.setReceiverType(MessageReceiverType.SUPPLIER);
        this.offer.setState(OfferState.ENQUIRY);
        this.offer.setOrderId(DataCreator.createId());
        this.offer.setEnquiryType(EnquiryType.SPOT);

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        this.service.handle(event);
    }

    @Test
    public void sendOfferMessageToSupplierOrderQuotedTestWithoutBuyerRef() {
        this.message.setReceiverType(MessageReceiverType.SUPPLIER);
        this.offer.setState(OfferState.QUOTED);
        this.offer.setOrderId(DataCreator.createId());
        this.offer.setEnquiryType(EnquiryType.SPOT);
        this.offer.setBuyerReference(null);

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);

        this.service.handle(event);

        verifyOrderSendMessage("New_Messages_Supplier_Customer_Trade_Enquiry", createSupplierSubject(false));

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(
            any(ReplacementOffer.class),
            eq(EnumMapper.map(ReceiverType.class, this.message.getReceiverType()))
        );
        verify(this.customerApiRestService).getCustomer(this.offer.getCustomerId());
    }

    @Test
    public void sendOfferMessageToSupplierOrderQuotedTestWithBuyerRef() {
        this.message.setReceiverType(MessageReceiverType.SUPPLIER);
        this.offer.setState(OfferState.QUOTED);
        this.offer.setOrderId(DataCreator.createId());
        this.offer.setEnquiryType(EnquiryType.SPOT);
        this.offer.setBuyerReference("Test 123");

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);

        this.service.handle(event);

        verifyOrderSendMessage("New_Messages_Supplier_Customer_Trade_Enquiry", createSupplierSubject(true));

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(
            any(ReplacementOffer.class),
            eq(EnumMapper.map(ReceiverType.class, this.message.getReceiverType()))
        );
        verify(this.customerApiRestService).getCustomer(this.offer.getCustomerId());
    }

    @Test
    public void sendOfferMessageToCustomerOrderTestWithoutBuyerRef() {
        this.message.setReceiverType(MessageReceiverType.CUSTOMER);
        this.offer.setState(OfferState.ORDER);
        this.offer.setOrderId(null);
        this.offer.setBuyerReference(null);

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);

        this.service.handle(event);

        verifyOrderSendMessage("New_Messages_Customer_Supplier_Order", createCustomerSubject(false));

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(
            any(ReplacementOffer.class),
            eq(EnumMapper.map(ReceiverType.class, this.message.getReceiverType()))
        );
        verify(this.supplierApiRestService).getSupplier(this.offer.getSupplierId());
    }

    @Test
    public void shouldSendOfferMessageToVesselForOrderStateAndEnabledSettings() {
        this.message.setReceiverType(MessageReceiverType.CUSTOMER);
        this.offer.setState(OfferState.ORDER);
        this.offer.setOrderId(null);
        this.offer.setBuyerReference(null);

        this.vessel.setMailNotificationSettings(
                MailNotificationSettings.builder()
                    .orderUpdateSettings(
                        MailNotificationSettings.NotificationSettings.builder()
                            .enabled(true)
                            .sendToVessel(false)
                            .sendToOthers(true)
                            .recipients(Collections.singletonList("<EMAIL>"))
                            .build()
                    )
                    .build()
            );

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);

        this.service.handle(event);

        final ArgumentCaptor<MessageMessage> messageCapture = ArgumentCaptor.forClass(MessageMessage.class);
        Mockito.verify(this.messageApiRestService).createMessage(messageCapture.capture());

        final MessageMessage resultMessage = messageCapture.getValue();
        assertEquals(createCustomerSubject(false), resultMessage.getSubject());
        assertEquals("New_Messages_Customer_Supplier_Order", resultMessage.getTemplate());
        assertEquals(this.replacements, resultMessage.getReplacements());
        assertTrue(resultMessage.isGroupMail());
        assertTrue(resultMessage.isSendMail());
        assertTrue(resultMessage.isHidden());
        assertEquals(
            Collections.singletonList(RecipientMessage.builder().emailAddress("<EMAIL>").build()),
            resultMessage.getRecipients()
        );
        assertEquals(MessageReceiverType.CUSTOM, resultMessage.getReceiverType());

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(any(ReplacementOffer.class), eq(ReceiverType.CUSTOMER));
        verify(this.supplierApiRestService).getSupplier(this.offer.getSupplierId());
    }

    @Test
    public void sendOfferMessageToCustomerOrderTestWithBuyerRef() {
        this.message.setReceiverType(MessageReceiverType.CUSTOMER);
        this.offer.setState(OfferState.ORDER);
        this.offer.setOrderId(null);
        this.offer.setBuyerReference("Test 123");

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);

        this.service.handle(event);

        verifyOrderSendMessage("New_Messages_Customer_Supplier_Order", createCustomerSubject(true));

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(
            any(ReplacementOffer.class),
            eq(EnumMapper.map(ReceiverType.class, this.message.getReceiverType()))
        );
        verify(this.supplierApiRestService).getSupplier(this.offer.getSupplierId());
    }

    @Test
    public void sendOfferMessageToCustomerEnquiryTestWithoutBuyerRef() {
        this.message.setReceiverType(MessageReceiverType.CUSTOMER);
        this.offer.setState(OfferState.ENQUIRY);
        this.offer.setOrderId(null);
        this.offer.setBuyerReference(null);

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);

        this.service.handle(event);

        verifyOrderSendMessage("New_Messages_Customer_Supplier_Enquiry", createCustomerSubject(false));

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(
            any(ReplacementOffer.class),
            eq(EnumMapper.map(ReceiverType.class, this.message.getReceiverType()))
        );
        verify(this.supplierApiRestService).getSupplier(this.offer.getSupplierId());
    }

    @Test
    public void shouldSendOfferMessageToVesselForEnquiryState() {
        this.message.setReceiverType(MessageReceiverType.CUSTOMER);
        this.offer.setState(OfferState.ENQUIRY);
        this.offer.setOrderId(null);
        this.offer.setBuyerReference(null);

        this.vessel.setMailNotificationSettings(
                MailNotificationSettings.builder()
                    .orderUpdateSettings(
                        MailNotificationSettings.NotificationSettings.builder()
                            .enabled(true)
                            .sendToVessel(false)
                            .sendToOthers(true)
                            .recipients(Collections.singletonList("<EMAIL>"))
                            .build()
                    )
                    .build()
            );

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);

        this.service.handle(event);

        final ArgumentCaptor<MessageMessage> messageCapture = ArgumentCaptor.forClass(MessageMessage.class);
        Mockito.verify(this.messageApiRestService).createMessage(messageCapture.capture());

        final MessageMessage resultMessage = messageCapture.getValue();
        assertEquals(createCustomerSubject(false), resultMessage.getSubject());
        assertEquals("New_Messages_Customer_Supplier_Enquiry", resultMessage.getTemplate());
        assertEquals(this.replacements, resultMessage.getReplacements());
        assertTrue(resultMessage.isGroupMail());
        assertTrue(resultMessage.isSendMail());
        assertTrue(resultMessage.isHidden());
        assertEquals(
            Collections.singletonList(RecipientMessage.builder().emailAddress("<EMAIL>").build()),
            resultMessage.getRecipients()
        );
        assertEquals(MessageReceiverType.CUSTOM, resultMessage.getReceiverType());

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(any(ReplacementOffer.class), eq(ReceiverType.CUSTOMER));
        verify(this.supplierApiRestService).getSupplier(this.offer.getSupplierId());
    }

    @Test
    public void sendOfferMessageToCustomerEnquiryTestWithBuyerRef() {
        this.message.setReceiverType(MessageReceiverType.CUSTOMER);
        this.offer.setState(OfferState.ENQUIRY);
        this.offer.setOrderId(null);
        this.offer.setBuyerReference("Test 123");

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);

        this.service.handle(event);

        verifyOrderSendMessage("New_Messages_Customer_Supplier_Enquiry", createCustomerSubject(true));

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(
            any(ReplacementOffer.class),
            eq(EnumMapper.map(ReceiverType.class, this.message.getReceiverType()))
        );
        verify(this.supplierApiRestService).getSupplier(this.offer.getSupplierId());
    }

    @Test
    public void sendOfferMessageToCustomerOrderQuotedTestWithoutBuyerRef() {
        this.message.setReceiverType(MessageReceiverType.CUSTOMER);
        this.offer.setState(OfferState.QUOTED);
        this.offer.setOrderId(DataCreator.createId());
        this.offer.setEnquiryType(EnquiryType.SPOT);
        this.offer.setBuyerReference(null);

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);
        when(this.orderApiRestService.getOrder(this.offer.getOrderId())).thenReturn(this.order);

        this.service.handle(event);

        verifyOrderSendMessage("New_Messages_Customer_Supplier_Trade_Enquiry", createCustomerOrderSubject(false));

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(
            any(ReplacementOffer.class),
            eq(EnumMapper.map(ReceiverType.class, this.message.getReceiverType()))
        );
        verify(this.supplierApiRestService).getSupplier(this.offer.getSupplierId());
        Mockito.verify(this.orderApiRestService).getOrder(this.offer.getOrderId());
    }

    @Test
    public void shouldSendOfferMessageToVesselForQuotedSpot() {
        this.message.setReceiverType(MessageReceiverType.CUSTOMER);
        this.offer.setState(OfferState.QUOTED);
        this.offer.setOrderId(DataCreator.createId());
        this.offer.setEnquiryType(EnquiryType.SPOT);
        this.offer.setBuyerReference(null);

        this.vessel.setMailNotificationSettings(
                MailNotificationSettings.builder()
                    .orderUpdateSettings(
                        MailNotificationSettings.NotificationSettings.builder()
                            .enabled(true)
                            .sendToVessel(false)
                            .sendToOthers(true)
                            .recipients(Collections.singletonList("<EMAIL>"))
                            .build()
                    )
                    .build()
            );

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);
        when(this.orderApiRestService.getOrder(this.offer.getOrderId())).thenReturn(this.order);

        this.service.handle(event);

        final ArgumentCaptor<MessageMessage> messageCapture = ArgumentCaptor.forClass(MessageMessage.class);
        Mockito.verify(this.messageApiRestService).createMessage(messageCapture.capture());

        final MessageMessage resultMessage = messageCapture.getValue();
        assertEquals(createCustomerOrderSubject(false), resultMessage.getSubject());
        assertEquals("New_Messages_Customer_Supplier_Trade_Enquiry", resultMessage.getTemplate());
        assertEquals(this.replacements, resultMessage.getReplacements());
        assertTrue(resultMessage.isGroupMail());
        assertTrue(resultMessage.isSendMail());
        assertTrue(resultMessage.isHidden());
        assertEquals(
            Collections.singletonList(RecipientMessage.builder().emailAddress("<EMAIL>").build()),
            resultMessage.getRecipients()
        );
        assertEquals(MessageReceiverType.CUSTOM, resultMessage.getReceiverType());

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(any(ReplacementOffer.class), eq(ReceiverType.CUSTOMER));
        verify(this.supplierApiRestService).getSupplier(this.offer.getSupplierId());
        verify(this.orderApiRestService).getOrder(this.offer.getOrderId());
    }

    @Test
    public void sendOfferMessageToCustomerOrderQuotedTestWithBuyerRef() {
        this.message.setReceiverType(MessageReceiverType.CUSTOMER);
        this.offer.setState(OfferState.QUOTED);
        this.offer.setOrderId(DataCreator.createId());
        this.offer.setEnquiryType(EnquiryType.SPOT);
        this.offer.setBuyerReference("Test 123");

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);
        when(this.orderApiRestService.getOrder(this.offer.getOrderId())).thenReturn(this.order);

        this.service.handle(event);

        verifyOrderSendMessage("New_Messages_Customer_Supplier_Trade_Enquiry", createCustomerOrderSubject(true));

        verify(this.vesselApiRestService).getVessel(this.offer.getVesselId());
        verify(this.offerReplacementService).create(any(ReplacementOffer.class));
        verify(this.webappReplacementService).create(
            any(ReplacementOffer.class),
            eq(EnumMapper.map(ReceiverType.class, this.message.getReceiverType()))
        );
        verify(this.supplierApiRestService).getSupplier(this.offer.getSupplierId());
        Mockito.verify(this.orderApiRestService).getOrder(this.offer.getOrderId());
    }

    @Test
    public void sendOfferMessageToCustomerOrderForwardedEnquiryTest() {
        this.message.setReceiverType(MessageReceiverType.CUSTOMER);
        this.offer.setState(OfferState.ENQUIRY);
        this.offer.setOrderId(DataCreator.createId());
        this.offer.setEnquiryType(EnquiryType.FORWARDED);

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(this.vesselApiRestService.getVessel(this.offer.getVesselId())).thenReturn(this.vessel);
        when(this.customerApiRestService.getCustomer(this.offer.getCustomerId())).thenReturn(this.customer);
        when(this.supplierApiRestService.getSupplier(this.offer.getSupplierId())).thenReturn(this.supplier);
        when(this.offerReplacementService.create(any(ReplacementOffer.class))).thenReturn(this.replacements);
        when(this.orderApiRestService.getOrder(this.offer.getOrderId())).thenReturn(this.order);

        this.service.handle(event);

        verify(messageApiRestService).getMessage(message.getId());
        verifyNoMoreInteractions(messageApiRestService);
        verifyNoInteractions(supplierApiRestService);
    }

    @Test
    public void shouldNotDoAnythingForOrderTypeFuel() {
        this.message.setReceiverType(MessageReceiverType.SUPPLIER);
        this.offer.setState(OfferState.ORDER);
        this.offer.setType(OrderType.FUEL);

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        this.service.handle(event);
        verifyNoInteractions(this.customerApiRestService);
        verifyNoInteractions(this.supplierApiRestService);
    }

    @Test
    public void shouldNotDoAnythingIfNotificationsAreDisabled() {
        this.message.setReceiverType(MessageReceiverType.SUPPLIER);
        this.offer.setState(OfferState.ORDER);
        this.offer.setType(OrderType.LUBES);

        final Event event = DataCreator.createEvent(
            DataCreator.createOfferMessageEventMessage(this.offer, this.message.getId())
        );

        when(
            notificationPreferenceService.shouldSendNotification(
                ReceiverType.SUPPLIER,
                this.message.getReceiverId(),
                NotificationCategory.NEW_CHAT_MESSAGE
            )
        ).thenReturn(false);

        this.service.handle(event);
        verifyNoInteractions(this.customerApiRestService);
        verifyNoInteractions(this.supplierApiRestService);
    }

    private void verifyOrderSendMessage(final String template, final String subject) {
        final ArgumentCaptor<MessageMessage> messageCapture = ArgumentCaptor.forClass(MessageMessage.class);
        Mockito.verify(this.messageApiRestService).createMessage(messageCapture.capture());

        final MessageMessage resultMessage = messageCapture.getValue();
        assertEquals(subject, resultMessage.getSubject());
        assertEquals(template, resultMessage.getTemplate());
        assertEquals(this.replacements, resultMessage.getReplacements());
        assertTrue(resultMessage.isGroupMail());
        assertTrue(resultMessage.isSendMail());
        assertTrue(resultMessage.isHidden());
    }

    private String createCustomerSubject(boolean includeBuyerReference) {
        if (includeBuyerReference) {
            return (
                this.vessel.getName() +
                " - " +
                this.offer.getBuyerReference() +
                " - " +
                this.offer.getOfferNumber() +
                " - New Message from " +
                this.supplier.getName()
            );
        }

        return (
            this.vessel.getName() +
            " - " +
            this.offer.getOfferNumber() +
            " - New Message from " +
            this.supplier.getName()
        );
    }

    private String createCustomerOrderSubject(boolean includeBuyerReference) {
        if (includeBuyerReference) {
            return (
                this.vessel.getName() +
                " - " +
                this.offer.getBuyerReference() +
                " - " +
                this.order.getOrderNumber() +
                " - New Message from " +
                this.supplier.getName()
            );
        }

        return (
            this.vessel.getName() +
            " - " +
            this.order.getOrderNumber() +
            " - New Message from " +
            this.supplier.getName()
        );
    }

    private String createSupplierSubject(boolean includeBuyerReference) {
        if (includeBuyerReference) {
            return (
                this.vessel.getName() +
                " - " +
                this.offer.getBuyerReference() +
                " - " +
                this.offer.getOfferNumber() +
                " - New Message from " +
                this.customer.getName()
            );
        }

        return (
            this.vessel.getName() +
            " - " +
            this.offer.getOfferNumber() +
            " - New Message from " +
            this.customer.getName()
        );
    }
}
