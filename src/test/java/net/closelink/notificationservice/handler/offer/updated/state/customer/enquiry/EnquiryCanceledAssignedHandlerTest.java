package net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;

import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerHandlerTest;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class EnquiryCanceledAssignedHandlerTest extends CustomerHandlerTest {

    private EnquiryCanceledAssignedHandler enquiryCanceledAssignedHandler;

    @BeforeEach
    public void setUp() {
        super.setUp();

        enquiryCanceledAssignedHandler = new EnquiryCanceledAssignedHandler(this.customerStateHandlerSupport);
    }

    @Test
    void shouldSendNotificationWithBuyerRef() {
        final String cancelReason = DataCreator.createString();
        this.offer.setBuyerReference("A Buyer Ref");

        Mockito.when(this.customerStateHandlerSupport.createCancelMessageReplacements(this.offer)).thenReturn(
            new String[] { cancelReason }
        );

        final OfferUpdatedEvent resultEvent = this.enquiryCanceledAssignedHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(cancelReason, notification.getMessageReplacements()[0]);
        assertEquals("enquiryCustomerCancel.message", notification.getMessage());
        assertEquals("Order_Supplier_Enquiry_Canceled", notification.getTemplate());
        assertEquals("enquiryCustomerCancelBuyerRef.subject", notification.getSubject());

        verify(this.customerStateHandlerSupport).createCancelMessageReplacements(this.offer);
    }

    @Test
    void shouldSendNotificationWithoutBuyerRef() {
        final String cancelReason = DataCreator.createString();
        this.offer.setBuyerReference(null);

        Mockito.when(this.customerStateHandlerSupport.createCancelMessageReplacements(this.offer)).thenReturn(
            new String[] { cancelReason }
        );

        final OfferUpdatedEvent resultEvent = this.enquiryCanceledAssignedHandler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(cancelReason, notification.getMessageReplacements()[0]);
        assertEquals("enquiryCustomerCancel.message", notification.getMessage());
        assertEquals("Order_Supplier_Enquiry_Canceled", notification.getTemplate());
        assertEquals("enquiryCustomerCancel.subject", notification.getSubject());

        verify(this.customerStateHandlerSupport).createCancelMessageReplacements(this.offer);
    }
}
