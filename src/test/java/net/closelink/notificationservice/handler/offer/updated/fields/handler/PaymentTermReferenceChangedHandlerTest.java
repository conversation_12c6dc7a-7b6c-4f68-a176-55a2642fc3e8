package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Map;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.service.rest.coredata.CoreDataService;
import net.closelink.notificationservice.service.rest.coredata.EnumMessage;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PaymentTermReferenceChangedHandlerTest {

    @InjectMocks
    private PaymentTermReferenceChangedHandler service;

    @Mock
    private BaseActivityLogService activityLogService;

    @Mock
    private CoreDataService coreDataService;

    @AfterEach
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.activityLogService);
    }

    @Test
    public void handlePrepaymentToOther() {
        var oldValue = DataCreator.createOffer();
        oldValue.setPaymentTermReference("PREPAYMENT");
        var newValue = DataCreator.createOffer();
        newValue.setPaymentTermReference("DAYS_AFTER_DELIVERY");

        var updateEvent = createEvent(oldValue, newValue);

        var paymentTermReferenceMap = Map.of(
            "PREPAYMENT",
            EnumMessage.DetailEnumMessage.builder().humanReadableValue("Prepayment").build(),
            "DAYS_AFTER_DELIVERY",
            EnumMessage.DetailEnumMessage.builder().humanReadableValue("Days after Delivery").build()
        );
        when(coreDataService.getHumanReadableValueForPaymentTermReference()).thenReturn(paymentTermReferenceMap);

        this.service.handle(updateEvent);

        var replacementsCapture = ArgumentCaptor.forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.paymenttermreferencedays"),
            replacementsCapture.capture()
        );

        assertEquals(
            paymentTermReferenceMap.get("PREPAYMENT").getHumanReadableValue(),
            replacementsCapture.getValue()[0]
        );
        assertEquals(
            String.format(
                "%s %s",
                newValue.getPaymentTermReferenceDays(),
                paymentTermReferenceMap.get("DAYS_AFTER_DELIVERY").getHumanReadableValue()
            ),
            replacementsCapture.getValue()[1]
        );
    }

    @Test
    public void handleOtherToPrepayment() {
        var oldValue = DataCreator.createOffer();
        oldValue.setPaymentTermReference("DAYS_AFTER_DELIVERY");
        var newValue = DataCreator.createOffer();
        newValue.setPaymentTermReference("PREPAYMENT");

        var updateEvent = createEvent(oldValue, newValue);

        var paymentTermReferenceMap = Map.of(
            "PREPAYMENT",
            EnumMessage.DetailEnumMessage.builder().humanReadableValue("Prepayment").build(),
            "DAYS_AFTER_DELIVERY",
            EnumMessage.DetailEnumMessage.builder().humanReadableValue("Days after Delivery").build()
        );
        when(coreDataService.getHumanReadableValueForPaymentTermReference()).thenReturn(paymentTermReferenceMap);

        this.service.handle(updateEvent);

        var replacementsCapture = ArgumentCaptor.forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.paymenttermreferencedays"),
            replacementsCapture.capture()
        );

        assertEquals(
            String.format(
                "%s %s",
                oldValue.getPaymentTermReferenceDays(),
                paymentTermReferenceMap.get("DAYS_AFTER_DELIVERY").getHumanReadableValue()
            ),
            replacementsCapture.getValue()[0]
        );
        assertEquals(
            paymentTermReferenceMap.get("PREPAYMENT").getHumanReadableValue(),
            replacementsCapture.getValue()[1]
        );
    }

    @Test
    public void handleZeroDaysToPrepayment() {
        var oldValue = DataCreator.createOffer();
        oldValue.setPaymentTermReference("PREPAYMENT");
        oldValue.setPaymentTermReferenceDays(0L);
        var newValue = DataCreator.createOffer();
        newValue.setPaymentTermReference("PREPAYMENT");
        newValue.setPaymentTermReferenceDays(0L);

        var updateEvent = createEvent(oldValue, newValue);

        var paymentTermReferenceMap = Map.of(
            "PREPAYMENT",
            EnumMessage.DetailEnumMessage.builder().humanReadableValue("Prepayment").build()
        );
        when(coreDataService.getHumanReadableValueForPaymentTermReference()).thenReturn(paymentTermReferenceMap);

        this.service.handle(updateEvent);

        var replacementsCapture = ArgumentCaptor.forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.paymenttermreferencedays"),
            replacementsCapture.capture()
        );

        assertEquals(
            String.format("%s", paymentTermReferenceMap.get("PREPAYMENT").getHumanReadableValue()),
            replacementsCapture.getValue()[0]
        );
        assertEquals(
            String.format("%s", paymentTermReferenceMap.get("PREPAYMENT").getHumanReadableValue()),
            replacementsCapture.getValue()[1]
        );
    }

    @Test
    public void handleOtherToOther() {
        var oldValue = DataCreator.createOffer();
        oldValue.setPaymentTermReference("DAYS_AFTER_DELIVERY");
        var newValue = DataCreator.createOffer();
        newValue.setPaymentTermReference("DAYS_AFTER_INVOICE_DATE");

        var updateEvent = createEvent(oldValue, newValue);

        var paymentTermReferenceMap = Map.of(
            "DAYS_AFTER_DELIVERY",
            EnumMessage.DetailEnumMessage.builder().humanReadableValue("Days after Delivery").build(),
            "DAYS_AFTER_INVOICE_DATE",
            EnumMessage.DetailEnumMessage.builder().humanReadableValue("Days after Invoice Date").build()
        );
        when(coreDataService.getHumanReadableValueForPaymentTermReference()).thenReturn(paymentTermReferenceMap);

        this.service.handle(updateEvent);

        var replacementsCapture = ArgumentCaptor.forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.paymenttermreferencedays"),
            replacementsCapture.capture()
        );

        assertEquals(
            String.format(
                "%s %s",
                oldValue.getPaymentTermReferenceDays(),
                paymentTermReferenceMap.get("DAYS_AFTER_DELIVERY").getHumanReadableValue()
            ),
            replacementsCapture.getValue()[0]
        );
        assertEquals(
            String.format(
                "%s %s",
                newValue.getPaymentTermReferenceDays(),
                paymentTermReferenceMap.get("DAYS_AFTER_INVOICE_DATE").getHumanReadableValue()
            ),
            replacementsCapture.getValue()[1]
        );
    }

    @Test
    public void handleNullTest() {
        var oldValue = DataCreator.createOffer();
        oldValue.setPaymentTermReference("DAYS_AFTER_DELIVERY");
        oldValue.setPaymentTermReferenceDays(null); // Only days, if reference is null, migration or prefilling went wrong
        var newValue = DataCreator.createOffer();
        newValue.setPaymentTermReference("DAYS_AFTER_INVOICE_DATE");

        var updateEvent = createEvent(oldValue, newValue);

        var paymentTermReferenceMap = Map.of(
            "DAYS_AFTER_DELIVERY",
            EnumMessage.DetailEnumMessage.builder().humanReadableValue("Days after Delivery").build(),
            "DAYS_AFTER_INVOICE_DATE",
            EnumMessage.DetailEnumMessage.builder().humanReadableValue("Days after Invoice Date").build()
        );
        when(coreDataService.getHumanReadableValueForPaymentTermReference()).thenReturn(paymentTermReferenceMap);

        this.service.handle(updateEvent);

        var replacementsCapture = forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.paymenttermreferencedays"),
            replacementsCapture.capture()
        );

        assertEquals(
            String.format(
                "%s %s",
                oldValue.getPaymentTermReferenceDays(),
                paymentTermReferenceMap.get("DAYS_AFTER_DELIVERY").getHumanReadableValue()
            ),
            replacementsCapture.getValue()[0]
        );
        assertEquals(
            String.format(
                "%s %s",
                newValue.getPaymentTermReferenceDays(),
                paymentTermReferenceMap.get("DAYS_AFTER_INVOICE_DATE").getHumanReadableValue()
            ),
            replacementsCapture.getValue()[1]
        );
    }

    private OfferUpdatedEvent createEvent(Offer oldValue, Offer newValue) {
        OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(GsonCoder.decode(newValue));
        updateEvent.getEventMessage().setOldValue(GsonCoder.decode(oldValue));
        return updateEvent;
    }
}
