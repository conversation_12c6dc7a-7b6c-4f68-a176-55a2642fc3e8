package net.closelink.notificationservice.handler.offer.updated.state.customer.quote;

import de.tschumacher.simplestatemachine.domain.StateChange;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class QuoteOrderHandlerTest {

    private QuoteOrderSpotHandler quoteOrderSpotHandler = Mockito.mock(QuoteOrderSpotHandler.class);
    private QuoteOrderAssignedHandler quoteOrderAssignedHandler = Mockito.mock(QuoteOrderAssignedHandler.class);
    private QuoteOrderForwardedHandler quoteOrderForwardedHandler = Mockito.mock(QuoteOrderForwardedHandler.class);

    private QuoteOrderHandler quoteOrderHandler;
    private OfferUpdatedEvent event;
    private StateChange<OfferState> stateChange;

    @BeforeEach
    void setUp() {
        Offer offer = DataCreator.createOffer();
        this.event = DataCreator.createOfferUpdatedEvent(offer);
        this.stateChange = DataCreator.createOfferStateChange();
        this.quoteOrderHandler = new QuoteOrderHandler(
            quoteOrderAssignedHandler,
            quoteOrderSpotHandler,
            quoteOrderForwardedHandler
        );
    }

    @Test
    void shouldCallQuoteSpotOfferHandler() {
        this.event.getEventMessage().getOffer().setEnquiryType(EnquiryType.SPOT);
        Mockito.when(this.quoteOrderSpotHandler.handle(this.stateChange, this.event)).thenReturn(this.event);

        OfferUpdatedEvent event = quoteOrderHandler.handle(this.stateChange, this.event);

        Mockito.verify(this.quoteOrderSpotHandler).handle(this.stateChange, this.event);

        Assertions.assertThat(event).isEqualTo(this.event);
    }
}
