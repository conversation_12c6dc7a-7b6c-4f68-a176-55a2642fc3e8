package net.closelink.notificationservice.handler.offer.updated.state.supplier.all;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;

import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierHandlerTest;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class GeneralSupplierCanceledHandlerTest extends SupplierHandlerTest {

    private static final String MESSAGE = "supplierCancelled.message";
    private static final String TEMPLATE = "Order_Customer_Order_Rejected";

    private GeneralSupplierCanceledHandler handler;

    @Override
    @Before
    public void setUp() {
        super.setUp();

        this.handler = new GeneralSupplierCanceledHandler(this.supplierStateHandlerSupport);
    }

    @Test
    public void handleTestWithoutBuyerRef() {
        this.offer.setBuyerReference(null);

        final String cancelReason = DataCreator.createString();
        Mockito.when(this.supplierStateHandlerSupport.createCancelMessageReplacements(this.offer)).thenReturn(
            new String[] { cancelReason }
        );

        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(cancelReason, notification.getMessageReplacements()[0]);
        assertEquals(MESSAGE, notification.getMessage());
        assertEquals(TEMPLATE, notification.getTemplate());
        assertEquals("supplierCancelled.subject", notification.getSubject());

        verify(this.supplierStateHandlerSupport).createCancelMessageReplacements(this.offer);
    }

    @Test
    public void handleTestWithBuyerRef() {
        this.offer.setBuyerReference("Test 123");

        final String cancelReason = DataCreator.createString();
        Mockito.when(this.supplierStateHandlerSupport.createCancelMessageReplacements(this.offer)).thenReturn(
            new String[] { cancelReason }
        );

        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(cancelReason, notification.getMessageReplacements()[0]);
        assertEquals(MESSAGE, notification.getMessage());
        assertEquals(TEMPLATE, notification.getTemplate());
        assertEquals("supplierCancelledBuyerRef.subject", notification.getSubject());

        verify(this.supplierStateHandlerSupport).createCancelMessageReplacements(this.offer);
    }
}
