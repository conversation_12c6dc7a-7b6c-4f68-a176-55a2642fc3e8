package net.closelink.notificationservice.handler.offer.updated.state.supplier.all

import de.tschumacher.simplestatemachine.domain.StateChange
import io.kotest.core.spec.style.DescribeSpec
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import net.closelink.cenqueue.domainobject.offer.OfferUpdatedEventMessage
import net.closelink.cenqueue.objects.Offer
import net.closelink.cenqueue.types.OfferState
import net.closelink.notificationservice.domainobject.Notification
import net.closelink.notificationservice.domainobject.ReceiverType
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierStateHandlerSupport
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent

class ConfirmedOrderChangedHandlerTest :
    DescribeSpec({
        val supplierStateHandlerSupport = mockk<SupplierStateHandlerSupport>()
        val confirmedOrderChangedHandler = ConfirmedOrderChangedHandler(supplierStateHandlerSupport)

        describe("ConfirmedOrderChangedHandler") {
            it("should handle state change when buyer reference is not empty") {
                val offerUpdatedEvent =
                    OfferUpdatedEvent.builder()
                        .eventMessage(
                            OfferUpdatedEventMessage.builder()
                                .offer(Offer.builder().buyerReference("buyerReference").build())
                                .build()
                        )
                        .build()
                val stateChange = StateChange(OfferState.CONFIRMED, OfferState.ORDER)

                val notificationBuilder =
                    Notification.builder()
                        .receiverId("someCustomerId")
                        .receiverType(ReceiverType.CUSTOMER)

                val expectedNotification =
                    notificationBuilder
                        .message("supplierConfirmedOrder.message")
                        .subject("supplierConfirmedOrderBuyerRef.subject")
                        .template("Order_Customer_Order_Supplier_Changes")
                        .groupMail(true)
                        .build()

                every {
                    supplierStateHandlerSupport.createNotificationBuilder(offerUpdatedEvent)
                } returns notificationBuilder
                every { supplierStateHandlerSupport.notify(expectedNotification) } just Runs

                confirmedOrderChangedHandler.handle(stateChange, offerUpdatedEvent)

                verify { supplierStateHandlerSupport.createNotificationBuilder(offerUpdatedEvent) }
                verify { supplierStateHandlerSupport.notify(expectedNotification) }
            }

            it("should handle state change when buyer reference is empty") {
                val offerUpdatedEvent =
                    OfferUpdatedEvent.builder()
                        .eventMessage(
                            OfferUpdatedEventMessage.builder()
                                .offer(Offer.builder().buyerReference("").build())
                                .build()
                        )
                        .build()
                val stateChange = StateChange(OfferState.CONFIRMED, OfferState.ORDER)

                val notificationBuilder =
                    Notification.builder()
                        .receiverId("someCustomerId")
                        .receiverType(ReceiverType.CUSTOMER)

                val expectedNotification =
                    notificationBuilder
                        .message("supplierConfirmedOrder.message")
                        .subject("supplierConfirmedOrder.subject")
                        .template("Order_Customer_Order_Supplier_Changes")
                        .groupMail(true)
                        .build()

                every {
                    supplierStateHandlerSupport.createNotificationBuilder(offerUpdatedEvent)
                } returns notificationBuilder
                every { supplierStateHandlerSupport.notify(expectedNotification) } just Runs

                confirmedOrderChangedHandler.handle(stateChange, offerUpdatedEvent)

                verify { supplierStateHandlerSupport.createNotificationBuilder(offerUpdatedEvent) }
                verify { supplierStateHandlerSupport.notify(expectedNotification) }
            }
        }
    })
