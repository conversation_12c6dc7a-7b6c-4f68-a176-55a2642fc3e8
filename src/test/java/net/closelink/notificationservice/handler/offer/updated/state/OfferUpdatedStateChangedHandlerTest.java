package net.closelink.notificationservice.handler.offer.updated.state;

import static net.closelink.notificationservice.data.DataCreator.createEvent;
import static net.closelink.notificationservice.data.DataCreator.createOffer;
import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedStateEvent;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import de.tschumacher.simplestatemachine.SimpleStateMachine;
import de.tschumacher.simplestatemachine.configuration.SimpleStateMachineConfig;
import de.tschumacher.simplestatemachine.exception.TransitionNotAllowedException;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class OfferUpdatedStateChangedHandlerTest {

    private OfferUpdatedStateChangedHandler service;
    private SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> customerStateMachineConfig;
    private SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> supplierStateMachineConfig;
    private SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> systemStateMachineConfig;

    @SuppressWarnings("unchecked")
    @Before
    public void setUp() {
        this.customerStateMachineConfig = mock(SimpleStateMachineConfig.class);
        this.supplierStateMachineConfig = mock(SimpleStateMachineConfig.class);
        this.systemStateMachineConfig = mock(SimpleStateMachineConfig.class);

        this.service = new OfferUpdatedStateChangedHandler(
            this.customerStateMachineConfig,
            this.supplierStateMachineConfig,
            systemStateMachineConfig
        );
    }

    @After
    public void afterTest() {
        verifyNoMoreInteractions(this.customerStateMachineConfig);
        verifyNoMoreInteractions(this.supplierStateMachineConfig);
        verifyNoMoreInteractions(this.systemStateMachineConfig);
    }

    @Test
    @SuppressWarnings("unchecked")
    public void handleCustomerTest() {
        final UserType companyType = UserType.CUSTOMER;
        final Offer offer = createOffer();
        final Event event = createEvent(companyType);
        final OfferUpdatedEvent orderUpdatedEvent = createOrderUpdatedStateEvent(offer, event);

        final OfferState oldState = encodeOfferState(orderUpdatedEvent.getEventMessage().getOldValue());
        final OfferState newState = encodeOfferState(orderUpdatedEvent.getEventMessage().getNewValue());

        final SimpleStateMachine<OfferState, OfferUpdatedEvent> machine = Mockito.mock(SimpleStateMachine.class);
        when(this.customerStateMachineConfig.createMachine(oldState)).thenReturn(machine);

        this.service.handle(orderUpdatedEvent);

        verify(this.customerStateMachineConfig).createMachine(oldState);
        verify(machine).change(newState, orderUpdatedEvent);
        verifyNoMoreInteractions(machine);
    }

    @Test
    @SuppressWarnings("unchecked")
    public void handleSupplierTest() {
        final UserType companyType = UserType.SUPPLIER;
        final Offer offer = createOffer();
        final Event event = createEvent(companyType);
        final OfferUpdatedEvent orderUpdatedEvent = createOrderUpdatedStateEvent(offer, event);

        final OfferState oldState = encodeOfferState(orderUpdatedEvent.getEventMessage().getOldValue());
        final OfferState newState = encodeOfferState(orderUpdatedEvent.getEventMessage().getNewValue());

        final SimpleStateMachine<OfferState, OfferUpdatedEvent> machine = Mockito.mock(SimpleStateMachine.class);
        when(this.supplierStateMachineConfig.createMachine(oldState)).thenReturn(machine);

        this.service.handle(orderUpdatedEvent);

        verify(this.supplierStateMachineConfig).createMachine(oldState);
        verify(machine).change(newState, orderUpdatedEvent);
        verifyNoMoreInteractions(machine);
    }

    @Test
    @SuppressWarnings("unchecked")
    public void handleSystemTest() {
        final UserType companyType = UserType.SYSTEM;
        final Offer offer = createOffer();
        final Event event = createEvent(companyType);
        final OfferUpdatedEvent orderUpdatedEvent = createOrderUpdatedStateEvent(offer, event);

        final OfferState oldState = encodeOfferState(orderUpdatedEvent.getEventMessage().getOldValue());
        final OfferState newState = encodeOfferState(orderUpdatedEvent.getEventMessage().getNewValue());

        final SimpleStateMachine<OfferState, OfferUpdatedEvent> machine = Mockito.mock(SimpleStateMachine.class);
        when(this.systemStateMachineConfig.createMachine(oldState)).thenReturn(machine);

        this.service.handle(orderUpdatedEvent);

        verify(this.systemStateMachineConfig).createMachine(oldState);
        verify(machine).change(newState, orderUpdatedEvent);
        verifyNoMoreInteractions(machine);
    }

    @Test
    public void handleNoUserTypeHandlerTest() {
        final UserType companyType = UserType.ADMIN;
        final Offer offer = createOffer();
        final Event event = createEvent(companyType);
        final OfferUpdatedEvent orderUpdatedEvent = createOrderUpdatedStateEvent(offer, event);

        this.service.handle(orderUpdatedEvent);
    }

    @Test
    @SuppressWarnings("unchecked")
    public void handleTransitionNotAllowedTest() {
        final UserType companyType = UserType.CUSTOMER;
        final Offer offer = createOffer();
        final Event event = createEvent(companyType);
        final OfferUpdatedEvent orderUpdatedEvent = createOrderUpdatedStateEvent(offer, event);

        final OfferState oldState = encodeOfferState(orderUpdatedEvent.getEventMessage().getOldValue());
        final OfferState newState = encodeOfferState(orderUpdatedEvent.getEventMessage().getNewValue());

        final SimpleStateMachine<OfferState, OfferUpdatedEvent> machine = Mockito.mock(SimpleStateMachine.class);
        when(this.customerStateMachineConfig.createMachine(oldState)).thenReturn(machine);
        when(machine.change(newState, orderUpdatedEvent)).thenThrow(TransitionNotAllowedException.class);

        this.service.handle(orderUpdatedEvent);

        verify(this.customerStateMachineConfig).createMachine(oldState);
        verify(machine).change(newState, orderUpdatedEvent);
        verifyNoMoreInteractions(machine);
    }

    private OfferState encodeOfferState(final String value) {
        return GsonCoder.encode(value, OfferState.class);
    }
}
