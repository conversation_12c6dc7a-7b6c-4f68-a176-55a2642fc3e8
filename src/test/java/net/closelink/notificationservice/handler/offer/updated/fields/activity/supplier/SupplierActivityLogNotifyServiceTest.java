package net.closelink.notificationservice.handler.offer.updated.fields.activity.supplier;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;

import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.support.RecipientInfo;
import net.closelink.notificationservice.support.RecipientsSupportService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class SupplierActivityLogNotifyServiceTest {

    private SupplierActivityLogNotifyService service;
    private NotificationService notificationService;
    private SpotEnquiryEmailNotificationCreator spotEnquiryNotificationCreator;
    private AssignedEnquiryEmailNotificationCreator assignedEnquiryNotificationCreator;
    private ForwardedEnquiryEmailNotificationCreator forwardedEnquiryNotificationCreator;
    private DefaultOrderEmailNotificationCreator defaultOrderNotificationCreator;
    private ForwardedOrderEmailNotificationCreator forwardedOrderNotificationCreator;
    private RecipientsSupportService recipientsSupportService;

    @BeforeEach
    void setUp() {
        this.notificationService = mock();
        this.spotEnquiryNotificationCreator = mock();
        this.assignedEnquiryNotificationCreator = mock();
        this.forwardedEnquiryNotificationCreator = mock();
        this.defaultOrderNotificationCreator = mock();
        this.forwardedOrderNotificationCreator = mock();
        this.recipientsSupportService = mock();
        this.service = new DefaultSupplierActivityLogNotifyService(
            this.notificationService,
            this.spotEnquiryNotificationCreator,
            this.assignedEnquiryNotificationCreator,
            this.forwardedEnquiryNotificationCreator,
            this.defaultOrderNotificationCreator,
            this.forwardedOrderNotificationCreator,
            this.recipientsSupportService
        );
    }

    @AfterEach
    void afterTest() {
        Mockito.verifyNoMoreInteractions(this.notificationService);
    }

    @Test
    void shouldCreateSimpleNotification() {
        String supplierId = "123-456";

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(true);
        updateEvent.getEventMessage().getOffer().setSupplierId(supplierId);

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        Mockito.when(this.recipientsSupportService.buildSupplierRecipientInfo(any())).thenReturn(
            new RecipientInfo(ReceiverType.SUPPLIER, null)
        );

        this.service.notify(updateEvent, notificationBuilder);

        final ArgumentCaptor<Notification> notificationCapture = ArgumentCaptor.forClass(Notification.class);
        Mockito.verify(this.notificationService).notify(notificationCapture.capture());

        Notification actualNotification = notificationCapture.getValue();

        Assertions.assertThat(actualNotification.getMessage()).isEqualTo(message);
        Assertions.assertThat(actualNotification.isSendMail()).isFalse();
        Assertions.assertThat(actualNotification.getReceiverType()).isEqualTo(ReceiverType.SUPPLIER);
        Assertions.assertThat(actualNotification.getReceiverId()).isEqualTo(supplierId);
    }

    @Test
    void shouldCreateEmailNotificationForAssignedEnquiry() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setState(OfferState.ENQUIRY);
        offer.setEnquiryType(EnquiryType.ASSIGNED);

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder()
            .message(message)
            .receiverType(ReceiverType.SUPPLIER)
            .receiverId("123");

        String subject = "subject";
        Object[] replacements = {};
        String template = "template";

        Notification notification = notificationBuilder
            .subject("subject")
            .subjectReplacements(new Object[] {})
            .template("template")
            .sendMail(true)
            .groupMail(true)
            .build();

        Mockito.when(assignedEnquiryNotificationCreator.createNotification(notificationBuilder, offer)).thenReturn(
            notification
        );

        Mockito.when(this.recipientsSupportService.buildSupplierRecipientInfo(any())).thenReturn(
            new RecipientInfo(ReceiverType.SUPPLIER, null)
        );

        this.service.notify(updateEvent, notificationBuilder);

        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(captor.capture());

        Notification actualNotification = captor.getValue();
        Assertions.assertThat(actualNotification.getSubject()).isEqualTo(subject);
        Assertions.assertThat(actualNotification.getSubjectReplacements()).isEqualTo(replacements);
        Assertions.assertThat(actualNotification.getTemplate()).isEqualTo(template);
        Assertions.assertThat(actualNotification.isSendMail()).isTrue();
        Assertions.assertThat(actualNotification.isGroupMail()).isTrue();
        Assertions.assertThat(actualNotification.getReceiverType()).isEqualTo(ReceiverType.SUPPLIER);
        Assertions.assertThat(actualNotification.getReceiverId()).isEqualTo("123");
    }

    @Test
    void shouldCreateEmailNotificationForSpotEnquiry() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setState(OfferState.ENQUIRY);
        offer.setEnquiryType(EnquiryType.SPOT);

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder()
            .message(message)
            .receiverType(ReceiverType.SUPPLIER)
            .receiverId("123");

        String subject = "subject";
        Object[] replacements = {};
        String template = "template";

        Notification notification = notificationBuilder
            .subject("subject")
            .subjectReplacements(new Object[] {})
            .template("template")
            .sendMail(true)
            .groupMail(true)
            .build();

        Mockito.when(spotEnquiryNotificationCreator.createNotification(notificationBuilder, offer)).thenReturn(
            notification
        );

        Mockito.when(this.recipientsSupportService.buildSupplierRecipientInfo(any())).thenReturn(
            new RecipientInfo(ReceiverType.SUPPLIER, null)
        );

        this.service.notify(updateEvent, notificationBuilder);

        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(captor.capture());

        Notification actualNotification = captor.getValue();
        Assertions.assertThat(actualNotification.getSubject()).isEqualTo(subject);
        Assertions.assertThat(actualNotification.getSubjectReplacements()).isEqualTo(replacements);
        Assertions.assertThat(actualNotification.getTemplate()).isEqualTo(template);
        Assertions.assertThat(actualNotification.isSendMail()).isTrue();
        Assertions.assertThat(actualNotification.isGroupMail()).isTrue();
        Assertions.assertThat(actualNotification.getReceiverType()).isEqualTo(ReceiverType.SUPPLIER);
        Assertions.assertThat(actualNotification.getReceiverId()).isEqualTo("123");
    }

    @Test
    void shouldCreateEmailNotificationForForwardedEnquiry() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setState(OfferState.ENQUIRY);
        offer.setEnquiryType(EnquiryType.FORWARDED);

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder()
            .message(message)
            .receiverType(ReceiverType.SUPPLIER)
            .receiverId("123");

        String subject = "subject";
        Object[] replacements = {};
        String template = "template";

        Notification notification = notificationBuilder
            .subject("subject")
            .subjectReplacements(new Object[] {})
            .template("template")
            .sendMail(true)
            .groupMail(true)
            .build();

        Mockito.when(forwardedEnquiryNotificationCreator.createNotification(notificationBuilder, offer)).thenReturn(
            notification
        );

        Mockito.when(this.recipientsSupportService.buildSupplierRecipientInfo(any())).thenReturn(
            new RecipientInfo(ReceiverType.SUPPLIER, null)
        );

        this.service.notify(updateEvent, notificationBuilder);

        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(captor.capture());

        Notification actualNotification = captor.getValue();
        Assertions.assertThat(actualNotification.getSubject()).isEqualTo(subject);
        Assertions.assertThat(actualNotification.getSubjectReplacements()).isEqualTo(replacements);
        Assertions.assertThat(actualNotification.getTemplate()).isEqualTo(template);
        Assertions.assertThat(actualNotification.isSendMail()).isTrue();
        Assertions.assertThat(actualNotification.isGroupMail()).isTrue();
        Assertions.assertThat(actualNotification.getReceiverType()).isEqualTo(ReceiverType.SUPPLIER);
        Assertions.assertThat(actualNotification.getReceiverId()).isEqualTo("123");
    }

    @Test
    void shouldNotCreateEmailIfStateChanged() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(true);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setState(OfferState.ENQUIRY);
        offer.setEnquiryType(EnquiryType.FORWARDED);

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        Mockito.when(this.recipientsSupportService.buildSupplierRecipientInfo(any())).thenReturn(
            new RecipientInfo(ReceiverType.SUPPLIER, null)
        );

        this.service.notify(updateEvent, notificationBuilder);

        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(captor.capture());

        Notification actualNotification = captor.getValue();
        Assertions.assertThat(actualNotification.getSubject()).isNull();
        Assertions.assertThat(actualNotification.getSubjectReplacements()).isNull();
        Assertions.assertThat(actualNotification.getTemplate()).isNull();
        Assertions.assertThat(actualNotification.isSendMail()).isFalse();
        Assertions.assertThat(actualNotification.isGroupMail()).isFalse();
    }

    @Test
    void shouldNotCreateEnquiryNotificationForQuotedForwardedOffer() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setState(OfferState.QUOTED);
        offer.setEnquiryType(EnquiryType.FORWARDED);

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder()
            .message(message)
            .receiverType(ReceiverType.SUPPLIER)
            .receiverId("123");

        Mockito.when(this.recipientsSupportService.buildSupplierRecipientInfo(any())).thenReturn(
            new RecipientInfo(ReceiverType.SUPPLIER, null)
        );

        this.service.notify(updateEvent, notificationBuilder);

        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(captor.capture());

        Notification actualNotification = captor.getValue();
        Assertions.assertThat(actualNotification.getSubject()).isNull();
        Assertions.assertThat(actualNotification.getSubjectReplacements()).isNull();
        Assertions.assertThat(actualNotification.getTemplate()).isNull();
        Assertions.assertThat(actualNotification.isSendMail()).isFalse();
        Assertions.assertThat(actualNotification.isGroupMail()).isFalse();
        Assertions.assertThat(actualNotification.getReceiverType()).isEqualTo(ReceiverType.SUPPLIER);
        Assertions.assertThat(actualNotification.getReceiverId()).isNotNull();
    }

    @Test
    void shouldCreateEnquiryNotificationForQuotedSpotOffer() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setState(OfferState.QUOTED);
        offer.setEnquiryType(EnquiryType.SPOT);

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder()
            .message(message)
            .receiverType(ReceiverType.SUPPLIER)
            .receiverId("123");

        String subject = "subject";
        Object[] replacements = {};
        String template = "template";

        Notification notification = notificationBuilder
            .subject("subject")
            .subjectReplacements(new Object[] {})
            .template("template")
            .sendMail(true)
            .groupMail(true)
            .build();

        Mockito.when(spotEnquiryNotificationCreator.createNotification(notificationBuilder, offer)).thenReturn(
            notification
        );

        Mockito.when(this.recipientsSupportService.buildSupplierRecipientInfo(any())).thenReturn(
            new RecipientInfo(ReceiverType.SUPPLIER, null)
        );

        this.service.notify(updateEvent, notificationBuilder);

        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(captor.capture());

        Notification actualNotification = captor.getValue();
        Assertions.assertThat(actualNotification.getSubject()).isEqualTo(subject);
        Assertions.assertThat(actualNotification.getSubjectReplacements()).isEqualTo(replacements);
        Assertions.assertThat(actualNotification.getTemplate()).isEqualTo(template);
        Assertions.assertThat(actualNotification.isSendMail()).isTrue();
        Assertions.assertThat(actualNotification.isGroupMail()).isTrue();
        Assertions.assertThat(actualNotification.getReceiverType()).isEqualTo(ReceiverType.SUPPLIER);
        Assertions.assertThat(actualNotification.getReceiverId()).isEqualTo("123");
    }

    @Test
    void shouldNotCreateEnquiryNotificationForQuotedAssignedOffer() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setState(OfferState.QUOTED);
        offer.setEnquiryType(EnquiryType.ASSIGNED);

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder()
            .message(message)
            .receiverType(ReceiverType.SUPPLIER)
            .receiverId("123");

        Mockito.when(this.recipientsSupportService.buildSupplierRecipientInfo(any())).thenReturn(
            new RecipientInfo(ReceiverType.SUPPLIER, null)
        );

        this.service.notify(updateEvent, notificationBuilder);

        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(captor.capture());

        Notification actualNotification = captor.getValue();
        Assertions.assertThat(actualNotification.getSubject()).isNull();
        Assertions.assertThat(actualNotification.getSubjectReplacements()).isNull();
        Assertions.assertThat(actualNotification.getTemplate()).isNull();
        Assertions.assertThat(actualNotification.isSendMail()).isFalse();
        Assertions.assertThat(actualNotification.isGroupMail()).isFalse();
        Assertions.assertThat(actualNotification.getReceiverType()).isEqualTo(ReceiverType.SUPPLIER);
        Assertions.assertThat(actualNotification.getReceiverId()).isNotNull();
    }

    @Test
    void shouldCreateOrderNotificationForAssignedOffers() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setState(OfferState.ORDER);
        offer.setEnquiryType(EnquiryType.ASSIGNED);

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder()
            .message(message)
            .receiverType(ReceiverType.SUPPLIER)
            .receiverId("123");

        String subject = "subject";
        Object[] replacements = {};
        String template = "template";

        Notification notification = notificationBuilder
            .subject(subject)
            .subjectReplacements(replacements)
            .template(template)
            .sendMail(true)
            .groupMail(true)
            .build();

        Mockito.when(defaultOrderNotificationCreator.createNotification(notificationBuilder, offer)).thenReturn(
            notification
        );

        Mockito.when(this.recipientsSupportService.buildSupplierRecipientInfo(any())).thenReturn(
            new RecipientInfo(ReceiverType.SUPPLIER, null)
        );

        this.service.notify(updateEvent, notificationBuilder);

        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(captor.capture());

        Notification actualNotification = captor.getValue();
        Assertions.assertThat(actualNotification.getSubject()).isEqualTo(subject);
        Assertions.assertThat(actualNotification.getSubjectReplacements()).isEqualTo(replacements);
        Assertions.assertThat(actualNotification.getTemplate()).isEqualTo(template);
        Assertions.assertThat(actualNotification.isSendMail()).isTrue();
        Assertions.assertThat(actualNotification.isGroupMail()).isTrue();
        Assertions.assertThat(actualNotification.getReceiverType()).isEqualTo(ReceiverType.SUPPLIER);
        Assertions.assertThat(actualNotification.getReceiverId()).isEqualTo("123");
    }

    @Test
    void shouldCreateOrderNotificationForSpotOffers() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setState(OfferState.ORDER);
        offer.setEnquiryType(EnquiryType.SPOT);

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder()
            .message(message)
            .receiverType(ReceiverType.SUPPLIER)
            .receiverId("123");

        String subject = "subject";
        Object[] replacements = {};
        String template = "template";

        Notification notification = notificationBuilder
            .subject(subject)
            .subjectReplacements(replacements)
            .template(template)
            .sendMail(true)
            .groupMail(true)
            .build();

        Mockito.when(defaultOrderNotificationCreator.createNotification(notificationBuilder, offer)).thenReturn(
            notification
        );

        Mockito.when(this.recipientsSupportService.buildSupplierRecipientInfo(any())).thenReturn(
            new RecipientInfo(ReceiverType.SUPPLIER, null)
        );

        this.service.notify(updateEvent, notificationBuilder);

        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(captor.capture());

        Notification actualNotification = captor.getValue();
        Assertions.assertThat(actualNotification.getSubject()).isEqualTo(subject);
        Assertions.assertThat(actualNotification.getSubjectReplacements()).isEqualTo(replacements);
        Assertions.assertThat(actualNotification.getTemplate()).isEqualTo(template);
        Assertions.assertThat(actualNotification.isSendMail()).isTrue();
        Assertions.assertThat(actualNotification.isGroupMail()).isTrue();
        Assertions.assertThat(actualNotification.getReceiverType()).isEqualTo(ReceiverType.SUPPLIER);
        Assertions.assertThat(actualNotification.getReceiverId()).isEqualTo("123");
    }

    @Test
    void shouldCreateOrderNotificationForForwardedOffers() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setState(OfferState.ORDER);
        offer.setEnquiryType(EnquiryType.FORWARDED);

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder()
            .message(message)
            .receiverType(ReceiverType.SUPPLIER)
            .receiverId("123");

        String subject = "subject";
        Object[] replacements = {};
        String template = "template";

        Notification notification = notificationBuilder
            .subject(subject)
            .subjectReplacements(replacements)
            .template(template)
            .sendMail(true)
            .groupMail(true)
            .build();

        Mockito.when(forwardedOrderNotificationCreator.createNotification(notificationBuilder, offer)).thenReturn(
            notification
        );

        Mockito.when(this.recipientsSupportService.buildSupplierRecipientInfo(any())).thenReturn(
            new RecipientInfo(ReceiverType.SUPPLIER, null)
        );

        this.service.notify(updateEvent, notificationBuilder);

        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(captor.capture());

        Notification actualNotification = captor.getValue();
        Assertions.assertThat(actualNotification.getSubject()).isEqualTo(subject);
        Assertions.assertThat(actualNotification.getSubjectReplacements()).isEqualTo(replacements);
        Assertions.assertThat(actualNotification.getTemplate()).isEqualTo(template);
        Assertions.assertThat(actualNotification.isSendMail()).isTrue();
        Assertions.assertThat(actualNotification.isGroupMail()).isTrue();
        Assertions.assertThat(actualNotification.getReceiverType()).isEqualTo(ReceiverType.SUPPLIER);
        Assertions.assertThat(actualNotification.getReceiverId()).isEqualTo("123");
    }

    @Test
    void shouldCreateEmailNotificationWithCorrectSenderInformationUser() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(false);
        updateEvent.getEvent().getEventTrigger().setUserId("userId-123");

        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setEnquiryType(EnquiryType.ASSIGNED);
        offer.setState(OfferState.ENQUIRY);

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder()
            .message(message)
            .receiverType(ReceiverType.SUPPLIER)
            .receiverId("123");

        Notification notification = notificationBuilder.senderType(SenderType.USER).senderId("userId-123").build();

        Mockito.when(assignedEnquiryNotificationCreator.createNotification(notificationBuilder, offer)).thenReturn(
            notification
        );

        Mockito.when(this.recipientsSupportService.buildSupplierRecipientInfo(any())).thenReturn(
            new RecipientInfo(ReceiverType.SUPPLIER, null)
        );

        this.service.notify(updateEvent, notificationBuilder);

        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(captor.capture());

        Notification actualNotification = captor.getValue();
        Assertions.assertThat(actualNotification.getSenderType()).isEqualTo(SenderType.USER);
        Assertions.assertThat(actualNotification.getSenderId()).isEqualTo("userId-123");
    }

    @Test
    void shouldCreateEmailNotificationWithCorrectSenderInformationCustomer() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(false);
        updateEvent.getEvent().getEventTrigger().setCompanyId("customerId-123");
        updateEvent.getEvent().getEventTrigger().setUserId(null);

        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setEnquiryType(EnquiryType.ASSIGNED);
        offer.setState(OfferState.ENQUIRY);

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder()
            .message(message)
            .receiverType(ReceiverType.SUPPLIER)
            .receiverId("123");

        Notification notification = notificationBuilder
            .senderType(SenderType.CUSTOMER)
            .senderId("customerId-123")
            .build();

        Mockito.when(assignedEnquiryNotificationCreator.createNotification(notificationBuilder, offer)).thenReturn(
            notification
        );

        Mockito.when(this.recipientsSupportService.buildSupplierRecipientInfo(any())).thenReturn(
            new RecipientInfo(ReceiverType.SUPPLIER, null)
        );

        this.service.notify(updateEvent, notificationBuilder);

        ArgumentCaptor<Notification> captor = ArgumentCaptor.forClass(Notification.class);

        Mockito.verify(notificationService).notify(captor.capture());

        Notification actualNotification = captor.getValue();
        Assertions.assertThat(actualNotification.getSenderType()).isEqualTo(SenderType.CUSTOMER);
        Assertions.assertThat(actualNotification.getSenderId()).isEqualTo("customerId-123");
    }
}
