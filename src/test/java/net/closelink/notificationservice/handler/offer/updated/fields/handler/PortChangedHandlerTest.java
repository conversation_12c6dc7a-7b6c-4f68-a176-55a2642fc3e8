package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static net.closelink.notificationservice.data.DataCreator.createPortMessage;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.service.rest.port.PortApiRestService;
import net.closelink.notificationservice.service.rest.port.domain.PortMessage;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class PortChangedHandlerTest {

    private PortChangedHandler service;
    private BaseActivityLogService activityLogService;
    private PortApiRestService ApiRestService;

    @Before
    public void setUp() {
        this.activityLogService = Mockito.mock(BaseActivityLogService.class);
        this.ApiRestService = Mockito.mock(PortApiRestService.class);

        this.service = new PortChangedHandler(this.activityLogService, this.ApiRestService);
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.activityLogService);
        Mockito.verifyNoMoreInteractions(this.ApiRestService);
    }

    @Test
    public void handleTest() {
        final PortMessage newValue = createPortMessage();
        final PortMessage oldValue = createPortMessage();

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(newValue.getId());
        updateEvent.getEventMessage().setOldValue(oldValue.getId());

        when(this.ApiRestService.getPort(newValue.getId())).thenReturn(newValue);
        when(this.ApiRestService.getPort(oldValue.getId())).thenReturn(oldValue);

        this.service.handle(updateEvent);

        final ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.ApiRestService).getPort(newValue.getId());
        verify(this.ApiRestService).getPort(oldValue.getId());
        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.port"),
            replacementsCapture.capture()
        );

        assertEquals(oldValue.getName(), replacementsCapture.getValue()[0]);
        assertEquals(newValue.getName(), replacementsCapture.getValue()[1]);
    }
}
