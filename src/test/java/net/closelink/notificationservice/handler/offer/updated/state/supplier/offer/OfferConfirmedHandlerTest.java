package net.closelink.notificationservice.handler.offer.updated.state.supplier.offer;

import static org.junit.Assert.assertEquals;

import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierHandlerTest;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.order.GeneralOrderConfirmedHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.Before;
import org.junit.Test;

public class OfferConfirmedHandlerTest extends SupplierHandlerTest {

    private static final String MESSAGE = "orderConfirmed.message";
    private static final String TEMPLATE = "Order_Customer_Order_Confirmed";

    private GeneralOrderConfirmedHandler handler;

    @Override
    @Before
    public void setUp() {
        super.setUp();
        this.handler = new GeneralOrderConfirmedHandler(this.supplierStateHandlerSupport);
    }

    @Test
    public void handleTestWithoutBuyerRef() {
        this.offer.setBuyerReference(null);

        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(MESSAGE, notification.getMessage());
        assertEquals(TEMPLATE, notification.getTemplate());
        assertEquals("orderConfirmed.subject", notification.getSubject());
    }

    @Test
    public void handleTestWithBuyerRef() {
        this.offer.setBuyerReference("Test 123");

        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final Notification notification = verifyNotify();

        assertEquals(MESSAGE, notification.getMessage());
        assertEquals(TEMPLATE, notification.getTemplate());
        assertEquals("orderConfirmedBuyerRef.subject", notification.getSubject());
    }
}
