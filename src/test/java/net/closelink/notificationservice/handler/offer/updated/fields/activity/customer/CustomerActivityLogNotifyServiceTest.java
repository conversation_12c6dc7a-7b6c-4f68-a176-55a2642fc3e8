package net.closelink.notificationservice.handler.offer.updated.fields.activity.customer;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;

import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementOffer;
import net.closelink.notificationservice.replacement.subject.customer.CustomerSubjectReplacementService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.MailNotificationSettings;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class CustomerActivityLogNotifyServiceTest {

    private CustomerActivityLogNotifyService service;
    private NotificationService notificationService;
    private CustomerSubjectReplacementService customerSubjectReplacementService;
    private VesselApiRestService vesselApiRestService;

    @Before
    public void setUp() {
        this.notificationService = Mockito.mock(NotificationService.class);
        this.customerSubjectReplacementService = Mockito.mock(CustomerSubjectReplacementService.class);
        this.vesselApiRestService = Mockito.mock(VesselApiRestService.class);

        this.service = new DefaultCustomerActivityLogNotifyService(
            this.notificationService,
            this.customerSubjectReplacementService,
            this.vesselApiRestService
        );
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.notificationService);
        Mockito.verifyNoMoreInteractions(this.customerSubjectReplacementService);
        Mockito.verifyNoMoreInteractions(this.vesselApiRestService);
    }

    @Test
    public void shouldSendNotificationForStateChange() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(true);

        Mockito.when(
            this.vesselApiRestService.getVessel(updateEvent.getEventMessage().getOffer().getVesselId())
        ).thenReturn(
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .sendToVessel(true)
                                .build()
                        )
                        .build()
                )
                .build()
        );

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        this.service.notify(updateEvent, notificationBuilder);

        final ArgumentCaptor<Notification> notificationCapture = ArgumentCaptor.forClass(Notification.class);
        Mockito.verify(this.notificationService).notify(notificationCapture.capture());
        Mockito.verify(this.vesselApiRestService).getVessel(updateEvent.getEventMessage().getOffer().getVesselId());

        var notification = notificationCapture.getValue();
        assertEquals(message, notification.getMessage());
        assertEquals(ReceiverType.CUSTOMER, notification.getReceiverType());
    }

    @Test
    public void shouldSendNotificationForEnquiry() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setState(OfferState.ENQUIRY);

        Mockito.when(
            this.vesselApiRestService.getVessel(updateEvent.getEventMessage().getOffer().getVesselId())
        ).thenReturn(
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .sendToVessel(true)
                                .build()
                        )
                        .build()
                )
                .build()
        );

        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        this.service.notify(updateEvent, notificationBuilder);

        final ArgumentCaptor<Notification> notificationCapture = ArgumentCaptor.forClass(Notification.class);
        Mockito.verify(this.notificationService).notify(notificationCapture.capture());
        Mockito.verify(this.vesselApiRestService).getVessel(updateEvent.getEventMessage().getOffer().getVesselId());

        var notification = notificationCapture.getValue();

        assertEquals(message, notification.getMessage());
        assertEquals(ReceiverType.CUSTOMER, notification.getReceiverType());
    }

    @Test
    public void shouldSendNotificationForQuotedOrderWithoutBuyerRef() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().getOffer().setDateCreated("2018-11-03T13:37:00Z");
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setBuyerReference(null);
        offer.setState(OfferState.QUOTED);
        offer.setEnquiryType(EnquiryType.ASSIGNED);

        Mockito.when(
            this.vesselApiRestService.getVessel(updateEvent.getEventMessage().getOffer().getVesselId())
        ).thenReturn(
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .sendToVessel(true)
                                .build()
                        )
                        .build()
                )
                .build()
        );

        final Object[] subjectReplacements = DataCreator.createStringArray();
        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        Mockito.when(
            this.customerSubjectReplacementService.createSubjectReplacements(any(ReplacementOffer.class))
        ).thenReturn(subjectReplacements);

        this.service.notify(updateEvent, notificationBuilder);

        final ArgumentCaptor<Notification> notificationCapture = ArgumentCaptor.forClass(Notification.class);
        Mockito.verify(this.notificationService).notify(notificationCapture.capture());

        var notification = notificationCapture.getValue();

        assertEquals(message, notification.getMessage());
        assertEquals("activitylog.subject.customer.quoted", notification.getSubject());
        assertEquals("Order_Customer_New_Quote", notification.getTemplate());
        assertTrue(notification.isGroupMail());
        assertTrue(notification.isSendMail());
        assertArrayEquals(subjectReplacements, notification.getSubjectReplacements());
        assertEquals(ReceiverType.CUSTOMER, notification.getReceiverType());

        Mockito.verify(this.customerSubjectReplacementService).createSubjectReplacements(any(ReplacementOffer.class));
        Mockito.verify(this.vesselApiRestService).getVessel(updateEvent.getEventMessage().getOffer().getVesselId());
    }

    @Test
    public void shouldSendNotificationForQuotedOrderWithBuyerRef() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().getOffer().setDateCreated("2018-11-03T13:37:00Z");
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setBuyerReference("Test 123");
        offer.setState(OfferState.QUOTED);
        offer.setEnquiryType(EnquiryType.ASSIGNED);

        Mockito.when(
            this.vesselApiRestService.getVessel(updateEvent.getEventMessage().getOffer().getVesselId())
        ).thenReturn(
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .sendToVessel(true)
                                .build()
                        )
                        .build()
                )
                .build()
        );

        final Object[] subjectReplacements = DataCreator.createStringArray();
        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        Mockito.when(
            this.customerSubjectReplacementService.createSubjectReplacements(any(ReplacementOffer.class))
        ).thenReturn(subjectReplacements);

        this.service.notify(updateEvent, notificationBuilder);

        final ArgumentCaptor<Notification> notificationCapture = ArgumentCaptor.forClass(Notification.class);
        Mockito.verify(this.notificationService).notify(notificationCapture.capture());

        var notification = notificationCapture.getValue();

        assertEquals(message, notification.getMessage());
        assertEquals("activitylog.subject.customer.quotedBuyerRef", notification.getSubject());
        assertEquals("Order_Customer_New_Quote", notification.getTemplate());
        assertTrue(notification.isGroupMail());
        assertTrue(notification.isSendMail());
        assertArrayEquals(subjectReplacements, notification.getSubjectReplacements());
        assertEquals(ReceiverType.CUSTOMER, notification.getReceiverType());

        Mockito.verify(this.customerSubjectReplacementService).createSubjectReplacements(any(ReplacementOffer.class));
        Mockito.verify(this.vesselApiRestService).getVessel(updateEvent.getEventMessage().getOffer().getVesselId());
    }

    @Test
    public void shouldSendNotificationForSpotQuotedOrderWithoutBuyerRef() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().getOffer().setDateCreated("2018-11-03T13:37:00Z");
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setBuyerReference(null);
        offer.setState(OfferState.QUOTED);
        offer.setEnquiryType(EnquiryType.SPOT);

        Mockito.when(
            this.vesselApiRestService.getVessel(updateEvent.getEventMessage().getOffer().getVesselId())
        ).thenReturn(
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .sendToVessel(true)
                                .build()
                        )
                        .build()
                )
                .build()
        );

        final Object[] subjectReplacements = DataCreator.createStringArray();
        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        Mockito.when(
            this.customerSubjectReplacementService.createSpotSubjectReplacements(any(ReplacementOffer.class))
        ).thenReturn(subjectReplacements);

        this.service.notify(updateEvent, notificationBuilder);

        final ArgumentCaptor<Notification> notificationCapture = ArgumentCaptor.forClass(Notification.class);
        Mockito.verify(this.notificationService).notify(notificationCapture.capture());

        var notification = notificationCapture.getValue();

        assertEquals(message, notification.getMessage());
        assertEquals("activitylog.subject.trade.customer.quoted", notification.getSubject());
        assertEquals("Order_Customer_New_Quote_Trade", notification.getTemplate());
        assertTrue(notification.isGroupMail());
        assertTrue(notification.isSendMail());
        assertArrayEquals(subjectReplacements, notification.getSubjectReplacements());
        assertEquals(ReceiverType.CUSTOMER, notification.getReceiverType());

        Mockito.verify(this.customerSubjectReplacementService).createSpotSubjectReplacements(
            any(ReplacementOffer.class)
        );
        Mockito.verify(this.vesselApiRestService).getVessel(updateEvent.getEventMessage().getOffer().getVesselId());
    }

    @Test
    public void shouldSendNotificationForSpotQuotedOrderWithBuyerRef() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().getOffer().setDateCreated("2018-11-03T13:37:00Z");
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setBuyerReference("Test 123");
        offer.setState(OfferState.QUOTED);
        offer.setEnquiryType(EnquiryType.SPOT);

        Mockito.when(
            this.vesselApiRestService.getVessel(updateEvent.getEventMessage().getOffer().getVesselId())
        ).thenReturn(
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .sendToVessel(true)
                                .build()
                        )
                        .build()
                )
                .build()
        );

        final Object[] subjectReplacements = DataCreator.createStringArray();
        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        Mockito.when(
            this.customerSubjectReplacementService.createSpotSubjectReplacements(any(ReplacementOffer.class))
        ).thenReturn(subjectReplacements);

        this.service.notify(updateEvent, notificationBuilder);

        final ArgumentCaptor<Notification> notificationCapture = ArgumentCaptor.forClass(Notification.class);
        Mockito.verify(this.notificationService).notify(notificationCapture.capture());

        var notification = notificationCapture.getValue();

        assertEquals(message, notification.getMessage());
        assertEquals("activitylog.subject.trade.customer.quotedBuyerRef", notification.getSubject());
        assertEquals("Order_Customer_New_Quote_Trade", notification.getTemplate());
        assertTrue(notification.isGroupMail());
        assertTrue(notification.isSendMail());
        assertArrayEquals(subjectReplacements, notification.getSubjectReplacements());
        assertEquals(ReceiverType.CUSTOMER, notification.getReceiverType());

        Mockito.verify(this.customerSubjectReplacementService).createSpotSubjectReplacements(
            any(ReplacementOffer.class)
        );
        Mockito.verify(this.vesselApiRestService).getVessel(updateEvent.getEventMessage().getOffer().getVesselId());
    }

    @Test
    public void shouldSendNotificationForOrderChangesWithoutBuyerRef() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().getOffer().setDateCreated("2018-11-03T13:37:00Z");
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setBuyerReference(null);
        offer.setState(OfferState.ORDER);
        offer.setEnquiryType(EnquiryType.ASSIGNED);

        Mockito.when(
            this.vesselApiRestService.getVessel(updateEvent.getEventMessage().getOffer().getVesselId())
        ).thenReturn(
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .sendToVessel(true)
                                .build()
                        )
                        .build()
                )
                .build()
        );

        final Object[] subjectReplacements = DataCreator.createStringArray();
        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        Mockito.when(
            this.customerSubjectReplacementService.createSubjectReplacements(any(ReplacementOffer.class))
        ).thenReturn(subjectReplacements);

        this.service.notify(updateEvent, notificationBuilder);

        final ArgumentCaptor<Notification> notificationCapture = ArgumentCaptor.forClass(Notification.class);
        Mockito.verify(this.notificationService).notify(notificationCapture.capture());

        var notification = notificationCapture.getValue();

        assertEquals(message, notification.getMessage());
        assertEquals("activitylog.subject.customer.order", notification.getSubject());
        assertEquals("Order_Customer_Order_Supplier_Changes", notification.getTemplate());
        assertTrue(notification.isGroupMail());
        assertTrue(notification.isSendMail());
        assertArrayEquals(subjectReplacements, notification.getSubjectReplacements());
        assertEquals(ReceiverType.CUSTOMER, notification.getReceiverType());

        Mockito.verify(this.customerSubjectReplacementService).createSubjectReplacements(any(ReplacementOffer.class));
        Mockito.verify(this.vesselApiRestService).getVessel(updateEvent.getEventMessage().getOffer().getVesselId());
    }

    @Test
    public void shouldSendNotificationForOrderChangesWithBuyerRef() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().getOffer().setDateCreated("2018-11-03T13:37:00Z");
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setBuyerReference("Test 123");
        offer.setState(OfferState.ORDER);
        offer.setEnquiryType(EnquiryType.ASSIGNED);

        Mockito.when(
            this.vesselApiRestService.getVessel(updateEvent.getEventMessage().getOffer().getVesselId())
        ).thenReturn(
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .sendToVessel(true)
                                .build()
                        )
                        .build()
                )
                .build()
        );

        final Object[] subjectReplacements = DataCreator.createStringArray();
        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        Mockito.when(
            this.customerSubjectReplacementService.createSubjectReplacements(any(ReplacementOffer.class))
        ).thenReturn(subjectReplacements);

        this.service.notify(updateEvent, notificationBuilder);

        final ArgumentCaptor<Notification> notificationCapture = ArgumentCaptor.forClass(Notification.class);
        Mockito.verify(this.notificationService).notify(notificationCapture.capture());

        var notification = notificationCapture.getValue();

        assertEquals(message, notification.getMessage());
        assertEquals("activitylog.subject.customer.orderBuyerRef", notification.getSubject());
        assertEquals("Order_Customer_Order_Supplier_Changes", notification.getTemplate());
        assertTrue(notification.isGroupMail());
        assertTrue(notification.isSendMail());
        assertArrayEquals(subjectReplacements, notification.getSubjectReplacements());
        assertEquals(ReceiverType.CUSTOMER, notification.getReceiverType());

        Mockito.verify(this.customerSubjectReplacementService).createSubjectReplacements(any(ReplacementOffer.class));
        Mockito.verify(this.vesselApiRestService).getVessel(updateEvent.getEventMessage().getOffer().getVesselId());
    }

    @Test
    public void shouldSendNotificationForConfirmedOrderChangesWithoutBuyerRef() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().getOffer().setDateCreated("2018-11-03T13:37:00Z");
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setBuyerReference(null);
        offer.setState(OfferState.CONFIRMED);
        offer.setEnquiryType(EnquiryType.ASSIGNED);

        Mockito.when(
            this.vesselApiRestService.getVessel(updateEvent.getEventMessage().getOffer().getVesselId())
        ).thenReturn(
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .sendToVessel(true)
                                .build()
                        )
                        .build()
                )
                .build()
        );

        final Object[] subjectReplacements = DataCreator.createStringArray();
        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        Mockito.when(
            this.customerSubjectReplacementService.createSubjectReplacements(any(ReplacementOffer.class))
        ).thenReturn(subjectReplacements);

        this.service.notify(updateEvent, notificationBuilder);

        final ArgumentCaptor<Notification> notificationCapture = ArgumentCaptor.forClass(Notification.class);
        Mockito.verify(this.notificationService).notify(notificationCapture.capture());

        var notification = notificationCapture.getValue();

        assertEquals(message, notification.getMessage());
        assertEquals("activitylog.subject.customer.order", notification.getSubject());
        assertEquals("Order_Customer_Order_Supplier_Changes", notification.getTemplate());
        assertTrue(notification.isGroupMail());
        assertTrue(notification.isSendMail());
        assertArrayEquals(subjectReplacements, notification.getSubjectReplacements());
        assertEquals(ReceiverType.CUSTOMER, notification.getReceiverType());

        Mockito.verify(this.customerSubjectReplacementService).createSubjectReplacements(any(ReplacementOffer.class));
        Mockito.verify(this.vesselApiRestService).getVessel(updateEvent.getEventMessage().getOffer().getVesselId());
    }

    @Test
    public void shouldSendNotificationForConfirmedOrderChangesWithBuyerRef() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().getOffer().setDateCreated("2018-11-03T13:37:00Z");
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setBuyerReference("Test 123");
        offer.setState(OfferState.CONFIRMED);
        offer.setEnquiryType(EnquiryType.ASSIGNED);

        Mockito.when(
            this.vesselApiRestService.getVessel(updateEvent.getEventMessage().getOffer().getVesselId())
        ).thenReturn(
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(
                            MailNotificationSettings.NotificationSettings.builder().enabled(false).build()
                        )
                        .build()
                )
                .build()
        );

        final Object[] subjectReplacements = DataCreator.createStringArray();
        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        Mockito.when(
            this.customerSubjectReplacementService.createSubjectReplacements(any(ReplacementOffer.class))
        ).thenReturn(subjectReplacements);

        this.service.notify(updateEvent, notificationBuilder);

        final ArgumentCaptor<Notification> notificationCapture = ArgumentCaptor.forClass(Notification.class);
        Mockito.verify(this.notificationService).notify(notificationCapture.capture());

        var notification = notificationCapture.getValue();

        assertEquals(message, notification.getMessage());
        assertEquals("activitylog.subject.customer.orderBuyerRef", notification.getSubject());
        assertEquals("Order_Customer_Order_Supplier_Changes", notification.getTemplate());
        assertTrue(notification.isGroupMail());
        assertTrue(notification.isSendMail());
        assertEquals(ReceiverType.CUSTOMER, notification.getReceiverType());
        assertArrayEquals(subjectReplacements, notification.getSubjectReplacements());

        Mockito.verify(this.customerSubjectReplacementService).createSubjectReplacements(any(ReplacementOffer.class));
        Mockito.verify(this.vesselApiRestService).getVessel(updateEvent.getEventMessage().getOffer().getVesselId());
    }

    @Test
    public void shouldCreateEmailNotificationWithCorrectSenderInformationUser() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().getOffer().setDateCreated("2018-11-03T13:37:00Z");
        updateEvent.getEventMessage().setStateChanged(false);
        updateEvent.getEvent().getEventTrigger().setUserId("userId-123");
        updateEvent.getEvent().getEventTrigger().setCompanyId(null);
        updateEvent.getEventMessage().getOffer().setState(OfferState.QUOTED);
        updateEvent.getEventMessage().getOffer().setEnquiryType(EnquiryType.ASSIGNED);

        Mockito.when(
            this.vesselApiRestService.getVessel(updateEvent.getEventMessage().getOffer().getVesselId())
        ).thenReturn(
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .sendToVessel(true)
                                .build()
                        )
                        .build()
                )
                .build()
        );

        final Object[] subjectReplacements = DataCreator.createStringArray();
        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        Mockito.when(
            this.customerSubjectReplacementService.createSubjectReplacements(any(ReplacementOffer.class))
        ).thenReturn(subjectReplacements);

        this.service.notify(updateEvent, notificationBuilder);

        final ArgumentCaptor<Notification> notificationCapture = ArgumentCaptor.forClass(Notification.class);
        Mockito.verify(this.notificationService).notify(notificationCapture.capture());

        Notification notification = notificationCapture.getValue();

        assertEquals(notification.getSenderId(), "userId-123");
        assertEquals(notification.getSenderType(), SenderType.USER);
        assertEquals(ReceiverType.CUSTOMER, notification.getReceiverType());

        Mockito.verify(this.customerSubjectReplacementService).createSubjectReplacements(any(ReplacementOffer.class));
        Mockito.verify(this.vesselApiRestService).getVessel(updateEvent.getEventMessage().getOffer().getVesselId());
    }

    @Test
    public void shouldCreateEmailNotificationWithCorrectSenderInformationSupplier() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().getOffer().setDateCreated("2018-11-03T13:37:00Z");
        updateEvent.getEventMessage().setStateChanged(false);
        updateEvent.getEvent().getEventTrigger().setCompanyId("supplierId-123");
        updateEvent.getEvent().getEventTrigger().setUserId(null);
        updateEvent.getEventMessage().getOffer().setState(OfferState.QUOTED);
        updateEvent.getEventMessage().getOffer().setEnquiryType(EnquiryType.ASSIGNED);

        Mockito.when(
            this.vesselApiRestService.getVessel(updateEvent.getEventMessage().getOffer().getVesselId())
        ).thenReturn(
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(false)
                                .sendToVessel(true)
                                .build()
                        )
                        .build()
                )
                .build()
        );

        final Object[] subjectReplacements = DataCreator.createStringArray();
        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        Mockito.when(
            this.customerSubjectReplacementService.createSubjectReplacements(any(ReplacementOffer.class))
        ).thenReturn(subjectReplacements);

        this.service.notify(updateEvent, notificationBuilder);

        final ArgumentCaptor<Notification> notificationCapture = ArgumentCaptor.forClass(Notification.class);
        Mockito.verify(this.notificationService).notify(notificationCapture.capture());

        Notification notification = notificationCapture.getValue();

        assertEquals("supplierId-123", notification.getSenderId());
        assertEquals(SenderType.SUPPLIER, notification.getSenderType());
        assertEquals(ReceiverType.CUSTOMER, notification.getReceiverType());

        Mockito.verify(this.customerSubjectReplacementService).createSubjectReplacements(any(ReplacementOffer.class));
        Mockito.verify(this.vesselApiRestService).getVessel(updateEvent.getEventMessage().getOffer().getVesselId());
    }

    @Test
    public void shouldSendNotificationToCustomRecipients() {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().getOffer().setDateCreated("2018-11-03T13:37:00Z");
        updateEvent.getEventMessage().setStateChanged(false);
        final Offer offer = updateEvent.getEventMessage().getOffer();
        offer.setBuyerReference("Test 123");
        offer.setState(OfferState.ORDER);
        offer.setEnquiryType(EnquiryType.ASSIGNED);

        Mockito.when(
            this.vesselApiRestService.getVessel(updateEvent.getEventMessage().getOffer().getVesselId())
        ).thenReturn(
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(
                            MailNotificationSettings.NotificationSettings.builder()
                                .enabled(true)
                                .sendToVessel(true)
                                .build()
                        )
                        .build()
                )
                .build()
        );

        final Object[] subjectReplacements = DataCreator.createStringArray();
        final String message = DataCreator.createString();
        final Notification.NotificationBuilder notificationBuilder = Notification.builder().message(message);

        Mockito.when(
            this.customerSubjectReplacementService.createSubjectReplacements(any(ReplacementOffer.class))
        ).thenReturn(subjectReplacements);

        this.service.notify(updateEvent, notificationBuilder);

        final ArgumentCaptor<Notification> notificationCapture = ArgumentCaptor.forClass(Notification.class);
        Mockito.verify(this.notificationService).notify(notificationCapture.capture());

        var notification = notificationCapture.getValue();

        assertEquals(ReceiverType.CUSTOM, notification.getReceiverType());
        assertEquals(1, notification.getRecipients().size());
        assertEquals("<EMAIL>", notification.getRecipients().get(0).getEmailAddress());

        Mockito.verify(this.customerSubjectReplacementService).createSubjectReplacements(any(ReplacementOffer.class));
        Mockito.verify(this.vesselApiRestService).getVessel(updateEvent.getEventMessage().getOffer().getVesselId());
    }
}
