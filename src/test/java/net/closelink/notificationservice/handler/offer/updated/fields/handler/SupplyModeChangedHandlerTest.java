package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.objects.SupplyModeDetails;
import net.closelink.notificationservice.data.MessageSourceCreator;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.context.MessageSource;

public class SupplyModeChangedHandlerTest {

    private SupplyModeChangedHandler service;
    private BaseActivityLogService activityLogService;

    @Before
    public void setUp() {
        this.activityLogService = Mockito.mock(BaseActivityLogService.class);

        MessageSource messageSource = MessageSourceCreator.createMessageSource();

        this.service = new SupplyModeChangedHandler(this.activityLogService, messageSource);
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.activityLogService);
    }

    @Test
    public void handleTest() {
        final OfferUpdatedEvent updateEvent = createEvent(
            SupplyModeDetails.builder().supplyMode("BARGE").build(),
            SupplyModeDetails.builder().supplyMode("TRUCK").build()
        );

        this.service.handle(updateEvent);

        final ArgumentCaptor<Object[]> replacementsCapture = ArgumentCaptor.forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.supplymode"),
            replacementsCapture.capture()
        );

        assertEquals("truck", replacementsCapture.getValue()[0]);
        assertEquals("barge", replacementsCapture.getValue()[1]);
    }

    @Test
    public void handleNullTest() {
        final OfferUpdatedEvent updateEvent = createEvent(
            SupplyModeDetails.builder().supplyMode("BARGE").build(),
            SupplyModeDetails.builder().supplyMode(null).build()
        );

        this.service.handle(updateEvent);

        final ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.supplymode"),
            replacementsCapture.capture()
        );

        assertNull(replacementsCapture.getValue()[0]);
        assertEquals("barge", replacementsCapture.getValue()[1]);
    }

    private OfferUpdatedEvent createEvent(final SupplyModeDetails newValue, final SupplyModeDetails oldValue) {
        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(GsonCoder.decode(newValue));
        updateEvent.getEventMessage().setOldValue(GsonCoder.decode(oldValue));
        return updateEvent;
    }
}
