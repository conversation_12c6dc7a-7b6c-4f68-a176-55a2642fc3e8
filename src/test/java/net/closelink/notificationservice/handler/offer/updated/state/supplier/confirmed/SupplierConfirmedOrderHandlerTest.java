package net.closelink.notificationservice.handler.offer.updated.state.supplier.confirmed;

import de.tschumacher.simplestatemachine.domain.StateChange;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.state.common.delivered.GeneralDeliveredHandler;
import net.closelink.notificationservice.handler.offer.updated.state.common.delivered.SupplierDeliveredHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class SupplierConfirmedOrderHandlerTest {

    private final GeneralDeliveredHandler generalDeliveredHandler = Mockito.mock(GeneralDeliveredHandler.class);
    private SupplierDeliveredHandler handler;
    private StateChange<OfferState> stateChange;

    @BeforeEach
    void setUp() {
        this.stateChange = DataCreator.createOfferStateChange();
        this.handler = new SupplierDeliveredHandler(this.generalDeliveredHandler);
    }

    @Test
    void shouldCallHandlerForAssigned() {
        Offer offer = DataCreator.createOffer();
        offer.setEnquiryType(EnquiryType.ASSIGNED);
        OfferUpdatedEvent offerUpdatedEvent = DataCreator.createOfferUpdatedEvent(offer);

        Mockito.when(this.generalDeliveredHandler.handle(this.stateChange, offerUpdatedEvent)).thenReturn(
            offerUpdatedEvent
        );

        OfferUpdatedEvent event = handler.handle(this.stateChange, offerUpdatedEvent);

        Mockito.verify(this.generalDeliveredHandler).handle(this.stateChange, offerUpdatedEvent);

        Assertions.assertThat(event).isEqualTo(offerUpdatedEvent);
    }

    @Test
    void shouldCallHandlerForSpot() {
        Offer offer = DataCreator.createOffer();
        offer.setEnquiryType(EnquiryType.SPOT);
        OfferUpdatedEvent offerUpdatedEvent = DataCreator.createOfferUpdatedEvent(offer);

        Mockito.when(this.generalDeliveredHandler.handle(this.stateChange, offerUpdatedEvent)).thenReturn(
            offerUpdatedEvent
        );

        OfferUpdatedEvent event = handler.handle(this.stateChange, offerUpdatedEvent);

        Mockito.verify(this.generalDeliveredHandler).handle(this.stateChange, offerUpdatedEvent);

        Assertions.assertThat(event).isEqualTo(offerUpdatedEvent);
    }

    @Test
    void shouldCallHandlerForForwarded() {
        Offer offer = DataCreator.createOffer();
        offer.setEnquiryType(EnquiryType.FORWARDED);
        OfferUpdatedEvent offerUpdatedEvent = DataCreator.createOfferUpdatedEvent(offer);

        Mockito.when(this.generalDeliveredHandler.handle(this.stateChange, offerUpdatedEvent)).thenReturn(
            offerUpdatedEvent
        );

        OfferUpdatedEvent event = handler.handle(this.stateChange, offerUpdatedEvent);

        Mockito.verify(this.generalDeliveredHandler).handle(this.stateChange, offerUpdatedEvent);

        Assertions.assertThat(event).isEqualTo(offerUpdatedEvent);
    }
}
