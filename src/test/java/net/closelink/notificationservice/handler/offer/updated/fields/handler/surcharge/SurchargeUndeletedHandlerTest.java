package net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge;

import static net.closelink.notificationservice.data.DataCreator.createOrderUpdatedEvent;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentCaptor.forClass;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.objects.Surcharge;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class SurchargeUndeletedHandlerTest {

    private SurchargeUndeletedHandler service;
    private BaseActivityLogService activityLogService;
    private SurchargeNameFormatter surchargeNameFormatter;

    @Before
    public void setUp() {
        this.activityLogService = Mockito.mock(BaseActivityLogService.class);
        this.surchargeNameFormatter = Mockito.mock(SurchargeNameFormatter.class);

        this.service = new SurchargeUndeletedHandler(this.activityLogService, this.surchargeNameFormatter);
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.activityLogService);
    }

    @Test
    public void handleTest() {
        final Surcharge newValue = DataCreator.createSurcharge();

        final OfferUpdatedEvent updateEvent = createOrderUpdatedEvent();
        updateEvent.getEventMessage().setNewValue(GsonCoder.decode(newValue));

        when(surchargeNameFormatter.getDisplayName(newValue)).thenReturn(newValue.getName());

        this.service.handle(updateEvent);

        final ArgumentCaptor<Object[]> replacementsCapture = forClass(Object[].class);

        verify(this.activityLogService).createActivityLog(
            eq(updateEvent),
            eq("activitylog.surchargeundelete"),
            replacementsCapture.capture()
        );

        assertEquals(newValue.getName(), replacementsCapture.getValue()[0]);
    }
}
