package net.closelink.notificationservice.handler.offer.reminder;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.offer.OfferConfirmReminderEventMessage;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.replacement.offer.OfferReplacementService;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementOffer;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

class OfferConfirmReminderHandlerTest {

    private OfferConfirmReminderHandler offerConfirmReminderHandler;
    private NotificationService notificationService;
    private VesselApiRestService vesselApiRestService;
    private OfferReplacementService offerReplacementService;
    private WebAppReplacementService webappReplacementService;

    @BeforeEach
    void setUp() {
        this.notificationService = Mockito.mock(NotificationService.class);
        this.vesselApiRestService = Mockito.mock(VesselApiRestService.class);
        this.offerReplacementService = Mockito.mock(OfferReplacementService.class);
        this.webappReplacementService = Mockito.mock(WebAppReplacementService.class);
        this.offerConfirmReminderHandler = new OfferConfirmReminderHandler(
            this.notificationService,
            this.offerReplacementService,
            this.vesselApiRestService,
            this.webappReplacementService
        );
    }

    @Test
    void shouldNotCallNotificationServiceIfEnquiryTypeForwarded() {
        Offer offer = DataCreator.createOffer();
        offer.setEnquiryType(EnquiryType.FORWARDED);

        final OfferConfirmReminderEventMessage eventMessage = OfferConfirmReminderEventMessage.builder()
            .offer(offer)
            .build();

        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));

        offerConfirmReminderHandler.handle(event);

        verifyNoInteractions(this.notificationService);
    }

    @Test
    void shouldNotCallNotificationServiceIfOrderTypeFuel() {
        Offer offer = DataCreator.createOffer();
        offer.setType(OrderType.FUEL);

        final OfferConfirmReminderEventMessage eventMessage = OfferConfirmReminderEventMessage.builder()
            .offer(offer)
            .build();

        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));

        offerConfirmReminderHandler.handle(event);

        verifyNoInteractions(this.notificationService);
    }

    @Test
    void shouldCallNotificationService() {
        Offer offer = DataCreator.createOffer();
        offer.setEnquiryType(EnquiryType.ASSIGNED);
        VesselMessage vessel = DataCreator.createVesselMessage();

        Map<String, String> vesselReplacements = DataCreator.createStringMap();
        Map<String, String> webAppReplacements = DataCreator.createStringMap();

        Mockito.when(this.offerReplacementService.create(ArgumentMatchers.any(ReplacementOffer.class))).thenReturn(
            vesselReplacements
        );
        Mockito.when(this.vesselApiRestService.getVessel(offer.getVesselId())).thenReturn(vessel);
        Mockito.when(
            this.webappReplacementService.create(any(ReplacementOffer.class), eq(ReceiverType.SUPPLIER))
        ).thenReturn(webAppReplacements);

        final OfferConfirmReminderEventMessage eventMessage = OfferConfirmReminderEventMessage.builder()
            .offer(offer)
            .build();

        final Event event = DataCreator.createEvent(GsonCoder.decode(eventMessage));

        offerConfirmReminderHandler.handle(event);

        final ArgumentCaptor<Notification> notificationCaptor = ArgumentCaptor.forClass(Notification.class);
        verify(this.notificationService).notify(notificationCaptor.capture());

        final Notification notification = notificationCaptor.getValue();

        Map<String, String> replacements = Stream.concat(
            vesselReplacements.entrySet().stream(),
            webAppReplacements.entrySet().stream()
        ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        Assertions.assertThat(notification.getReceiverType()).isEqualTo(ReceiverType.SUPPLIER);
        Assertions.assertThat(notification.getReceiverId()).isEqualTo(offer.getSupplierId());
        Assertions.assertThat(notification.isSendMail()).isTrue();
        Assertions.assertThat(notification.isHidden()).isTrue();
        Assertions.assertThat(notification.getSenderType()).isEqualTo(SenderType.SYSTEM);
        Assertions.assertThat(notification.getSubject()).isNotNull();
        Assertions.assertThat(notification.getTemplate()).isNotNull();
        Assertions.assertThat(notification.getMailReplacements()).isEqualTo(replacements);
        Assertions.assertThat(notification.getSubjectReplacements()[0]).isEqualTo(vessel.getName());
        Assertions.assertThat(notification.getSubjectReplacements()[1]).isEqualTo(offer.getOfferNumber());
        Assertions.assertThat(notification.getSubjectReplacements()[2]).isEqualTo(offer.getBuyerReference());
    }
}
