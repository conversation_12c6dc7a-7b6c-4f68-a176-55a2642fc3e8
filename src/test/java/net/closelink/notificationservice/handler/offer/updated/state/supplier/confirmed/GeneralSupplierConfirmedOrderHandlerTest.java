package net.closelink.notificationservice.handler.offer.updated.state.supplier.confirmed;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;

import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.common.delivered.GeneralDeliveredHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierHandlerTest;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class GeneralSupplierConfirmedOrderHandlerTest extends SupplierHandlerTest {

    private static final String SUPPLIER_MESSAGE = "delivered.customer.message";
    private static final String SUPPLIER_TEMPLATE = "Order_Supplier_Order_Delivered_New";

    private static final String CUSTOMER_MESSAGE = "delivered.supplier.message";
    private static final String CUSTOMER_TEMPLATE = "Order_Customer_Order_Delivered_New";

    private CustomerStateHandlerSupport customerStateHandlerSupport;
    private GeneralDeliveredHandler handler;

    @Override
    @Before
    public void setUp() {
        super.setUp();

        this.customerStateHandlerSupport = Mockito.mock(CustomerStateHandlerSupport.class);
        Mockito.when(this.customerStateHandlerSupport.createNotificationBuilder(this.event)).thenReturn(
            DataCreator.createNotificationBuilder()
        );

        this.handler = new GeneralDeliveredHandler(this.supplierStateHandlerSupport, this.customerStateHandlerSupport);
    }

    @Override
    @After
    public void afterTest() {
        super.afterTest();
    }

    @Test
    public void handleTestWithoutBuyerRef() {
        this.event.getEventMessage().setOffer(DataCreator.createOffer(EnquiryType.ASSIGNED));
        this.event.getEventMessage().getOffer().setBuyerReference(null);
        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final ArgumentCaptor<Notification> notificationCaptor = ArgumentCaptor.forClass(Notification.class);

        verify(this.customerStateHandlerSupport).notify(notificationCaptor.capture());

        Notification supplierNotification = notificationCaptor.getValue();

        assertEquals(SUPPLIER_MESSAGE, supplierNotification.getMessage());
        assertEquals("delivered.customer.subject", supplierNotification.getSubject());
        assertEquals(SUPPLIER_TEMPLATE, supplierNotification.getTemplate());
        assertTrue(supplierNotification.isHidden());

        verify(this.supplierStateHandlerSupport).notify(notificationCaptor.capture());

        Notification customerNotification = notificationCaptor.getValue();

        assertEquals(CUSTOMER_MESSAGE, customerNotification.getMessage());
        assertEquals("delivered.supplier.subject", customerNotification.getSubject());
        assertEquals(CUSTOMER_TEMPLATE, customerNotification.getTemplate());
        assertFalse(customerNotification.isHidden());

        verify(this.customerStateHandlerSupport).createNotificationBuilder(this.event);
        Mockito.verifyNoMoreInteractions(this.customerStateHandlerSupport);
    }

    @Test
    public void handleTestWithBuyerRef() {
        this.event.getEventMessage().setOffer(DataCreator.createOffer(EnquiryType.ASSIGNED));
        this.event.getEventMessage().getOffer().setBuyerReference("Test 123");
        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final ArgumentCaptor<Notification> notificationCaptor = ArgumentCaptor.forClass(Notification.class);

        verify(this.customerStateHandlerSupport).notify(notificationCaptor.capture());

        Notification supplierNotification = notificationCaptor.getValue();

        assertEquals(SUPPLIER_MESSAGE, supplierNotification.getMessage());
        assertEquals("deliveredBuyerRef.customer.subject", supplierNotification.getSubject());
        assertEquals(SUPPLIER_TEMPLATE, supplierNotification.getTemplate());
        assertTrue(supplierNotification.isHidden());

        verify(this.supplierStateHandlerSupport).notify(notificationCaptor.capture());

        Notification customerNotification = notificationCaptor.getValue();

        assertEquals(CUSTOMER_MESSAGE, customerNotification.getMessage());
        assertEquals("deliveredBuyerRef.supplier.subject", customerNotification.getSubject());
        assertEquals(CUSTOMER_TEMPLATE, customerNotification.getTemplate());
        assertFalse(customerNotification.isHidden());

        verify(this.customerStateHandlerSupport).createNotificationBuilder(this.event);
        Mockito.verifyNoMoreInteractions(this.customerStateHandlerSupport);
    }

    @Test
    public void handleForwardedTestWithoutBuyerRef() {
        this.event.getEventMessage().setOffer(DataCreator.createOffer(EnquiryType.FORWARDED));
        this.event.getEventMessage().getOffer().setBuyerReference(null);

        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final ArgumentCaptor<Notification> notificationCaptor = ArgumentCaptor.forClass(Notification.class);

        verifyNoMoreInteractions(this.customerStateHandlerSupport);

        verify(this.supplierStateHandlerSupport).notify(notificationCaptor.capture());

        Notification customerNotification = notificationCaptor.getValue();

        assertEquals(CUSTOMER_MESSAGE, customerNotification.getMessage());
        assertEquals("delivered.supplier.subject", customerNotification.getSubject());
        assertEquals(CUSTOMER_TEMPLATE, customerNotification.getTemplate());
        assertFalse(customerNotification.isHidden());
    }

    @Test
    public void handleForwardedTestWithBuyerRef() {
        this.event.getEventMessage().setOffer(DataCreator.createOffer(EnquiryType.FORWARDED));
        this.event.getEventMessage().getOffer().setBuyerReference("Test 123");

        final OfferUpdatedEvent resultEvent = this.handler.handle(this.stateChange, this.event);

        assertEquals(resultEvent, this.event);

        final ArgumentCaptor<Notification> notificationCaptor = ArgumentCaptor.forClass(Notification.class);

        verifyNoMoreInteractions(this.customerStateHandlerSupport);

        verify(this.supplierStateHandlerSupport).notify(notificationCaptor.capture());

        Notification customerNotification = notificationCaptor.getValue();

        assertEquals(CUSTOMER_MESSAGE, customerNotification.getMessage());
        assertEquals("deliveredBuyerRef.supplier.subject", customerNotification.getSubject());
        assertEquals(CUSTOMER_TEMPLATE, customerNotification.getTemplate());
        assertFalse(customerNotification.isHidden());
    }
}
