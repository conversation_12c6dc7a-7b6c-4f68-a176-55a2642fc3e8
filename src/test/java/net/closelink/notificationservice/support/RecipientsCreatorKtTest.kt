package net.closelink.notificationservice.support

import net.closelink.notificationservice.domainobject.Recipient
import net.closelink.notificationservice.service.rest.vessel.domain.MailNotificationSettings
import net.closelink.notificationservice.service.rest.vessel.domain.MailNotificationSettings.NotificationSettings
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class RecipientsCreatorKtTest {
    @Test
    fun shouldNotSendNotifications() {
        val notificationSettings =
            NotificationSettings.builder()
                .enabled(false)
                .sendToOthers(true)
                .sendToVessel(true)
                .recipients(listOf("<EMAIL>"))
                .build()
        val vessel =
            VesselMessage.builder()
                .email(null)
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(notificationSettings)
                        .build()
                )
                .build()

        // When
        val recipients =
            createRecipientListForVesselNotification(vessel.email, notificationSettings)

        // Then
        assertTrue(recipients.isEmpty())
    }

    @Test
    fun shouldSendNotificationToVessel() {
        val notificationSettings =
            NotificationSettings.builder()
                .enabled(true)
                .sendToOthers(false)
                .sendToVessel(true)
                .recipients(listOf("<EMAIL>"))
                .build()
        val vessel =
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(notificationSettings)
                        .build()
                )
                .build()

        // When
        val recipients =
            createRecipientListForVesselNotification(vessel.email, notificationSettings)
                .stream()
                .map { it.emailAddress }
                .toList()

        // Then
        assertTrue(recipients.contains("<EMAIL>"))
    }

    @Test
    fun shouldSendNotificationToOthers() {
        // Given
        val notificationSettings =
            NotificationSettings.builder()
                .enabled(true)
                .sendToOthers(true)
                .sendToVessel(false)
                .recipients(listOf("<EMAIL>"))
                .build()
        val vessel =
            VesselMessage.builder()
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(notificationSettings)
                        .build()
                )
                .build()

        // When
        val recipients =
            createRecipientListForVesselNotification(vessel.email, notificationSettings)
                .stream()
                .map { obj: Recipient -> obj.emailAddress }
                .toList()
        // Then
        assertTrue(recipients.contains("<EMAIL>"))
    }

    @Test
    fun shouldSendNotificationToAll() {
        // Given
        val notificationSettings =
            NotificationSettings.builder()
                .enabled(true)
                .sendToOthers(true)
                .sendToVessel(true)
                .recipients(listOf("<EMAIL>"))
                .build()
        val vessel =
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(notificationSettings)
                        .build()
                )
                .build()

        // When
        val recipients =
            createRecipientListForVesselNotification(vessel.email, notificationSettings)
                .stream()
                .map { obj: Recipient -> obj.emailAddress }
                .toList()
        // Then
        assertTrue(recipients.containsAll(listOf("<EMAIL>", "<EMAIL>")))
    }

    @Test
    fun shouldNotSendNotificationWithDisabledFlagButOthersTrue() {
        // Given
        val notificationSettings =
            NotificationSettings.builder()
                .enabled(false)
                .sendToOthers(true)
                .sendToVessel(true)
                .recipients(listOf("<EMAIL>"))
                .build()
        val vessel =
            VesselMessage.builder()
                .email("<EMAIL>")
                .mailNotificationSettings(
                    MailNotificationSettings.builder()
                        .orderUpdateSettings(notificationSettings)
                        .build()
                )
                .build()

        // When
        val recipients =
            createRecipientListForVesselNotification(vessel.email, notificationSettings)
                .stream()
                .map { obj: Recipient -> obj.emailAddress }
                .toList()
        // Then
        assertTrue(recipients.isEmpty())
    }
}
