package net.closelink.notificationservice.service.rest.coredata;

import java.util.HashMap;
import net.closelink.notificationservice.properties.RestProperties;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;
import retrofit2.mock.Calls;

class CoreDataServiceTest {

    private CoreDataService coreDataService;
    private CoreDataServiceClient coreDataServiceClient = Mockito.mock(CoreDataServiceClient.class);
    private RestProperties restProperties = Mockito.mock(RestProperties.class);

    @BeforeEach
    public void setUp() {
        RestProperties.Service service = new RestProperties.Service();
        service.setBaseUrl("https://closelink.net");
        Mockito.when(restProperties.getCoredataService()).thenReturn(service);

        coreDataService = new CoreDataService(restProperties);

        ReflectionTestUtils.setField(coreDataService, "coreDataServiceClient", coreDataServiceClient);
    }

    @Test
    public void shouldReturnNullIfEnumIsNotFound() {
        HashMap<String, EnumMessage.DetailEnumMessage> enumValueToMessage = new HashMap<>();
        enumValueToMessage.put(
            "AE_CIRC",
            EnumMessage.DetailEnumMessage.builder().humanReadableValue("AE CIRC").build()
        );

        EnumMessage enumMessage = EnumMessage.builder().vesselTankCategory(enumValueToMessage).build();

        Mockito.when(coreDataServiceClient.getEnums()).thenReturn(Calls.response(enumMessage));

        String humanReadableCategory = coreDataService.getHumanReadableValueForVesselTankCategory("TEST");

        Assertions.assertThat(humanReadableCategory).isNull();
    }

    @Test
    public void shouldReturnHumanReadableValueIfEnumIsFound() {
        HashMap<String, EnumMessage.DetailEnumMessage> enumValueToMessage = new HashMap<>();
        enumValueToMessage.put(
            "AE_CIRC",
            EnumMessage.DetailEnumMessage.builder().humanReadableValue("AE CIRC").build()
        );

        EnumMessage enumMessage = EnumMessage.builder().vesselTankCategory(enumValueToMessage).build();

        Mockito.when(coreDataServiceClient.getEnums()).thenReturn(Calls.response(enumMessage));

        String humanReadableCategory = coreDataService.getHumanReadableValueForVesselTankCategory("AE_CIRC");

        Assertions.assertThat(humanReadableCategory).isEqualTo("AE CIRC");
    }
}
