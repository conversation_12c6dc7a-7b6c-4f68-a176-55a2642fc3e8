package net.closelink.notificationservice.service.notification;

import static net.closelink.notificationservice.data.DataCreator.createNotification;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import net.closelink.notificationservice.data.MessageSourceCreator;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.service.rest.message.MessageApiRestService;
import net.closelink.notificationservice.service.rest.message.domain.MessageMessage;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.context.MessageSource;

public class NotificationServiceTest {

    private final MessageSource messageSource = MessageSourceCreator.createMessageSource();
    private final MessageApiRestService messageApiRestService = mock(MessageApiRestService.class);
    private final NotificationPreferenceService notificationPreferenceService = mock(
        NotificationPreferenceService.class
    );

    private NotificationService service;

    @BeforeEach
    public void setUp() {
        this.service = new DefaultNotificationService(
            this.messageSource,
            this.messageApiRestService,
            this.notificationPreferenceService
        );
    }

    @AfterEach
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.messageApiRestService);
        Mockito.verifyNoMoreInteractions(this.notificationPreferenceService);
    }

    @Test
    public void shouldSendNotificationWithNoCategory() {
        var notification = createNotification("test.notification.subject", "test.notification.message")
            .toBuilder()
            .category(null)
            .build();

        when(
            notificationPreferenceService.shouldSendNotification(
                notification.getReceiverType(),
                notification.getReceiverId(),
                notification.getCategory()
            )
        ).thenReturn(true);

        this.service.notify(notification);

        verify(notificationPreferenceService).shouldSendNotification(
            notification.getReceiverType(),
            notification.getReceiverId(),
            notification.getCategory()
        );
        verifyNotifySendMessage(notification);
    }

    private void verifyNotifySendMessage(final Notification notification) {
        final String prefix = "test";
        final String messageTranslation =
            prefix + notification.getMessageReplacements()[0] + notification.getMessageReplacements()[1];
        final String subjectTranslation =
            prefix + notification.getSubjectReplacements()[0] + notification.getSubjectReplacements()[1];

        final ArgumentCaptor<MessageMessage> messageCapture = ArgumentCaptor.forClass(MessageMessage.class);
        verify(this.messageApiRestService).createMessage(messageCapture.capture());

        final MessageMessage message = messageCapture.getValue();
        assertThat(message.getMessage()).isEqualTo(messageTranslation);
        assertThat(message.getSubject()).isEqualTo(subjectTranslation);
        assertThat(message.getReceiverId()).isEqualTo(notification.getReceiverId());
        assertThat(message.getReceiverType().toString()).isEqualTo(notification.getReceiverType().toString());
        assertThat(message.getSenderId()).isEqualTo(notification.getSenderId());
        assertThat(message.getSenderType().toString()).isEqualTo(notification.getSenderType().toString());
        assertThat(message.isGroupMail()).isEqualTo(notification.isGroupMail());
        assertThat(message.isHidden()).isEqualTo(notification.isHidden());
        assertThat(message.isSendMail()).isEqualTo(notification.isSendMail());
        assertThat(message.getTemplate()).isEqualTo(notification.getTemplate());
        assertThat(message.getOfferId()).isEqualTo(notification.getOfferId());
        assertThat(message.getReplacements()).isEqualTo(notification.getMailReplacements());
    }
}
