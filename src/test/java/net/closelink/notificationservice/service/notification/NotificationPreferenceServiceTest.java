package net.closelink.notificationservice.service.notification;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.service.rest.company.EmailCategorySettings;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerSettingsResponseMessage;
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierSettingsResponseMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

class NotificationPreferenceServiceTest {

    private final CustomerApiRestService customerApiRestService = mock(CustomerApiRestService.class);
    private final SupplierApiRestService supplierApiRestService = mock(SupplierApiRestService.class);
    private NotificationPreferenceService notificationPreferenceService;

    @BeforeEach
    public void setUp() {
        this.notificationPreferenceService = new NotificationPreferenceService(
            this.supplierApiRestService,
            this.customerApiRestService
        );
    }

    @ParameterizedTest
    @EnumSource(value = NotificationCategory.class)
    public void shouldSendCustomerNotificationWithCategory(NotificationCategory notificationCategory) {
        var customerId = "customerId-1";

        when(this.customerApiRestService.getSettings(customerId)).thenReturn(
            CustomerSettingsResponseMessage.builder()
                .emailCategorySettings(
                    EmailCategorySettings.builder()
                        .newChatMessage(true)
                        .offerStateChange(true)
                        .systemReminder(true)
                        .offerUpdate(true)
                        .keyPortCallReminder(true)
                        .openEnquiryReminder(true)
                        .orderDeliveryReminder(true)
                        .safetyReserveLevelReminder(true)
                        .stockWarningLevelReminder(true)
                        .keyPortCallReminder(true)
                        .openEnquiryReminder(true)
                        .orderDeliveryReminder(true)
                        .safetyReserveLevelReminder(true)
                        .stockWarningLevelReminder(true)
                        .build()
                )
                .build()
        );

        var shouldSendNotification =
            this.notificationPreferenceService.shouldSendNotification(
                    ReceiverType.CUSTOMER,
                    customerId,
                    notificationCategory
                );

        assertThat(shouldSendNotification).isTrue();

        verify(customerApiRestService).getSettings(customerId);
    }

    @ParameterizedTest
    @EnumSource(value = NotificationCategory.class)
    public void shouldNotSendCustomerNotificationWithCategory(NotificationCategory notificationCategory) {
        var customerId = "customerId-1";

        when(this.customerApiRestService.getSettings(customerId)).thenReturn(
            CustomerSettingsResponseMessage.builder()
                .emailCategorySettings(
                    EmailCategorySettings.builder()
                        .newChatMessage(false)
                        .offerStateChange(false)
                        .systemReminder(false)
                        .offerUpdate(false)
                        .keyPortCallReminder(false)
                        .openEnquiryReminder(false)
                        .orderDeliveryReminder(false)
                        .safetyReserveLevelReminder(false)
                        .stockWarningLevelReminder(false)
                        .build()
                )
                .build()
        );

        var shouldSendNotification =
            this.notificationPreferenceService.shouldSendNotification(
                    ReceiverType.CUSTOMER,
                    customerId,
                    notificationCategory
                );

        assertThat(shouldSendNotification).isFalse();

        verify(this.customerApiRestService).getSettings(customerId);
    }

    @ParameterizedTest
    @EnumSource(
        value = NotificationCategory.class,
        names = {
            "OPEN_ENQUIRY_REMINDER",
            "ORDER_DELIVERY_REMINDER",
            "KEY_PORT_CALL_REMINDER",
            "STOCK_WARNING_LEVEL_REMINDER",
            "SAFETY_RESERVE_LEVEL_REMINDER",
        },
        mode = EnumSource.Mode.EXCLUDE
    )
    public void shouldSendSupplierNotificationWithCategory(NotificationCategory notificationCategory) {
        var supplierId = "supplierId-1";

        when(this.supplierApiRestService.getSettings(supplierId)).thenReturn(
            SupplierSettingsResponseMessage.builder()
                .emailCategorySettings(
                    EmailCategorySettings.builder()
                        .newChatMessage(true)
                        .offerStateChange(true)
                        .systemReminder(true)
                        .offerUpdate(true)
                        .build()
                )
                .build()
        );

        var shouldSendNotification =
            this.notificationPreferenceService.shouldSendNotification(
                    ReceiverType.SUPPLIER,
                    supplierId,
                    notificationCategory
                );

        assertThat(shouldSendNotification).isTrue();

        verify(this.supplierApiRestService).getSettings(supplierId);
    }

    @ParameterizedTest
    @EnumSource(value = NotificationCategory.class)
    public void shouldNotSendSupplierNotificationWithCategory(NotificationCategory notificationCategory) {
        var supplierId = "supplierId-1";

        when(this.supplierApiRestService.getSettings(supplierId)).thenReturn(
            SupplierSettingsResponseMessage.builder()
                .emailCategorySettings(
                    EmailCategorySettings.builder()
                        .newChatMessage(false)
                        .offerStateChange(false)
                        .systemReminder(false)
                        .offerUpdate(false)
                        .build()
                )
                .build()
        );

        var shouldSendNotification =
            this.notificationPreferenceService.shouldSendNotification(
                    ReceiverType.SUPPLIER,
                    supplierId,
                    notificationCategory
                );

        assertThat(shouldSendNotification).isFalse();

        verify(this.supplierApiRestService).getSettings(supplierId);
    }

    @ParameterizedTest
    @EnumSource(value = ReceiverType.class, names = { "SUPPLIER", "CUSTOMER" }, mode = EnumSource.Mode.EXCLUDE)
    public void shouldSendNotifications(ReceiverType receiverType) {
        var shouldSendNotification =
            this.notificationPreferenceService.shouldSendNotification(
                    receiverType,
                    "anyReceiverId",
                    NotificationCategory.OFFER_UPDATE
                );

        assertThat(shouldSendNotification).isTrue();

        verifyNoInteractions(this.supplierApiRestService);
        verifyNoInteractions(this.customerApiRestService);
    }
}
