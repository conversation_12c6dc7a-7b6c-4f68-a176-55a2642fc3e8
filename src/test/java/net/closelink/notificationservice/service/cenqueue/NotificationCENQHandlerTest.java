package net.closelink.notificationservice.service.cenqueue;

import static net.closelink.notificationservice.data.DataCreator.createEvent;
import static org.mockito.Mockito.*;

import net.closelink.cenqueue.domainobject.Event;
import net.closelink.notificationservice.exception.NoHandlerFoundException;
import net.closelink.notificationservice.handler.EventHandler;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class NotificationCENQHandlerTest {

    private NotificationCENQHandler service;
    private EventHandlerRegistry handlerRegistry;

    @Before
    public void setUp() {
        this.handlerRegistry = mock(EventHandlerRegistry.class);
        this.service = new NotificationCENQHandler(this.handlerRegistry);
    }

    @After
    public void afterTest() {
        verifyNoMoreInteractions(this.handlerRegistry);
    }

    @Test
    public void handleTest() {
        final Event event = createEvent();
        final EventHandler eventHandler = mock(EventHandler.class);

        when(this.handlerRegistry.get(event.getEventType())).thenReturn(eventHandler);

        this.service.handle(event);

        verify(eventHandler).handle(event);
        verifyNoMoreInteractions(eventHandler);
        verify(this.handlerRegistry).get(event.getEventType());
    }

    @Test
    public void handleFailTest() {
        final Event event = createEvent();
        when(this.handlerRegistry.get(event.getEventType())).thenThrow(new NoHandlerFoundException());

        this.service.handle(event);

        verify(this.handlerRegistry).get(event.getEventType());
    }

    @Test
    public void shouldExitEarlyIfEventTypeIsNull() {
        final Event event = createEvent();
        event.setEventType(null);

        this.service.handle(event);

        verifyNoInteractions(this.handlerRegistry);
    }
}
