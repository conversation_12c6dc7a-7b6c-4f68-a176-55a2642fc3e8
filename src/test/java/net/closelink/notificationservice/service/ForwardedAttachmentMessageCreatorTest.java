package net.closelink.notificationservice.service;

import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.domainobject.Attachment;
import net.closelink.notificationservice.service.attachment.ForwardedAttachmentCreator;
import net.closelink.notificationservice.service.rest.export.ExportApiRestService;
import net.closelink.notificationservice.service.rest.export.domain.OfferExportMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class ForwardedAttachmentMessageCreatorTest {

    private final ExportApiRestService exportApiRestService = Mockito.mock(ExportApiRestService.class);
    private final VesselApiRestService vesselApiRestService = Mockito.mock(VesselApiRestService.class);
    private ForwardedAttachmentCreator forwardedAttachmentCreator;

    @BeforeEach
    void setUp() {
        this.forwardedAttachmentCreator = new ForwardedAttachmentCreator(exportApiRestService, vesselApiRestService);
    }

    @Test
    void shouldCreateAttachment() {
        String offerId = "123-456";
        String vesselId = "987-654";

        // Fri Nov 15 2019 10:47:13
        Long dateDeliveryMillis = 1573814833326L;

        Offer offer = DataCreator.createOffer();
        offer.setId(offerId);
        offer.setVesselId(vesselId);
        offer.setDateDelivery(dateDeliveryMillis);

        String attachmentUrl = "https://google.com";

        OfferExportMessage offerExport = new OfferExportMessage();
        offerExport.setUrl(attachmentUrl);
        offerExport.setId("123");

        Mockito.when(exportApiRestService.getOfferExport(offerId)).thenReturn(offerExport);

        String vesselName = "MS Classica";
        VesselMessage vessel = DataCreator.createVesselMessage();
        vessel.setName(vesselName);

        Mockito.when(vesselApiRestService.getVessel(vesselId)).thenReturn(vessel);

        Attachment attachment = forwardedAttachmentCreator.createAttachment(offer, "Order %s %s.pdf");

        Assertions.assertThat(attachment.getName()).isEqualTo("Order MS Classica Nov 15, 2019.pdf");
        Assertions.assertThat(attachment.getUrl()).isEqualTo(attachmentUrl);
    }
}
