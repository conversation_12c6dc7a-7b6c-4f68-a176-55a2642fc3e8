package net.closelink.notificationservice.data;

import de.tschumacher.simplestatemachine.domain.StateChange;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestCreatedEventMessage;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestPendingReminderEventMessage;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestUpdatedEventMessage;
import net.closelink.cenqueue.domainobject.company.AssignmentCreatedEventMessage;
import net.closelink.cenqueue.domainobject.lead.LeadCreatedEventMessage;
import net.closelink.cenqueue.domainobject.offer.OfferDeliveryReminderEventMessage;
import net.closelink.cenqueue.domainobject.offer.OfferMessageEventMessage;
import net.closelink.cenqueue.domainobject.offer.OfferUpdatedEventMessage;
import net.closelink.cenqueue.domainobject.order.OrderCreatedEventMessage;
import net.closelink.cenqueue.domainobject.order.OrderOpenReminderEventMessage;
import net.closelink.cenqueue.domainobject.order.OrderUpdatedEventMessage;
import net.closelink.cenqueue.domainobject.passwordreset.PasswordResetRequestCreatedMessage;
import net.closelink.cenqueue.domainobject.passwordreset.PasswordResetRequestResetMessage;
import net.closelink.cenqueue.domainobject.price.PriceListUploadedEventMessage;
import net.closelink.cenqueue.domainobject.user.UserInvitedEventMessage;
import net.closelink.cenqueue.domainobject.vesselrequisition.VesselRequisitionCreatedEventMessage;
import net.closelink.cenqueue.domainobject.vesselrequisition.VesselRequisitionImportedEventMessage;
import net.closelink.cenqueue.domainvalue.offer.OfferUpdatedEventType;
import net.closelink.cenqueue.domainvalue.order.OrderUpdatedEventType;
import net.closelink.cenqueue.objects.ApprovalRequest;
import net.closelink.cenqueue.objects.Assignment;
import net.closelink.cenqueue.objects.Item;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.objects.Order;
import net.closelink.cenqueue.objects.PercentRange;
import net.closelink.cenqueue.objects.Samplekit;
import net.closelink.cenqueue.objects.Surcharge;
import net.closelink.cenqueue.objects.User;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.Notification.NotificationBuilder;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.order.updated.support.OrderUpdatedEvent;
import net.closelink.notificationservice.properties.WebappProperties;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementItem;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementOffer;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementSamplekit;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementSurcharge;
import net.closelink.notificationservice.replacement.order.domain.ReplacementOrder;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.message.domain.MessageMessage;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;
import net.closelink.notificationservice.service.rest.order.domain.OrderMessage;
import net.closelink.notificationservice.service.rest.port.domain.PortMessage;
import net.closelink.notificationservice.service.rest.product.domain.ProductMessage;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierMessage;
import net.closelink.notificationservice.service.rest.user.domain.UserMessage;
import net.closelink.notificationservice.service.rest.vessel.domain.MailNotificationSettings;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import net.closelink.test.podam.DefaultDataCreator;

public class DataCreator extends DefaultDataCreator {

    public static SupplierMessage createSupplierMessage() {
        return factory.manufacturePojo(SupplierMessage.class);
    }

    public static Event createEvent() {
        return factory.manufacturePojo(Event.class);
    }

    public static Notification createNotification(final String subject, final String message) {
        return Notification.builder()
            .groupMail(createBoolean())
            .message(message)
            .messageReplacements(createStringArray())
            .sendMail(createBoolean())
            .subject(subject)
            .subjectReplacements(createStringArray())
            .template(createString())
            .hidden(true)
            .receiverId(createString())
            .receiverType(createReceiverType())
            .senderId(createString())
            .senderType(createSenderType())
            .offerId(createString())
            .build();
    }

    private static SenderType createSenderType() {
        return factory.manufacturePojo(SenderType.class);
    }

    private static ReceiverType createReceiverType() {
        return factory.manufacturePojo(ReceiverType.class);
    }

    public static Event createEvent(final String rawMessage) {
        final Event event = createEvent();
        event.setRawMessage(rawMessage);
        return event;
    }

    public static PriceListUploadedEventMessage createPriceListUploadedEventMessage() {
        return PriceListUploadedEventMessage.builder()
            .customerId(createString())
            .supplierGroupId(createString())
            .build();
    }

    public static UserInvitedEventMessage createUserInvitedEventMessage(
        final net.closelink.cenqueue.objects.User user
    ) {
        return UserInvitedEventMessage.builder().onboardingLink(createString()).user(user).build();
    }

    public static UserMessage createUserMessage() {
        return factory.manufacturePojo(UserMessage.class);
    }

    public static Offer createOffer() {
        final Offer offer = factory.manufacturePojo(Offer.class);
        offer.setDateCreated(OffsetDateTime.now().toString());
        offer.setEnquiryType(EnquiryType.ASSIGNED);
        offer.setType(OrderType.LUBES);
        return offer;
    }

    public static VesselMessage createVesselMessage() {
        var vesselMessage = factory.manufacturePojo(VesselMessage.class);
        vesselMessage.setMailNotificationSettings(
            MailNotificationSettings.builder()
                .orderUpdateSettings(
                    MailNotificationSettings.NotificationSettings.builder()
                        .enabled(false)
                        .sendToVessel(false)
                        .sendToOthers(false)
                        .build()
                )
                .keyPortCallSettings(
                    MailNotificationSettings.NotificationSettings.builder()
                        .enabled(false)
                        .sendToVessel(false)
                        .sendToOthers(false)
                        .build()
                )
                .stockWarningSettings(
                    MailNotificationSettings.NotificationSettings.builder()
                        .enabled(false)
                        .sendToVessel(false)
                        .sendToOthers(false)
                        .build()
                )
                .safetyReserveSettings(
                    MailNotificationSettings.NotificationSettings.builder()
                        .enabled(false)
                        .sendToVessel(false)
                        .sendToOthers(false)
                        .build()
                )
                .build()
        );

        return vesselMessage;
    }

    public static CustomerMessage createCustomerMessage() {
        return factory.manufacturePojo(CustomerMessage.class);
    }

    public static PortMessage createPortMessage() {
        return factory.manufacturePojo(PortMessage.class);
    }

    public static List<ReplacementSamplekit> createReplacementSamplekits() {
        return new ArrayList<>(
            Arrays.asList(createReplacementSamplekit(), createReplacementSamplekit(), createReplacementSamplekit())
        );
    }

    private static ReplacementSamplekit createReplacementSamplekit() {
        return factory.manufacturePojo(ReplacementSamplekit.class);
    }

    public static List<ReplacementItem> createReplacementItems() {
        return new ArrayList<>(
            Arrays.asList(createReplacementItem(), createReplacementItem(), createReplacementItem())
        );
    }

    private static ReplacementItem createReplacementItem() {
        return factory.manufacturePojo(ReplacementItem.class);
    }

    public static ProductMessage createProductMessage() {
        return factory.manufacturePojo(ProductMessage.class);
    }

    public static List<ReplacementSurcharge> createReplacementSurcharges() {
        return new ArrayList<>(
            Arrays.asList(createReplacementSurcharge(), createReplacementSurcharge(), createReplacementSurcharge())
        );
    }

    private static ReplacementSurcharge createReplacementSurcharge() {
        return factory.manufacturePojo(ReplacementSurcharge.class);
    }

    public static OrderMessage createOrderMessage() {
        final OrderMessage order = factory.manufacturePojo(OrderMessage.class);
        order.setDateCreated(OffsetDateTime.now().toString());
        return order;
    }

    public static OfferUpdatedEvent createOfferUpdatedEvent(final Offer offer) {
        return OfferUpdatedEvent.builder().eventMessage(createOrderUpdatedEventMessage(offer)).build();
    }

    public static OfferUpdatedEvent createOrderUpdatedStateEvent(final Offer offer, final Event event) {
        final OfferUpdatedEventMessage eventMessage = createOrderUpdatedEventMessage(offer);
        eventMessage.setOldValue(GsonCoder.decode(createOfferState()));
        eventMessage.setNewValue(GsonCoder.decode(createOfferState()));
        eventMessage.setEventType(OfferUpdatedEventType.STATE_CHANGED);
        return OfferUpdatedEvent.builder().eventMessage(eventMessage).event(event).build();
    }

    private static OfferUpdatedEventMessage createOrderUpdatedEventMessage(final Offer offer) {
        final OfferUpdatedEventMessage eventMessage = factory.manufacturePojo(OfferUpdatedEventMessage.class);
        eventMessage.setOffer(offer);
        return eventMessage;
    }

    public static StateChange<OfferState> createOfferStateChange() {
        return new StateChange<>(createOfferState(), createOfferState());
    }

    private static OfferState createOfferState() {
        return factory.manufacturePojo(OfferState.class);
    }

    public static NotificationBuilder createNotificationBuilder() {
        return Notification.builder();
    }

    public static void makeSpotOrder(final Offer offer) {
        offer.setOrderId(DataCreator.createId());
        offer.setEnquiryType(EnquiryType.SPOT);
    }

    public static OfferUpdatedEventType createOfferUpdatedEventType() {
        return factory.manufacturePojo(OfferUpdatedEventType.class);
    }

    public static OfferUpdatedEventMessage createOfferUpdatedEventMessage(final OfferUpdatedEventType eventType) {
        return OfferUpdatedEventMessage.builder()
            .offer(Offer.builder().type(OrderType.LUBES).build())
            .eventType(eventType)
            .build();
    }

    public static Event createEvent(final OfferUpdatedEventMessage eventMessage) {
        final Event event = createEvent();
        event.setRawMessage(GsonCoder.decode(eventMessage));
        return event;
    }

    public static Event createEvent(final ApprovalRequestUpdatedEventMessage eventMessage) {
        final Event event = createEvent();
        event.setRawMessage(GsonCoder.decode(eventMessage));
        return event;
    }

    public static Event createEvent(final ApprovalRequestPendingReminderEventMessage eventMessage) {
        final Event event = createEvent();
        event.setRawMessage(GsonCoder.decode(eventMessage));
        return event;
    }

    public static Event createEvent(final UserType companyType) {
        final Event event = createEvent();
        event.getEventTrigger().setCompanyType(companyType);
        return event;
    }

    public static OfferUpdatedEvent createOrderUpdatedEvent() {
        return factory.manufacturePojo(OfferUpdatedEvent.class);
    }

    public static OfferDeliveryReminderEventMessage createOrderDeliveryReminderEventMessage(final Offer offer) {
        return OfferDeliveryReminderEventMessage.builder().offer(offer).build();
    }

    public static Map<String, String> createStringMap() {
        final Map<String, String> map = new HashMap<>();
        for (int i = 0; i < 5; i++) {
            map.put(createString(), createString());
        }
        return map;
    }

    public static OrderOpenReminderEventMessage createOrderOpenReminderEventMessage(
        final net.closelink.cenqueue.objects.Order order
    ) {
        return OrderOpenReminderEventMessage.builder().order(order).build();
    }

    public static OrderCreatedEventMessage createOrderCreatedEventMessage(
        final net.closelink.cenqueue.objects.Order order
    ) {
        return OrderCreatedEventMessage.builder().order(order).build();
    }

    public static OrderUpdatedEventMessage createOrderUpdatedEventMessage(
        final OrderUpdatedEventType updatedEventType
    ) {
        return OrderUpdatedEventMessage.builder()
            .order(Order.builder().type(OrderType.LUBES).build())
            .eventType(updatedEventType)
            .build();
    }

    public static OrderUpdatedEventType createOrderUpdatedEventType() {
        return factory.manufacturePojo(OrderUpdatedEventType.class);
    }

    public static Event createEvent(final OrderUpdatedEventMessage eventMessage) {
        final Event event = createEvent();
        event.setRawMessage(GsonCoder.decode(eventMessage));
        return event;
    }

    public static OrderUpdatedEvent createOrderUpdatedEvent(
        final net.closelink.cenqueue.objects.Order order,
        final Event event
    ) {
        final OrderUpdatedEventMessage eventMessage = createOrderUpdatedEventMessage(order);
        eventMessage.setOldValue(GsonCoder.decode(createOffsetDateTime()));
        eventMessage.setNewValue(GsonCoder.decode(createOffsetDateTime()));
        eventMessage.setEventType(OrderUpdatedEventType.DATE_DELIVERY_CHANGED);
        return OrderUpdatedEvent.builder().eventMessage(eventMessage).event(event).build();
    }

    private static OrderUpdatedEventMessage createOrderUpdatedEventMessage(
        final net.closelink.cenqueue.objects.Order order
    ) {
        final OrderUpdatedEventMessage eventMessage = factory.manufacturePojo(OrderUpdatedEventMessage.class);
        eventMessage.setOrder(order);
        return eventMessage;
    }

    public static MessageMessage createMessageMessage() {
        return factory.manufacturePojo(MessageMessage.class);
    }

    public static Event createEvent(final OfferMessageEventMessage eventMessage) {
        final Event event = createEvent();
        event.setRawMessage(GsonCoder.decode(eventMessage));
        return event;
    }

    public static Event createEvent(final LeadCreatedEventMessage eventMessage) {
        final Event event = createEvent();
        event.setRawMessage(GsonCoder.decode(eventMessage));
        return event;
    }

    public static Event createEvent(final VesselRequisitionCreatedEventMessage eventMessage) {
        final Event event = createEvent();
        event.setRawMessage(GsonCoder.decode(eventMessage));
        return event;
    }

    public static Event createEvent(final VesselRequisitionImportedEventMessage eventMessage) {
        final Event event = createEvent();
        event.setRawMessage(GsonCoder.decode(eventMessage));
        return event;
    }

    public static OfferMessageEventMessage createOfferMessageEventMessage(final Offer offer, final String messageId) {
        return OfferMessageEventMessage.builder().offer(offer).messageId(messageId).build();
    }

    public static WebappProperties createWebappProperties() {
        return factory.manufacturePojo(WebappProperties.class);
    }

    public static Offer createOffer(final EnquiryType enquiryType) {
        final Offer offer = createOffer();
        offer.setEnquiryType(enquiryType);
        return offer;
    }

    public static LeadCreatedEventMessage createLeadCreatedEventMessage(
        String name,
        String companyName,
        String businessEmailAddress,
        String phoneNumber,
        String referrer,
        String message
    ) {
        return LeadCreatedEventMessage.builder()
            .name(name)
            .companyName(companyName)
            .businessEmailAddress(businessEmailAddress)
            .phoneNumber(phoneNumber)
            .referrer(referrer)
            .message(message)
            .build();
    }

    public static ReplacementOffer createReplacementOffer() {
        return factory.manufacturePojo(ReplacementOffer.class);
    }

    public static Surcharge createSurcharge() {
        return factory.manufacturePojo(Surcharge.class);
    }

    public static Item createItem() {
        return factory.manufacturePojo(Item.class);
    }

    public static Samplekit createSamplekit() {
        return factory.manufacturePojo(Samplekit.class);
    }

    public static Order createOrder() {
        final Order order = factory.manufacturePojo(Order.class);
        order.setDateCreated(OffsetDateTime.now().toString());
        order.setDateUpdated(OffsetDateTime.now().toString());
        order.setType(OrderType.LUBES);
        return order;
    }

    public static OfferMessage createOfferMessage(String enquiryType) {
        OfferMessage offerMessage = factory.manufacturePojo(OfferMessage.class);
        offerMessage.setEnquiryType(enquiryType);
        return offerMessage;
    }

    public static User createUser(UserType userType) {
        User user = factory.manufacturePojo(User.class);
        user.setUserType(userType);
        return user;
    }

    public static ReplacementOrder createReplacementOrder() {
        return factory.manufacturePojo(ReplacementOrder.class);
    }

    public static ApprovalRequestCreatedEventMessage createApprovalRequestCreatedEventMessage() {
        return ApprovalRequestCreatedEventMessage.builder()
            .approvalRequest(createApprovalRequest())
            .approvalLink(createURL().toString())
            .approverEmailAddress(createString())
            .build();
    }

    public static ApprovalRequestPendingReminderEventMessage createApprovalRequestPendingReminderEventMessage() {
        return ApprovalRequestPendingReminderEventMessage.builder()
            .approvalRequest(createApprovalRequest())
            .approvalLink(createURL().toString())
            .approverEmailAddress(createString())
            .build();
    }

    private static ApprovalRequest createApprovalRequest() {
        return factory.manufacturePojo(ApprovalRequest.class);
    }

    public static Event createEvent(final ApprovalRequestCreatedEventMessage eventMessage) {
        final Event event = createEvent();
        event.setRawMessage(GsonCoder.decode(eventMessage));
        return event;
    }

    public static Event createEvent(final PasswordResetRequestCreatedMessage eventMessage) {
        final Event event = createEvent();
        event.setRawMessage(GsonCoder.decode(eventMessage));
        return event;
    }

    public static Event createEvent(final PasswordResetRequestResetMessage eventMessage) {
        final Event event = createEvent();
        event.setRawMessage(GsonCoder.decode(eventMessage));
        return event;
    }

    public static OffsetDateTime createOffsetDateTime() {
        return OffsetDateTime.now(ZoneOffset.UTC);
    }
}
