package net.closelink.notificationservice.data;

import org.springframework.context.MessageSource;
import org.springframework.context.support.ResourceBundleMessageSource;

public class MessageSourceCreator {

    public static MessageSource createMessageSource() {
        final ResourceBundleMessageSource source = new ResourceBundleMessageSource();
        source.setBasenames("strings/messages");
        source.setUseCodeAsDefaultMessage(true);
        return source;
    }
}
