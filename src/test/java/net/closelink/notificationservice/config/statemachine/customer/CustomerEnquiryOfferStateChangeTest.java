package net.closelink.notificationservice.config.statemachine.customer;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import de.tschumacher.simplestatemachine.exception.TransitionNotAllowedException;
import net.closelink.cenqueue.types.OfferState;
import org.junit.Test;
import org.mockito.Mockito;

public class CustomerEnquiryOfferStateChangeTest extends CustomerOfferStateChangeTest {

    private static final OfferState BASE_STATE = OfferState.ENQUIRY;

    @Test
    public void testOrder() {
        Mockito.when(this.enquiryOrderHandler.handle(any(), eq(this.orderUpdatedEvent))).thenReturn(
            this.orderUpdatedEvent
        );

        testStatePermitted(BASE_STATE, OfferState.ORDER);

        Mockito.verify(this.enquiryOrderHandler).handle(any(), eq(this.orderUpdatedEvent));
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testEnquiry() {
        testStatePermitted(BASE_STATE, OfferState.ENQUIRY);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testAdjusted() {
        testStatePermitted(BASE_STATE, OfferState.ADJUSTED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testCanceled() {
        testStatePermitted(BASE_STATE, OfferState.CANCELED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testEnquiryDeclined() {
        testStatePermitted(BASE_STATE, OfferState.ENQUIRY_DECLINED);
    }

    @Test
    public void testEnquiryExpired() {
        Mockito.when(this.enquiryExpiredHandler.handle(any(), eq(this.orderUpdatedEvent))).thenReturn(
            this.orderUpdatedEvent
        );
        testStatePermitted(BASE_STATE, OfferState.ENQUIRY_EXPIRED);
        Mockito.verify(this.enquiryExpiredHandler).handle(any(), eq(this.orderUpdatedEvent));
    }

    @Test
    public void testEnquiryCanceled() {
        Mockito.when(this.enquiryCanceledHandler.handle(any(), eq(this.orderUpdatedEvent))).thenReturn(
            this.orderUpdatedEvent
        );
        testStatePermitted(BASE_STATE, OfferState.ENQUIRY_CANCELED);
        Mockito.verify(this.enquiryCanceledHandler).handle(any(), eq(this.orderUpdatedEvent));
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testConfirmed() {
        testStatePermitted(BASE_STATE, OfferState.CONFIRMED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testDelivered() {
        testStatePermitted(BASE_STATE, OfferState.DELIVERED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testDeliveredConfirmed() {
        testStatePermitted(BASE_STATE, OfferState.DELIVERY_CONFIRMED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testQuoted() {
        testStatePermitted(BASE_STATE, OfferState.QUOTED);
    }
}
