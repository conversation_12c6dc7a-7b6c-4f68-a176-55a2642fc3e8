package net.closelink.notificationservice.config.statemachine.customer;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import de.tschumacher.simplestatemachine.exception.TransitionNotAllowedException;
import net.closelink.cenqueue.types.OfferState;
import org.junit.Test;
import org.mockito.Mockito;

public class CustomerQuotedOfferStateChangeTest extends CustomerOfferStateChangeTest {

    private static final OfferState BASE_STATE = OfferState.QUOTED;

    @Test
    public void testOrder() {
        Mockito.when(this.quoteOrderHandler.handle(any(), eq(this.orderUpdatedEvent))).thenReturn(
            this.orderUpdatedEvent
        );
        testStatePermitted(BASE_STATE, OfferState.ORDER);
        Mockito.verify(this.quoteOrderHandler).handle(any(), eq(this.orderUpdatedEvent));
    }

    @Test
    public void testEnquiry() {
        Mockito.when(this.quoteEnquiryHandler.handle(any(), eq(this.orderUpdatedEvent))).thenReturn(
            this.orderUpdatedEvent
        );
        testStatePermitted(BASE_STATE, OfferState.ENQUIRY);
        Mockito.verify(this.quoteEnquiryHandler).handle(any(), eq(this.orderUpdatedEvent));
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testAdjusted() {
        testStatePermitted(BASE_STATE, OfferState.ADJUSTED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testCanceled() {
        testStatePermitted(BASE_STATE, OfferState.CANCELED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testConfirmed() {
        testStatePermitted(BASE_STATE, OfferState.CONFIRMED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testDelivered() {
        testStatePermitted(BASE_STATE, OfferState.DELIVERED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testDeliveredConfirmed() {
        testStatePermitted(BASE_STATE, OfferState.DELIVERY_CONFIRMED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testQuoted() {
        testStatePermitted(BASE_STATE, OfferState.QUOTED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testEnquiryDeclined() {
        testStatePermitted(BASE_STATE, OfferState.ENQUIRY_DECLINED);
    }

    @Test
    public void testEnquiryOfferDeclined() {
        Mockito.when(this.quoteDeclinedHandler.handle(any(), eq(this.orderUpdatedEvent))).thenReturn(
            this.orderUpdatedEvent
        );
        testStatePermitted(BASE_STATE, OfferState.QUOTE_DECLINED);
        Mockito.verify(this.quoteDeclinedHandler).handle(any(), eq(this.orderUpdatedEvent));
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testEnquiryCanceled() {
        testStatePermitted(BASE_STATE, OfferState.ENQUIRY_CANCELED);
    }
}
