package net.closelink.notificationservice.config.statemachine.supplier;

import static org.junit.Assert.assertEquals;

import de.tschumacher.simplestatemachine.configuration.SimpleStateMachineConfig;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.config.statemachine.SupplierOfferStateMachineConfig;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.state.common.delivered.SupplierDeliveredHandler;
import net.closelink.notificationservice.handler.offer.updated.state.common.invoiced.GeneralOrderInvoicedHandler;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.all.ConfirmedOrderChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.all.SupplierCanceledHandler;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.delivered.SupplierDeliveredConfirmedHandler;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.enquiry.EnquiryQuoteHandler;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.enquiry.SupplierEnquiryCanceledHandler;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.order.OrderConfirmedHandler;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.order.SupplierAcknowledgedHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.After;
import org.junit.Before;
import org.mockito.Mockito;

public class SupplierOfferStateChangeTest {

    protected SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> service;

    protected EnquiryQuoteHandler enquiryQuoteHandler;
    protected SupplierEnquiryCanceledHandler enquiryCanceledHandler;
    protected OrderConfirmedHandler orderConfirmedHandler;
    protected SupplierCanceledHandler supplierCanceledHandler;
    protected SupplierDeliveredHandler supplierDeliveredHandler;
    protected SupplierDeliveredConfirmedHandler deliveredConfirmHandler;
    protected SupplierAcknowledgedHandler supplierAcknowledgedHandler;
    protected GeneralOrderInvoicedHandler generalOrderInvoicedHandler;
    protected ConfirmedOrderChangedHandler confirmedOrderChangedHandler;
    protected OfferUpdatedEvent orderUpdatedEvent;

    @Before
    public void setUp() {
        this.enquiryQuoteHandler = Mockito.mock(EnquiryQuoteHandler.class);
        this.enquiryCanceledHandler = Mockito.mock(SupplierEnquiryCanceledHandler.class);
        this.orderConfirmedHandler = Mockito.mock(OrderConfirmedHandler.class);
        this.supplierCanceledHandler = Mockito.mock(SupplierCanceledHandler.class);
        this.supplierDeliveredHandler = Mockito.mock(SupplierDeliveredHandler.class);
        this.deliveredConfirmHandler = Mockito.mock(SupplierDeliveredConfirmedHandler.class);
        this.supplierAcknowledgedHandler = Mockito.mock(SupplierAcknowledgedHandler.class);
        this.generalOrderInvoicedHandler = Mockito.mock(GeneralOrderInvoicedHandler.class);
        this.confirmedOrderChangedHandler = Mockito.mock(ConfirmedOrderChangedHandler.class);

        final SupplierOfferStateMachineConfig stateMachineConfig = new SupplierOfferStateMachineConfig();
        this.service = stateMachineConfig.supplierSimpleStateMachineConfig(
            this.enquiryQuoteHandler,
            this.enquiryCanceledHandler,
            this.orderConfirmedHandler,
            this.supplierCanceledHandler,
            this.supplierDeliveredHandler,
            this.deliveredConfirmHandler,
            this.supplierAcknowledgedHandler,
            this.generalOrderInvoicedHandler,
            this.confirmedOrderChangedHandler
        );

        this.orderUpdatedEvent = DataCreator.createOrderUpdatedEvent();
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.enquiryQuoteHandler);
        Mockito.verifyNoMoreInteractions(this.enquiryCanceledHandler);
        Mockito.verifyNoMoreInteractions(this.orderConfirmedHandler);
        Mockito.verifyNoMoreInteractions(this.supplierCanceledHandler);
        Mockito.verifyNoMoreInteractions(this.supplierDeliveredHandler);
        Mockito.verifyNoMoreInteractions(this.supplierAcknowledgedHandler);
        Mockito.verifyNoMoreInteractions(this.generalOrderInvoicedHandler);
        Mockito.verifyNoMoreInteractions(this.confirmedOrderChangedHandler);
    }

    protected void testStatePermitted(final OfferState oldState, final OfferState newState) {
        final OfferUpdatedEvent orderUpdatedEvent =
            this.service.createMachine(oldState).change(newState, this.orderUpdatedEvent);

        assertEquals(orderUpdatedEvent, this.orderUpdatedEvent);
    }
}
