package net.closelink.notificationservice.config.statemachine.customer;

import static org.junit.Assert.assertEquals;

import de.tschumacher.simplestatemachine.configuration.SimpleStateMachineConfig;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.config.statemachine.CustomerOfferStateMachineConfig;
import net.closelink.notificationservice.data.DataCreator;
import net.closelink.notificationservice.handler.offer.updated.state.common.delivered.GeneralDeliveredHandler;
import net.closelink.notificationservice.handler.offer.updated.state.common.invoiced.GeneralOrderInvoicedHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.acknowledge.AcknowledgedOrderHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.all.CustomerCanceledHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.confirm.ConfirmedOrderHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.delivered.DeliveredOrderHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry.EnquiryCanceledHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry.EnquiryExpiredHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry.EnquiryOrderHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.enquirydeclined.EnquiryDeclinedEnquiryHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.quote.QuoteDeclinedHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.quote.QuoteEnquiryHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.quote.QuoteOrderHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.junit.After;
import org.junit.Before;
import org.mockito.Mockito;

public class CustomerOfferStateChangeTest {

    protected SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> service;
    protected CustomerCanceledHandler customerCanceledHandler;
    protected ConfirmedOrderHandler confirmedOrderHandler;
    protected QuoteEnquiryHandler quoteEnquiryHandler;
    protected QuoteOrderHandler quoteOrderHandler;
    protected EnquiryOrderHandler enquiryOrderHandler;
    protected DeliveredOrderHandler deliveredOrderHandler;
    protected EnquiryCanceledHandler enquiryCanceledHandler;
    protected EnquiryExpiredHandler enquiryExpiredHandler;
    protected QuoteDeclinedHandler quoteDeclinedHandler;
    protected EnquiryDeclinedEnquiryHandler enquiryDeclinedEnquiryHandler;
    protected AcknowledgedOrderHandler acknowledgedOrderHandler;
    protected GeneralDeliveredHandler generalDeliveredHandler;
    protected OfferUpdatedEvent orderUpdatedEvent;
    protected GeneralOrderInvoicedHandler generalOrderInvoicedHandler;

    @Before
    public void setUp() {
        this.customerCanceledHandler = Mockito.mock(CustomerCanceledHandler.class);
        this.confirmedOrderHandler = Mockito.mock(ConfirmedOrderHandler.class);
        this.quoteEnquiryHandler = Mockito.mock(QuoteEnquiryHandler.class);
        this.quoteOrderHandler = Mockito.mock(QuoteOrderHandler.class);
        this.enquiryOrderHandler = Mockito.mock(EnquiryOrderHandler.class);
        this.deliveredOrderHandler = Mockito.mock(DeliveredOrderHandler.class);
        this.enquiryCanceledHandler = Mockito.mock(EnquiryCanceledHandler.class);
        this.enquiryExpiredHandler = Mockito.mock(EnquiryExpiredHandler.class);
        this.quoteDeclinedHandler = Mockito.mock(QuoteDeclinedHandler.class);
        this.enquiryDeclinedEnquiryHandler = Mockito.mock(EnquiryDeclinedEnquiryHandler.class);
        this.acknowledgedOrderHandler = Mockito.mock(AcknowledgedOrderHandler.class);
        this.generalOrderInvoicedHandler = Mockito.mock(GeneralOrderInvoicedHandler.class);
        this.generalDeliveredHandler = Mockito.mock(GeneralDeliveredHandler.class);

        final CustomerOfferStateMachineConfig stateMachineConfig = new CustomerOfferStateMachineConfig();
        this.service = stateMachineConfig.customerSimpleStateMachineConfig(
            this.customerCanceledHandler,
            this.confirmedOrderHandler,
            this.quoteEnquiryHandler,
            this.quoteOrderHandler,
            this.enquiryOrderHandler,
            this.deliveredOrderHandler,
            this.enquiryCanceledHandler,
            this.enquiryExpiredHandler,
            this.quoteDeclinedHandler,
            this.enquiryDeclinedEnquiryHandler,
            this.acknowledgedOrderHandler,
            this.generalDeliveredHandler,
            this.generalOrderInvoicedHandler
        );

        this.orderUpdatedEvent = DataCreator.createOrderUpdatedEvent();
    }

    @After
    public void afterTest() {
        Mockito.verifyNoMoreInteractions(this.customerCanceledHandler);
        Mockito.verifyNoMoreInteractions(this.confirmedOrderHandler);
        Mockito.verifyNoMoreInteractions(this.quoteEnquiryHandler);
        Mockito.verifyNoMoreInteractions(this.quoteOrderHandler);
        Mockito.verifyNoMoreInteractions(this.enquiryOrderHandler);
        Mockito.verifyNoMoreInteractions(this.enquiryCanceledHandler);
        Mockito.verifyNoMoreInteractions(this.enquiryExpiredHandler);
        Mockito.verifyNoMoreInteractions(this.quoteDeclinedHandler);
        Mockito.verifyNoMoreInteractions(this.enquiryDeclinedEnquiryHandler);
        Mockito.verifyNoMoreInteractions(this.acknowledgedOrderHandler);
        Mockito.verifyNoMoreInteractions(this.generalDeliveredHandler);
        Mockito.verifyNoMoreInteractions(this.generalOrderInvoicedHandler);
    }

    protected void testStatePermitted(final OfferState oldState, final OfferState newState) {
        final OfferUpdatedEvent orderUpdatedEvent =
            this.service.createMachine(oldState).change(newState, this.orderUpdatedEvent);

        assertEquals(orderUpdatedEvent, this.orderUpdatedEvent);
    }
}
