package net.closelink.notificationservice.config.statemachine.supplier;

import static org.junit.jupiter.api.Assertions.assertThrows;

import de.tschumacher.simplestatemachine.exception.TransitionNotAllowedException;
import java.util.Arrays;
import java.util.Set;
import net.closelink.cenqueue.types.OfferState;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;

@RunWith(Parameterized.class)
public class SupplierSupplierAdjustedOfferStateChangeTest extends SupplierOfferStateChangeTest {

    private static final OfferState BASE_STATE = OfferState.SUPPLIER_ADJUSTED;
    private static final Set<OfferState> allowedStates = Set.of(OfferState.CONFIRMED, OfferState.DELIVERED);
    private final OfferState state;

    @Parameterized.Parameters
    public static Object[] dataProvider() {
        return Arrays.stream(OfferState.values()).filter(state -> !allowedStates.contains(state)).toArray();
    }

    public SupplierSupplierAdjustedOfferStateChangeTest(OfferState state) {
        this.state = state;
    }

    @Test
    public void testAllowedStates() {
        assertThrows(TransitionNotAllowedException.class, () -> testStatePermitted(BASE_STATE, state));
    }
}
