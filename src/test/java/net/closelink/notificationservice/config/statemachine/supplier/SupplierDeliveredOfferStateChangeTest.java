package net.closelink.notificationservice.config.statemachine.supplier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import de.tschumacher.simplestatemachine.exception.TransitionNotAllowedException;
import net.closelink.cenqueue.types.OfferState;
import org.junit.Test;
import org.mockito.Mockito;

public class SupplierDeliveredOfferStateChangeTest extends SupplierOfferStateChangeTest {

    private static final OfferState BASE_STATE = OfferState.DELIVERED;

    @Test(expected = TransitionNotAllowedException.class)
    public void testOrder() {
        testStatePermitted(BASE_STATE, OfferState.ORDER);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testEnquiry() {
        testStatePermitted(BASE_STATE, OfferState.ENQUIRY);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testAdjusted() {
        testStatePermitted(BASE_STATE, OfferState.ADJUSTED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testCanceled() {
        testStatePermitted(BASE_STATE, OfferState.CANCELED);
    }

    @Test
    public void testConfirmed() {
        Mockito.when(this.deliveredConfirmHandler.handle(any(), eq(this.orderUpdatedEvent))).thenReturn(
            this.orderUpdatedEvent
        );
        testStatePermitted(BASE_STATE, OfferState.CONFIRMED);

        Mockito.verify(this.deliveredConfirmHandler).handle(any(), eq(this.orderUpdatedEvent));
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testDelivered() {
        testStatePermitted(BASE_STATE, OfferState.DELIVERED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testDeliveredConfirmed() {
        testStatePermitted(BASE_STATE, OfferState.DELIVERY_CONFIRMED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testQuoted() {
        testStatePermitted(BASE_STATE, OfferState.QUOTED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testEnquiryDeclined() {
        testStatePermitted(BASE_STATE, OfferState.ENQUIRY_DECLINED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testEnquiryQuoteDeclined() {
        testStatePermitted(BASE_STATE, OfferState.QUOTE_DECLINED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testEnquiryQuoteCanceled() {
        testStatePermitted(BASE_STATE, OfferState.QUOTE_CANCELED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testEnquiryEnquiryExpired() {
        testStatePermitted(BASE_STATE, OfferState.ENQUIRY_EXPIRED);
    }

    @Test(expected = TransitionNotAllowedException.class)
    public void testEnquiryCanceled() {
        testStatePermitted(BASE_STATE, OfferState.ENQUIRY_CANCELED);
    }
}
