<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<springProperty scope="context" name="ACTIVE_PROFILE" source="spring.profiles.active"/>
	<springProperty scope="context" name="APPLICATION_NAME" source="spring.application.name"/>

	<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<charset>UTF-8</charset>
			<pattern><![CDATA[%date{ISO8601} | %-20thread | %-5level{5} | %-170message | %-35(%logger{0}:%-5L) | %mdc%n%exception{full}]]></pattern>
		</encoder>
	</appender>

	<appender name="SENTRY" class="io.sentry.logback.SentryAppender">
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>
		<!-- Optionally add an encoder -->
		<encoder>
			<pattern>%msg - %logger%n</pattern>
		</encoder>
	</appender>

	<appender name="ELASTIC" class="com.agido.logback.elasticsearch.ElasticsearchAppender">
		<operation>index</operation>
		<url>https://vpc-es-closelink-logs-ieziw6d36bxeyvrdgezcchssdi.eu-central-1.es.amazonaws.com/_bulk</url>
		<index>logs-%date{yyyy-MM-dd}</index>
		<includeMdc>true</includeMdc>
		<properties>
			<esProperty>
				<name>host</name>
				<value>${HOSTNAME}</value>
				<allowEmpty>false</allowEmpty>
			</esProperty>
			<esProperty>
				<name>severity</name>
				<value>%level</value>
			</esProperty>
			<esProperty>
				<name>thread</name>
				<value>%thread</value>
			</esProperty>
			<esProperty>
				<name>stacktrace</name>
				<value>%ex</value>
			</esProperty>
			<esProperty>
				<name>logger</name>
				<value>%logger</value>
			</esProperty>
			<esProperty>
				<name>method</name>
				<value>%method:%line</value>
			</esProperty>
			<esProperty>
				<name>profiles</name>
				<value>${ACTIVE_PROFILE}</value>
			</esProperty>
			<esProperty>
				<name>application</name>
				<value>${APPLICATION_NAME}</value>
			</esProperty>
		</properties>
		<headers>
			<header>
				<name>Content-Type</name>
				<value>application/json</value>
			</header>
		</headers>
	</appender>

	<root level="INFO" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ELASTIC" />
		<appender-ref ref="SENTRY" />
	</root>

</configuration>
