system.assignment.subject=New assignment request
system.assignment.message=There is a new assignment waiting

system.upload.subject=New price list upload
system.upload.message=There is a new price list upload waiting

user.onboarding.subject=You received a Closelink invitation
user.onboarding.message=You received a Closelink invitation

#FROM CUSTOMER
enquiryOrder.subject={0} - {1} - New order from {2}
enquiryOrderBuyerRef.subject={0} - {3} - {1} - New order from {2}
enquiryOrder.message=Enquiry is ordered.

enquiryCustomerCancel.subject={0} - {1} - Enquiry cancelled from {2}
enquiryCustomerCancelBuyerRef.subject={0} - {3} - {1} - Enquiry cancelled from {2}
enquiryCustomerCancel.message=Enquiry cancelled: {0}

quoteQuoteCancelled.subject={0} - {1} - Spot offer declined from {2}
quoteQuoteCancelledBuyerRef.subject={0} - {3} - {1} - Spot offer declined from {2}
quoteQuoteCancelled.message=Spot offer has been declined.

enquiryEnquiryCancelled.subject={0} - {1} - Spot enquiry expired from {2}
enquiryEnquiryCancelledBuyerRef.subject={0} - {3} - {1} - Spot enquiry expired from {2}
enquiryEnquiryCancelled.message=Spot enquiry has been expired.

enquiryEnquiryExpired.subject={0} - {1} - Spot enquiry expired from {2}
enquiryEnquiryExpiredBuyerRef.subject={0} - {3} - {1} - Spot enquiry expired from {2}
enquiryEnquiryExpired.message=Competitor offer has been chosen.

quoteQuoteDeclined.subject={0} - {1} - Spot offer declined from {2}
quoteQuoteDeclinedBuyerRef.subject={0} - {3} - {1} - Spot offer declined from {2}
quoteQuoteDeclined.message=Competitor offer has been chosen.

quoteOrder.subject={0} - {1} - New order from {2}
quoteOrderBuyerRef.subject={0} - {3} - {1} - New order from {2}
quoteOrder.message=Quoted enquiry has been ordered

quoteOrderTrade.subject={0} - {1} - New spot order from {2}
quoteOrderTradeBuyerRef.subject={0} - {3} - {1} - New spot order from {2}
quoteOrderTrade.message=Quoted spot enquiry has been ordered

confirmedOrder.subject={0} - {1} - Changes to order from {2}
confirmedOrderBuyerRef.subject={0} - {3} - {1} - Changes to order from {2}
confirmedOrder.message=Confirmed order was changed.

acknowledgedOrder.subject={0} - {1} - Changes to order from {2}
acknowledgedOrderBuyerRef.subject={0} - {3} - {1} - Changes to order from {2}
acknowledgedOrder.message=Acknowledged order was changed.

draftEnquiry.subject={0} - {1} - New enquiry from {2}
draftEnquiryBuyerRef.subject={0} - {3} - {1} - New enquiry from {2}
draftEnquiry.message=Enquiry filed.

draftTradeEnquiry.subject={0} - {1} - New spot enquiry from {2}
draftTradeEnquiryBuyerRef.subject={0} - {3} - {1} - New spot enquiry from {2}
draftTradeEnquiry.message=Spot enquiry filed.

quoteEnquiry.subject={0} - {1} - Quoted enquiry changed from {2}
quoteEnquiryBuyerRef.subject={0} - {3} - {1} - Quoted enquiry changed from {2}
quoteEnquiry.message=Enquiry has been changed.

enquiryDeclinedEnquiry.subject={0} - {1} - Changes to enquiry from {2}
enquiryDeclinedEnquiryBuyerRef.subject={0} - {3} - {1} - Changes to enquiry from {2}
enquiryDeclinedEnquiry.message=Enquiry restored.

enquiryDeclinedEnquiryTrade.subject={0} - {1} - Changes to spot enquiry from {2}
enquiryDeclinedEnquiryTradeBuyerRef.subject={0} - {3} - {1} - Changes to spot enquiry from {2}
enquiryDeclinedEnquiryTrade.message=Spot enquiry restored.

customerCancel.subject={0} - {1} - Order cancelled from {2}
customerCancelBuyerRef.subject={0} - {3} - {1} - Order cancelled from {2}
customerCancel.message=Order cancelled: {0}

deliveredOrder.subject={0} - {1} - Changes to order from {2}
deliveredOrderBuyerRef.subject={0} - {3} - {1} - Changes to order from {2}
deliveredOrder.message=Delivered order was changed.

deliveryReminder.subject={0} - {1} - Supply reminder
deliveryReminderBuyerRef.subject={0} - {2} - {1} - Supply reminder
deliveryReminder.message=Supply reminder.

openTradeReminder.subject={0} - {1} - Open enquiry reminder
openTradeReminderBuyerRef.subject={0} - {2} - {1} - Open enquiry reminder
openTradeReminder.message=Open enquiry reminder.

delivered.customer.subject={0} - {1} - Order delivered to {2}
deliveredBuyerRef.customer.subject={0} - {3} - {1} - Order delivered to {2}
delivered.customer.message=Order delivered.

customerInvoiced.subject={0} - {1} - Order of {2} invoiced
customerInvoicedBuyerRef.subject={0} - {3} - {1} - Order of {2} invoiced
customerInvoiced.message=Order was invoiced

#FROM SUPPLIER
delivered.supplier.subject={0} - {1} - Order delivered from {2}
deliveredBuyerRef.supplier.subject={0} - {3} - {1} - Order delivered from {2}
delivered.supplier.message=Order delivered.

enquiryQuote.subject={0} - {1} - New quote from {2}
enquiryQuoteBuyerRef.subject={0} - {3} - {1} - New quote from {2}
enquiryQuote.message=Enquiry was quoted.

enquiryQuoteTrade.subject={0} - {1} - New spot offer from {2}
enquiryQuoteTradeBuyerRef.subject={0} - {3} - {1} - New spot offer from {2}
enquiryQuoteTrade.message=Spot offer was created.

orderConfirmed.subject={0} - {1} - Order confirmed from {2}
orderConfirmedBuyerRef.subject={0} - {3} - {1} - Order confirmed from {2}
orderConfirmed.message=Order was confirmed

acknowledgedOffer.subject={0} - {1} - Order acknowledged from {2}
acknowledgedOfferBuyerRef.subject={0} - {3} - {1} - Order acknowledged from {2}
acknowledgedOffer.message=Order was acknowledged

supplierEnquiryCancelled.subject={0} - {1} - Enquiry rejected from {2}
supplierEnquiryCancelledBuyerRef.subject={0} - {3} - {1} - Enquiry rejected from {2}
supplierEnquiryCancelled.message=Enquiry rejected: {0}

supplierEnquiryCancelledTrade.subject={0} - {1} - Spot enquiry rejected from {2}
supplierEnquiryCancelledTradeBuyerRef.subject={0} - {3} - {1} - Spot enquiry rejected from {2}
supplierEnquiryCancelledTrade.message=Spot enquiry rejected: {0}

supplierCancelled.subject={0} - {1} - Order rejected from {2}
supplierCancelledBuyerRef.subject={0} - {3} - {1} - Order rejected from {2}
supplierCancelled.message=Order cancelled: {0}

deliveredConfirm.subject={0} - {1} - Changes to order from {2}
deliveredConfirmBuyerRef.subject={0} - {3} - {1} - Changes to order from {2}
deliveredConfirm.message=Delivered order was changed.

supplierConfirmedOrder.subject = {0} - {1} - Changes to order from {2}
supplierConfirmedOrderBuyerRef.subject = {0} - {3} - {1} - Changes to order from {2}
supplierConfirmedOrder.message = Confirmed order was changed.

#Approval Request
approval.created.subject={0} - {1} - Request for Approval
approval.created.buyerRef.subject={0} - {2} - {1} - Request for Approval
approval.created.message=Approval Request was created.

#Approval Request
approval.reminder.subject={0} - {1} - Waiting for Approval
approval.reminder.buyerRef.subject={0} - {2} - {1} - Waiting for Approval
approval.reminder.message=Approval Request is waiting for approval.

# Counter Offer
counteroffer.created.message=Counter Offer sent.
counteroffer.canceled.message=Counter Offer cancelled.

#activity log
activitylog.paymenttermreferencedays=The payment terms changed from {1} to {2}.
activitylog.eta=The ETA changed from {1} to {2}.
activitylog.etd=The ETD changed from {1} to {2}.
activitylog.validityTime=The validity time changed from {1} to {2}.
activitylog.noticedays=The notice days changed from {1} to {2}.
activitylog.supplymode=The supply mode changed from {1} to {2}.
activitylog.agent=The agent was changed from {1} to {2}.
activitylog.quotevaliditytime=The offer validity time was reset to {2} minutes.
activitylog.deliverydate=The delivery date was changed from {1} to {2}.
activitylog.vessel=The vessel was changed from {1} to {2}. 
activitylog.port=The port was changed from {1} to {2}. 
activitylog.buyerref=The buyer reference was changed from {1} to {2}.
activitylog.itemunits=The unit amount of {1} was changed from {2} to {3}.
activitylog.itemunitsize=The packsize of {1} was changed from {2} to {3}.
activitylog.itemphysicalsupplier=The physical supplier of {1} was changed from {2} to {3}.
activitylog.itempacktype=The packtype of {1} was changed from {2} to {3}.
activitylog.itemprice=The price of {1} was changed from {2} to {3}.
activitylog.itemproduct=The product {1} was changed to {2}.
activitylog.itemadded=A new product was added: {1}.
activitylog.itemdeleted=A product was deleted: {1}.
activitylog.itemundeleted=A product was restored: {1}.
activitylog.surchagename=A surcharge was renamed from {1} to {2}.
activitylog.surchargevalue=The surcharge value of {1} was changed from {2} to {3}.
activitylog.surchargeadded=A new surcharge was added: {1}.
activitylog.surchargedelete=A surcharge was deleted: {1}.
activitylog.surchargeundelete=A surcharge was restored: {1}.
activitylog.waivername=A waiver was renamed from {1} to {2}.
activitylog.waivervalue=The waiver value of {1} was changed from {2} to {3}.
activitylog.waiveradded=A new waiver was added: {1}.
activitylog.waiverdelete=A waiver was deleted: {1}.
activitylog.waiverundelete=A waiver was restored: {1}.
activitylog.samplekitname=A sample kit was renamed from {1} to {2}.
activitylog.samplekitquantity=The unit amount of {1} was changed from {2} to {3}.
activitylog.samplekitvalue=The sample kit value of {1} was changed from {2} to {3}.
activitylog.samplekitadded=A new sample kit was added: {1}.
activitylog.samplekitdelete=A sample kit was deleted: {1}.
activitylog.samplekitundelete=A sample kit was restored: {1}.
activitylog.prepayment=Prepayment
activitylog.na=N/A
activitylog.agentId.added=Agent added
activitylog.agentId.updated=Agent changed
activitylog.agentId.removed=Agent removed
activitylog.file-added=File added
activitylog.file-removed=File removed

activitylog.subject.supplier.enquiry={0} - {1} - Changes to enquiry from {2}
activitylog.subject.supplier.enquiryBuyerRef={0} - {3} - {1} - Changes to enquiry from {2}
activitylog.subject.trade.supplier.enquiry={0} - {1} - Changes to spot enquiry from {2}
activitylog.subject.trade.supplier.enquiryBuyerRef={0} - {3} - {1} - Changes to spot enquiry from {2}
activitylog.subject.supplier.order={0} - {1} - Changes to order from {2}
activitylog.subject.supplier.orderBuyerRef={0} - {3} - {1} - Changes to order from {2}
activitylog.subject.customer.quoted={0} - {1} - New quote from {2}
activitylog.subject.customer.quotedBuyerRef={0} - {3} - {1} - New quote from {2}
activitylog.subject.trade.customer.quoted={0} - {1} - New spot offer from {2}
activitylog.subject.trade.customer.quotedBuyerRef={0} - {3} - {1} - New spot offer from {2}
activitylog.subject.customer.order={0} - {1} - Changes to order from {2}
activitylog.subject.customer.orderBuyerRef={0} - {3} - {1} - Changes to order from {2}

message.subject.customer={0} - {1} - New message from {2}
message.subjectBuyerRef.customer={0} - {3} - {1} - New message from {2}
message.subject.trade.customer={0} - {1} - New message from {2}
message.subjectBuyerRef.trade.customer={0} - {3} - {1} - New message from {2}
message.subject.supplier={0} - {1} - New message from {2}
message.subjectBuyerRef.supplier={0} - {3} - {1} - New message from {2}

BULK=Bulk
BULK_EX_IBC=Bulk Ex IBC
BULK_EX_DRUMS=Bulk Ex Drums
DRUMS=Drums
IBCS=IBCS
PAIL=Pail
BOTTLE=Bottle
PACK=Pack

BARGE=Barge
BARGEANDTRUCK=Barge & Truck
TRUCK=Truck
EX_WHARF=Ex-Wharf
EX_PIPE=Ex-Pipe

CYLINDER_OIL=Cylinder Oil
SYSMTEM_OIL_2_STROKE=System Oil 2-Stroke
ENGINE_OIL_4_STROKE=Engine Oil 4-Stroke
ANCILLARY_OIL=Ancillary Oil
GREASE=Grease
BLEND_ON_BOARD_ADDITIVES=Blend On Board Additives

HS=max. 3.5 % S
VLS=max. 0.5 % S
ULS=max. 0.1 % S

export.vessel.subject=New Order

state.DRAFT.title=Draft - Not yet sent to supplier
state.ENQUIRY.title=Enquiry - Waiting for quote
state.QUOTED.title=Enquiry - Quoted
state.ORDER.title=Order - Waiting for confirmation
state.ADJUSTED.title=Order - Changed by supplier
state.CONFIRMED.title=Order - Confirmed
state.DELIVERED.title=Delivered - Confirmed by supplier
state.DELIVERY_CONFIRMED.title=Completed
state.CANCELED.title=Cancelled
state.ENQUIRY_CANCELED.title=Enquiry cancelled by customer
state.ENQUIRY_DECLINED.title=Enquiry declined by supplier
state.QUOTE_DECLINED.title=Declined - Competitor offer chosen
state.QUOTE_CANCELED.title=Enquiry cancelled
state.ENQUIRY_EXPIRED.title=Expired - Competitor offer chosen

cancel_reason_not_available=n/a

report.prepaymentdays=days
report.operator.positive=higher than
report.operator.negative=lower than

stockdemand.subject={0} - Lubricants below warning limit
stockDemand.safetyReserve.subject={0} - Lubricants below safety reserve limit

leadCreated.subject=New lead!

forwarded.newEnquiry.subject={1} - New enquiry for {0}
forwarded.newEnquiry.subjectBuyerRef={3} - {1} - New enquiry for {0}
activitylog.subject.forwarded.supplier.enquiry={1} - Updates to enquiry for {0}
activitylog.subjectBuyerRef.forwarded.supplier.enquiry={3} - {1} - Updates to enquiry for {0}
forwarded.newOrder.subject={1} - New order for {0}
forwarded.newOrder.subjectBuyerRef={3} - {1} - New order for {0}
activitylog.subject.forwarded.supplier.order={1} - Updates to order for {0}
activitylog.subjectBuyerRef.forwarded.supplier.order={3} - {1} - Updates to order for {0}
forwarded.enquiryCustomerCancel.subject={1} - Cancelled enquiry for {0}
forwarded.enquiryCustomerCancel.subjectBuyerRef={3} - {1} - Cancelled enquiry for {0}
forwarded.orderCustomerCancel.subject= {1} - Cancelled order for {0}
forwarded.orderCustomerCancel.subjectBuyerRef={3} - {1} - Cancelled order for {0}

offerConfirmReminder.subject={0} - {1} - Customer waiting for response
offerConfirmReminder.subjectBuyerRef={0} - {2} - {1} - Customer waiting for response

approvalRequest.approved.subject={0} - {1} - Request approved
approvalRequest.approved.subjectBuyerRef={0} - {2} - {1} - Request approved
approvalRequest.regain.approved.subject={0} - {1} - Request re-approved
approvalRequest.regain.approved.subjectBuyerRef={0} - {2} - {1} - Request re-approved
approvalRequest.declined.subject={0} - {1} - Request rejected
approvalRequest.declined.subjectBuyerRef={0} - {2} - {1} - Request rejected
approvalRequest.regain.declined.subject={0} - {1} - Re-approval request rejected
approvalRequest.regain.declined.subjectBuyerRef={0} - {2} - {1} - Re-approval request rejected
approvalRequest.system=System

order.draftcreated.subject=New enquiry draft ({0}): {1}

vesselRequisition.created.subject=New Lubricant Requisition Form from {0}
vesselRequisition.imported.subject=Requisition data successfully imported

passwordResetRequest.created.subject=Complete your password reset request
passwordResetRequest.reset.subject=Closelink password changed

keyPortCallNotification.created.subject={0} - New Key Port opportunities
