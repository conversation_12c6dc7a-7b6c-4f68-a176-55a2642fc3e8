package net.closelink.notificationservice.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties("closelink.rest.internal")
@Data
public class InternalApiProperties {

    private String authorization;
    private Service coredataService;
    private Service messageService;
    private Service companyService;
    private Service productPriceService;
    private Service vesselService;
    private Service vesselTankService;
    private Service orderService;
    private Service userService;
    private Service exportService;
    private Service offerApprovalRequestService;

    @Data
    public static class Service {

        private String baseUrl;
    }
}
