package net.closelink.notificationservice.exception;

public class NoHandlerFoundException extends RuntimeException {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    public NoHandlerFoundException() {
        super();
    }

    public NoHandlerFoundException(
        final String message,
        final Throwable cause,
        final boolean enableSuppression,
        final boolean writableStackTrace
    ) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public NoHandlerFoundException(final String message, final Throwable cause) {
        super(message, cause);
    }

    public NoHandlerFoundException(final String message) {
        super(message);
    }

    public NoHandlerFoundException(final Throwable cause) {
        super(cause);
    }
}
