package net.closelink.notificationservice.util

import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

/** Converts epoch milliseconds to OffsetDateTime in UTC, handling null values. */
fun fromEpochMillisOrNull(epochMillis: Long?): OffsetDateTime? =
    epochMillis?.let { Instant.ofEpochMilli(it).atOffset(ZoneOffset.UTC) }

/**
 * Formats an OffsetDateTime to display format: "MMM d, yyyy" (e.g., "Jul 27, 2021")
 *
 * @param dateTime the OffsetDateTime to format
 * @return formatted string or empty string if dateTime is null
 */
fun formatDisplayDate(dateTime: OffsetDateTime?): String {
    val formatter = DateTimeFormatter.ofPattern("MMM d, yyyy")
    return dateTime?.format(formatter) ?: ""
}
