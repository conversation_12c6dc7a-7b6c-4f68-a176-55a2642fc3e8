package net.closelink.notificationservice.domainobject;

import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class Notification {

    private final String subject;
    private final String message;
    private final String template;
    private final String orderId;
    private final String offerId;
    private final boolean sendMail;
    private final boolean groupMail;
    private final boolean hidden;
    private final Object[] subjectReplacements;
    private final Object[] messageReplacements;
    private final String senderId;
    private final SenderType senderType;
    private final String receiverId;
    private final ReceiverType receiverType;
    private final Map<String, String> mailReplacements;
    private final List<Attachment> attachments;
    private final List<Recipient> recipients;
    private final NotificationCategory category;
    private final String customSenderEmailAddress;
    private final String customSenderName;

    Notification(
        String subject,
        String message,
        String template,
        String orderId,
        String offerId,
        boolean sendMail,
        boolean groupMail,
        boolean hidden,
        Object[] subjectReplacements,
        Object[] messageReplacements,
        String senderId,
        SenderType senderType,
        String receiverId,
        ReceiverType receiverType,
        Map<String, String> mailReplacements,
        List<Attachment> attachments,
        List<Recipient> recipients,
        NotificationCategory category,
        String customSenderEmailAddress,
        String customSenderName
    ) {
        this.subject = subject;
        this.message = message;
        this.template = template;
        this.orderId = orderId;
        this.offerId = offerId;
        this.sendMail = sendMail;
        this.groupMail = groupMail;
        this.hidden = hidden;
        this.subjectReplacements = subjectReplacements;
        this.messageReplacements = messageReplacements;
        this.senderId = senderId;
        this.senderType = senderType;
        this.receiverId = receiverId;
        this.receiverType = receiverType;
        this.mailReplacements = mailReplacements;
        this.attachments = attachments;
        this.recipients = recipients;
        this.category = category;
        this.customSenderEmailAddress = customSenderEmailAddress;
        this.customSenderName = customSenderName;
    }

    public static NotificationBuilder builder() {
        return new NotificationBuilder();
    }

    public NotificationBuilder toBuilder() {
        return new NotificationBuilder()
            .subject(this.subject)
            .message(this.message)
            .template(this.template)
            .orderId(this.orderId)
            .offerId(this.offerId)
            .sendMail(this.sendMail)
            .groupMail(this.groupMail)
            .hidden(this.hidden)
            .subjectReplacements(this.subjectReplacements)
            .messageReplacements(this.messageReplacements)
            .senderId(this.senderId)
            .senderType(this.senderType)
            .receiverId(this.receiverId)
            .receiverType(this.receiverType)
            .mailReplacements(this.mailReplacements)
            .attachments(this.attachments)
            .recipients(this.recipients)
            .category(this.category)
            .customSenderEmailAddress(this.customSenderEmailAddress)
            .customSenderName(this.customSenderName);
    }

    public static class NotificationBuilder {

        private String subject;
        private String message;
        private String template;
        private String orderId;
        private String offerId;
        private boolean sendMail;
        private boolean groupMail;
        private boolean hidden;
        private Object[] subjectReplacements;
        private Object[] messageReplacements;
        private String senderId;
        private SenderType senderType;
        private String receiverId;
        private ReceiverType receiverType;
        private Map<String, String> mailReplacements;
        private List<Attachment> attachments;
        private List<Recipient> recipients;
        private NotificationCategory category;
        private String customSenderEmailAddress;
        private String customSenderName;

        NotificationBuilder() {}

        public NotificationBuilder subject(String subject) {
            this.subject = subject;
            return this;
        }

        public NotificationBuilder message(String message) {
            this.message = message;
            return this;
        }

        public NotificationBuilder template(String template) {
            this.template = template;
            return this;
        }

        public NotificationBuilder orderId(String orderId) {
            this.orderId = orderId;
            return this;
        }

        public NotificationBuilder offerId(String offerId) {
            this.offerId = offerId;
            return this;
        }

        public NotificationBuilder sendMail(boolean sendMail) {
            this.sendMail = sendMail;
            return this;
        }

        public NotificationBuilder groupMail(boolean groupMail) {
            this.groupMail = groupMail;
            return this;
        }

        public NotificationBuilder hidden(boolean hidden) {
            this.hidden = hidden;
            return this;
        }

        public NotificationBuilder subjectReplacements(Object[] subjectReplacements) {
            this.subjectReplacements = subjectReplacements;
            return this;
        }

        public NotificationBuilder messageReplacements(Object[] messageReplacements) {
            this.messageReplacements = messageReplacements;
            return this;
        }

        public NotificationBuilder senderId(String senderId) {
            this.senderId = senderId;
            return this;
        }

        public NotificationBuilder senderType(SenderType senderType) {
            this.senderType = senderType;
            return this;
        }

        public NotificationBuilder receiverId(String receiverId) {
            this.receiverId = receiverId;
            return this;
        }

        public NotificationBuilder receiverType(ReceiverType receiverType) {
            this.receiverType = receiverType;
            return this;
        }

        public NotificationBuilder mailReplacements(Map<String, String> mailReplacements) {
            this.mailReplacements = mailReplacements;
            return this;
        }

        public NotificationBuilder attachments(List<Attachment> attachments) {
            this.attachments = attachments;
            return this;
        }

        public NotificationBuilder recipients(List<Recipient> recipients) {
            this.recipients = recipients;
            return this;
        }

        public NotificationBuilder category(NotificationCategory category) {
            this.category = category;
            return this;
        }

        public NotificationBuilder customSenderEmailAddress(String customSenderEmailAddress) {
            this.customSenderEmailAddress = customSenderEmailAddress;
            return this;
        }

        public NotificationBuilder customSenderName(String customSenderName) {
            this.customSenderName = customSenderName;
            return this;
        }

        public Notification build() {
            return new Notification(
                this.subject,
                this.message,
                this.template,
                this.orderId,
                this.offerId,
                this.sendMail,
                this.groupMail,
                this.hidden,
                this.subjectReplacements,
                this.messageReplacements,
                this.senderId,
                this.senderType,
                this.receiverId,
                this.receiverType,
                this.mailReplacements,
                this.attachments,
                this.recipients,
                this.category,
                this.customSenderEmailAddress,
                this.customSenderName
            );
        }

        public String toString() {
            return (
                "Notification.NotificationBuilder(subject=" +
                this.subject +
                ", message=" +
                this.message +
                ", template=" +
                this.template +
                ", orderId=" +
                this.orderId +
                ", offerId=" +
                this.offerId +
                ", sendMail=" +
                this.sendMail +
                ", groupMail=" +
                this.groupMail +
                ", hidden=" +
                this.hidden +
                ", subjectReplacements=" +
                java.util.Arrays.deepToString(this.subjectReplacements) +
                ", messageReplacements=" +
                java.util.Arrays.deepToString(this.messageReplacements) +
                ", senderId=" +
                this.senderId +
                ", senderType=" +
                this.senderType +
                ", receiverId=" +
                this.receiverId +
                ", receiverType=" +
                this.receiverType +
                ", mailReplacements=" +
                this.mailReplacements +
                ", attachments=" +
                this.attachments +
                ", recipients=" +
                this.recipients +
                ", category=" +
                this.category +
                ")"
            );
        }
    }
}
