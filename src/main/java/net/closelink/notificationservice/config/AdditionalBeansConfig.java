package net.closelink.notificationservice.config;

import de.tschumacher.queueservice.sns.SNSQueue;
import de.tschumacher.queueservice.sns.SNSQueueConfiguration;
import de.tschumacher.queueservice.sqs.SQSQueue;
import de.tschumacher.queueservice.sqs.SQSQueueConfiguration;
import net.closelink.cenqueue.consumer.CENQConsumer;
import net.closelink.cenqueue.consumer.CENQHandler;
import net.closelink.cenqueue.domainvalue.EventType;
import net.closelink.cenqueue.domainvalue.offer.OfferUpdatedEventType;
import net.closelink.notificationservice.handler.approval.created.ApprovalRequestCreatedHandler;
import net.closelink.notificationservice.handler.approval.reminder.ApprovalRequestPendingReminderHandler;
import net.closelink.notificationservice.handler.approval.updated.ApprovalRequestUpdatedHandler;
import net.closelink.notificationservice.handler.demand.StockSafetyReserveDemandNotificationCreatedHandler;
import net.closelink.notificationservice.handler.demand.StockWarningLimitDemandNotificationCreatedHandler;
import net.closelink.notificationservice.handler.keyportcall.KeyPortCallNotificationCreatedHandler;
import net.closelink.notificationservice.handler.lead.LeadCreatedHandler;
import net.closelink.notificationservice.handler.offer.created.OfferCreatedHandler;
import net.closelink.notificationservice.handler.offer.message.OfferMessageHandler;
import net.closelink.notificationservice.handler.offer.reminder.OfferConfirmReminderHandler;
import net.closelink.notificationservice.handler.offer.reminder.OfferDeliveryReminderHandler;
import net.closelink.notificationservice.handler.offer.updated.OfferUpdatedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.AgentChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.AgentIdChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.BuyerRefChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.DateDeliveryChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.EtaChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.EtdChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.FileAddedChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.FileRemovedChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.NoticeDaysChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.PaymentTermReferenceChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.PortChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.QuoteValidityTimeChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.SupplyModeChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.ValidityTimeChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.VesselChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.item.ItemAddedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.item.ItemDeletedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.item.ItemPackTypeChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.item.ItemPhysicalSupplierNameChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.item.ItemPriceChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.item.ItemProductChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.item.ItemUndeletedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.item.ItemUnitSizeChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.item.ItemUnitsChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.samplekit.SamplekitAddedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.samplekit.SamplekitDeletedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.samplekit.SamplekitNameChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.samplekit.SamplekitQuantityChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.samplekit.SamplekitUndeletedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.samplekit.SamplekitValueChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge.SurchargeAddedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge.SurchargeDeletedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge.SurchargeNameChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge.SurchargeTypeChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge.SurchargeUndeletedHandler;
import net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge.SurchargeValueChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.state.OfferUpdatedStateChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedHandlerRegistry;
import net.closelink.notificationservice.handler.order.created.OrderCreatedHandler;
import net.closelink.notificationservice.handler.order.reminder.OrderOpenReminderHandler;
import net.closelink.notificationservice.handler.order.updated.OrderUpdatedHandler;
import net.closelink.notificationservice.handler.order.updated.support.OrderUpdatedHandlerRegistry;
import net.closelink.notificationservice.handler.passwordreset.PasswordResetRequestCreatedHandler;
import net.closelink.notificationservice.handler.passwordreset.PasswordResetRequestResetHandler;
import net.closelink.notificationservice.handler.price.PriceListUploadedHandler;
import net.closelink.notificationservice.handler.user.UserInvitedHandler;
import net.closelink.notificationservice.handler.vesselrequisition.VesselRequisitionCreatedHandler;
import net.closelink.notificationservice.handler.vesselrequisition.VesselRequisitionImportedHandler;
import net.closelink.notificationservice.service.cenqueue.EventHandlerRegistry;
import net.closelink.permission.PermissionEvaluatorRegistry;
import net.closelink.properties.SNSProperties;
import net.closelink.properties.SQSProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;

@Configuration
public class AdditionalBeansConfig {

    // ####################################################################################################
    // PermissionEvaluatorRegistry
    // ####################################################################################################
    @Bean
    public PermissionEvaluatorRegistry permissionEvaluatorRegistry() {
        return new PermissionEvaluatorRegistry();
    }

    // ####################################################################################################
    // Strings
    // ####################################################################################################
    @Bean
    public ResourceBundleMessageSource messageSource() {
        final ResourceBundleMessageSource source = new ResourceBundleMessageSource();
        source.setBasenames("strings/messages");
        source.setUseCodeAsDefaultMessage(true);
        return source;
    }

    // ####################################################################################################
    // NotificationRegistry
    // ####################################################################################################
    @Bean
    public EventHandlerRegistry eventHandlerRegistry(
        PriceListUploadedHandler priceListUploadedHandler,
        UserInvitedHandler userInvitedHandler,
        OfferCreatedHandler offerCreatedHandler,
        OfferUpdatedHandler offerUpdatedHandler,
        OrderUpdatedHandler orderUpdatedHandler,
        OrderOpenReminderHandler orderOpenReminderHandler,
        OfferDeliveryReminderHandler offerDeliveryReminderHandler,
        OfferMessageHandler offerMessageHandler,
        StockWarningLimitDemandNotificationCreatedHandler stockWarningLimitDemandNotificationCreatedHandler,
        StockSafetyReserveDemandNotificationCreatedHandler stockSafetyReserveDemandNotificationCreatedHandler,
        LeadCreatedHandler leadCreatedHandler,
        OfferConfirmReminderHandler offerConfirmReminderHandler,
        ApprovalRequestCreatedHandler approvalRequestCreatedHandler,
        ApprovalRequestUpdatedHandler approvalRequestUpdatedHandler,
        ApprovalRequestPendingReminderHandler approvalRequestPendingReminderHandler,
        OrderCreatedHandler orderCreatedHandler,
        VesselRequisitionCreatedHandler vesselRequisitionCreatedHandler,
        VesselRequisitionImportedHandler vesselRequisitionImportedHandler,
        PasswordResetRequestCreatedHandler passwordResetRequestCreatedHandler,
        PasswordResetRequestResetHandler passwordResetRequestResetHandler,
        KeyPortCallNotificationCreatedHandler keyPortCallNotificationCreatedHandler
    ) {
        EventHandlerRegistry registry = new EventHandlerRegistry();
        registry.put(EventType.PRICE_LIST_UPLOADED, priceListUploadedHandler);
        registry.put(EventType.USER_INVITED, userInvitedHandler);
        registry.put(EventType.OFFER_CREATED, offerCreatedHandler);
        registry.put(EventType.OFFER_UPDATED, offerUpdatedHandler);
        registry.put(EventType.ORDER_CREATED, orderCreatedHandler);
        registry.put(EventType.ORDER_UPDATED, orderUpdatedHandler);
        registry.put(EventType.ORDER_OPEN_REMINDER, orderOpenReminderHandler);
        registry.put(EventType.OFFER_DELIVERY_REMINDER, offerDeliveryReminderHandler);
        registry.put(EventType.OFFER_MESSAGE, offerMessageHandler);
        registry.put(EventType.LEAD_CREATED, leadCreatedHandler);
        registry.put(EventType.OFFER_CONFIRM_REMINDER, offerConfirmReminderHandler);
        registry.put(EventType.APPROVAL_REQUEST_CREATED, approvalRequestCreatedHandler);
        registry.put(EventType.APPROVAL_REQUEST_UPDATED, approvalRequestUpdatedHandler);
        registry.put(EventType.APPROVAL_REQUEST_PENDING_REMINDER, approvalRequestPendingReminderHandler);
        registry.put(EventType.VESSEL_REQUISITION_CREATED, vesselRequisitionCreatedHandler);
        registry.put(EventType.VESSEL_REQUISITION_IMPORTED, vesselRequisitionImportedHandler);
        registry.put(EventType.PASSWORD_RESET_REQUEST_CREATED, passwordResetRequestCreatedHandler);
        registry.put(EventType.PASSWORD_RESET_REQUEST_RESET, passwordResetRequestResetHandler);
        registry.put(EventType.KEY_PORT_CALL_NOTIFICATION_CREATED, keyPortCallNotificationCreatedHandler);
        registry.put(EventType.STOCK_DEMAND_NOTIFICATION_CREATED, stockWarningLimitDemandNotificationCreatedHandler);
        registry.put(
            EventType.STOCK_DEMAND_SAFETY_RESERVE_NOTIFICATION_CREATED,
            stockSafetyReserveDemandNotificationCreatedHandler
        );

        return registry;
    }

    // ####################################################################################################
    // OfferUpdatedHandler
    // ####################################################################################################
    @Bean
    public OfferUpdatedHandlerRegistry offerUpdatedHandlerRegistry(
        final OfferUpdatedStateChangedHandler orderUpdatedStateChangedHandler,
        final AgentChangedHandler agentChangedHandler,
        final AgentIdChangedHandler agentIdChangedHandler,
        final DateDeliveryChangedHandler dateDeliveryChangedHandler,
        final NoticeDaysChangedHandler noticeDaysChangedHandler,
        final PaymentTermReferenceChangedHandler paymentTermReferenceChangedHandler,
        final PortChangedHandler portChangedHandler,
        final QuoteValidityTimeChangedHandler quoteValidityTimeChangedHandler,
        final SupplyModeChangedHandler supplyModeChangedHandler,
        final VesselChangedHandler vesselChangedHandler,
        final ItemAddedHandler itemAddedHandler,
        final ItemDeletedHandler itemDeletedHandler,
        final ItemPackTypeChangedHandler itemPackTypeChangedHandler,
        final ItemPriceChangedHandler itemPriceChangedHandler,
        final ItemProductChangedHandler itemProductChangedHandler,
        final ItemUndeletedHandler itemUndeletedHandler,
        final ItemUnitsChangedHandler itemUnitsChangedHandler,
        final ItemUnitSizeChangedHandler itemUnitSizeChangedHandler,
        final ItemPhysicalSupplierNameChangedHandler itemPhysicalSupplierNameChangedHandler,
        final SamplekitAddedHandler samplekitAddedHandler,
        final SamplekitDeletedHandler samplekitDeletedHandler,
        final SamplekitNameChangedHandler samplekitNameChangedHandler,
        final SamplekitQuantityChangedHandler samplekitQuantityChangedHandler,
        final SamplekitUndeletedHandler samplekitUndeletedHandler,
        final SamplekitValueChangedHandler samplekitValueChangedHandler,
        final SurchargeAddedHandler surchargeAddedHandler,
        final SurchargeDeletedHandler surchargeDeletedHandler,
        final SurchargeNameChangedHandler surchargeNameChangedHandler,
        final SurchargeUndeletedHandler surchargeUndeletedHandler,
        final SurchargeValueChangedHandler surchargeValueChangedHandler,
        final SurchargeTypeChangedHandler surchargeTypeChangedHandler,
        final EtaChangedHandler etaChangedHandler,
        final EtdChangedHandler etdChangedHandler,
        final ValidityTimeChangedHandler validityTimeChangedHandler,
        final FileAddedChangedHandler fileAddedChangedHandler,
        final FileRemovedChangedHandler fileRemovedChangedHandler,
        final BuyerRefChangedHandler buyerRefChangedHandler
    ) {
        final OfferUpdatedHandlerRegistry registry = new OfferUpdatedHandlerRegistry();

        registry.put(OfferUpdatedEventType.STATE_CHANGED, orderUpdatedStateChangedHandler);
        registry.put(OfferUpdatedEventType.AGENT_CHANGED, agentChangedHandler);
        registry.put(OfferUpdatedEventType.AGENT_ID_CHANGED, agentIdChangedHandler);
        registry.put(OfferUpdatedEventType.DATE_DELIVERY_CHANGED, dateDeliveryChangedHandler);
        registry.put(OfferUpdatedEventType.NOTICE_DAYS_CHANGED, noticeDaysChangedHandler);
        registry.put(OfferUpdatedEventType.PAYMENT_TERM_REFERENCE_CHANGED, paymentTermReferenceChangedHandler);
        registry.put(OfferUpdatedEventType.PORT_CHANGED, portChangedHandler);
        registry.put(OfferUpdatedEventType.SUPPLY_MODE_CHANGED, supplyModeChangedHandler);
        registry.put(OfferUpdatedEventType.VESSEL_CHANGED, vesselChangedHandler);
        registry.put(OfferUpdatedEventType.QUOTE_VALIDITY_TIME_CHANGED, quoteValidityTimeChangedHandler);

        registry.put(OfferUpdatedEventType.ITEM_ADDED, itemAddedHandler);
        registry.put(OfferUpdatedEventType.ITEM_REMOVED, itemDeletedHandler);
        registry.put(OfferUpdatedEventType.ITEM_PACK_TYPE_CHANGED, itemPackTypeChangedHandler);
        registry.put(OfferUpdatedEventType.ITEM_PRICE_CHANGED, itemPriceChangedHandler);
        registry.put(OfferUpdatedEventType.ITEM_PRODUCT_CHANGED, itemProductChangedHandler);
        registry.put(OfferUpdatedEventType.ITEM_RESTORED, itemUndeletedHandler);
        registry.put(OfferUpdatedEventType.ITEM_UNITS_CHANGED, itemUnitsChangedHandler);
        registry.put(OfferUpdatedEventType.ITEM_UNIT_SIZE_CHANGED, itemUnitSizeChangedHandler);
        registry.put(OfferUpdatedEventType.ITEM_PHYSICAL_SUPPLIER_NAME_CHANGED, itemPhysicalSupplierNameChangedHandler);

        registry.put(OfferUpdatedEventType.SAMPLEKIT_ADDED, samplekitAddedHandler);
        registry.put(OfferUpdatedEventType.SAMPLEKIT_REMOVED, samplekitDeletedHandler);
        registry.put(OfferUpdatedEventType.SAMPLEKIT_NAME_CHANGED, samplekitNameChangedHandler);
        registry.put(OfferUpdatedEventType.SAMPLEKIT_QUANTITY_CHANGED, samplekitQuantityChangedHandler);
        registry.put(OfferUpdatedEventType.SAMPLEKIT_RESTORED, samplekitUndeletedHandler);
        registry.put(OfferUpdatedEventType.SAMPLEKIT_VALUE_CHANGED, samplekitValueChangedHandler);

        registry.put(OfferUpdatedEventType.SURCHARGE_ADDED, surchargeAddedHandler);
        registry.put(OfferUpdatedEventType.SURCHARGE_REMOVED, surchargeDeletedHandler);
        registry.put(OfferUpdatedEventType.SURCHARGE_NAME_CHANGED, surchargeNameChangedHandler);
        registry.put(OfferUpdatedEventType.SURCHARGE_RESTORED, surchargeUndeletedHandler);
        registry.put(OfferUpdatedEventType.SURCHARGE_VALUE_CHANGED, surchargeValueChangedHandler);
        registry.put(OfferUpdatedEventType.SURCHARGE_TYPE_CHANGED, surchargeTypeChangedHandler);

        registry.put(OfferUpdatedEventType.ETA_CHANGED, etaChangedHandler);
        registry.put(OfferUpdatedEventType.ETD_CHANGED, etdChangedHandler);
        registry.put(OfferUpdatedEventType.VALIDITY_TIME_CHANGED, validityTimeChangedHandler);

        registry.put(OfferUpdatedEventType.FILE_ADDED, fileAddedChangedHandler);
        registry.put(OfferUpdatedEventType.FILE_REMOVED, fileRemovedChangedHandler);

        registry.put(OfferUpdatedEventType.BUYERREF_CHANGED, buyerRefChangedHandler);

        return registry;
    }

    // ####################################################################################################
    // OrderUpdatedEventHandler
    // ####################################################################################################
    @Bean
    public OrderUpdatedHandlerRegistry orderUpdatedHandlerRegistry() {
        return new OrderUpdatedHandlerRegistry();
    }

    // ####################################################################################################
    // CENQ
    // ####################################################################################################
    @Bean
    public CENQConsumer cenqConsumer(final SNSQueue snsQueue, final SQSQueue sqsQueue, final CENQHandler cenqHandler) {
        return new CENQConsumer(snsQueue, sqsQueue, cenqHandler);
    }

    // ####################################################################################################
    // SNS
    // ####################################################################################################
    @Bean
    public SNSQueue snsQueue(final SNSProperties properties) {
        return new SNSQueue(
            SNSQueueConfiguration.builder()
                .accessKey(properties.getAws().getAccessKey())
                .secretKey(properties.getAws().getSecretKey())
                .topicName(properties.getAws().getTopic())
                .build()
        );
    }

    // ####################################################################################################
    // SQS
    // ####################################################################################################
    @Bean
    public SQSQueue sqsQueue(final SQSProperties properties) {
        return new SQSQueue(
            SQSQueueConfiguration.builder()
                .accessKey(properties.getAws().getAccessKey())
                .secretKey(properties.getAws().getSecretKey())
                .queueName(properties.getAws().getQueueName())
                .build()
        );
    }
}
