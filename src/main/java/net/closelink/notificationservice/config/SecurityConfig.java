package net.closelink.notificationservice.config;

import net.closelink.config.SecurityConfigSupport;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    private SecurityConfigSupport securityConfigSupport;

    public SecurityConfig(SecurityConfigSupport securityConfigSupport) {
        this.securityConfigSupport = securityConfigSupport;
    }

    @Bean
    protected AuthenticationManager authenticationManager(final HttpSecurity http) throws Exception {
        var authenticationManagerBuilder = http.getSharedObject(AuthenticationManagerBuilder.class);

        authenticationManagerBuilder.authenticationProvider(securityConfigSupport.createApiUserAuthProvider());
        authenticationManagerBuilder.authenticationProvider(securityConfigSupport.createApiKeyAuthProvider());

        return authenticationManagerBuilder.build();
    }

    @Bean
    protected SecurityFilterChain filterChain(
        final HttpSecurity http,
        final AuthenticationManager authenticationManager
    ) throws Exception {
        return securityConfigSupport
            .configure(http, authenticationManager)
            .authorizeHttpRequests(auth ->
                auth
                    .requestMatchers("/actuator/**")
                    .permitAll()
                    .requestMatchers("/notificationservice/api-docs/**")
                    .permitAll()
                    .requestMatchers("/**")
                    .denyAll()
            )
            .build();
    }
}
