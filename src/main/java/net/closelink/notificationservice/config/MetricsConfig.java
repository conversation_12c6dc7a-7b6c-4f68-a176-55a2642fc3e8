package net.closelink.notificationservice.config;

import io.micrometer.core.aop.TimedAspect;
import io.micrometer.core.instrument.Meter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.config.MeterFilter;
import io.micrometer.core.instrument.distribution.DistributionStatisticConfig;
import io.micrometer.prometheusmetrics.PrometheusNamingConvention;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MetricsConfig {

    @Bean
    public PrometheusNamingConvention prometheusNamingConvention() {
        return new PrometheusNamingConvention();
    }

    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags(
        PrometheusNamingConvention prometheusNamingConvention
    ) {
        return registry -> {
            registry
                .config()
                .namingConvention(prometheusNamingConvention)
                .meterFilter(
                    new MeterFilter() {
                        @Override
                        public DistributionStatisticConfig configure(Meter.Id id, DistributionStatisticConfig config) {
                            return config.merge(
                                // Configures the percentiles that are published to Prometheus
                                // percentiles: p0.5, p0.9, p0.95, p0.98, p0.99, p0.999
                                DistributionStatisticConfig.builder()
                                    .percentiles(0.5, 0.9, 0.95, 0.98, 0.99, 0.999)
                                    .build()
                            );
                        }
                    }
                );
        };
    }

    @Bean
    public TimedAspect timedAspect(MeterRegistry registry) {
        return new TimedAspect(registry);
    }
}
