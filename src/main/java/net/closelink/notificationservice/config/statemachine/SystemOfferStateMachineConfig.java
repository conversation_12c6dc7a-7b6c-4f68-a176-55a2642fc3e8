package net.closelink.notificationservice.config.statemachine;

import de.tschumacher.simplestatemachine.configuration.DefaultSimpleStateMachineConfig;
import de.tschumacher.simplestatemachine.configuration.SimpleStateMachineConfig;
import de.tschumacher.simplestatemachine.configuration.state.StateConfiguration;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.common.delivered.GeneralDeliveredHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry.EnquiryCanceledHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SystemOfferStateMachineConfig {

    @Bean(name = "systemStateMachine")
    public SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> systemSimpleStateMachineConfig(
        EnquiryCanceledHandler enquiryCanceledH<PERSON><PERSON>,
        GeneralDeliveredHandler generalDeliveredHandler
    ) {
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config = new DefaultSimpleStateMachineConfig<>();
        configureEnquiry(config, enquiryCanceledHandler);
        configureQuoted(config, enquiryCanceledHandler);
        configureConfirmed(config, generalDeliveredHandler);
        return config;
    }

    private void configureQuoted(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final EnquiryCanceledHandler enquiryCanceledHandler
    ) {
        final StateConfiguration<OfferState, OfferUpdatedEvent> configure = config.configure(OfferState.QUOTED);
        configure.permit(OfferState.QUOTE_CANCELED, enquiryCanceledHandler);
    }

    private void configureEnquiry(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final EnquiryCanceledHandler enquiryCanceledHandler
    ) {
        final StateConfiguration<OfferState, OfferUpdatedEvent> configure = config.configure(OfferState.ENQUIRY);
        configure.permit(OfferState.ENQUIRY_CANCELED, enquiryCanceledHandler);
    }

    private void configureConfirmed(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final GeneralDeliveredHandler generalDeliveredHandler
    ) {
        final StateConfiguration<OfferState, OfferUpdatedEvent> configure = config.configure(OfferState.CONFIRMED);
        configure.permit(OfferState.DELIVERED, generalDeliveredHandler);
    }
}
