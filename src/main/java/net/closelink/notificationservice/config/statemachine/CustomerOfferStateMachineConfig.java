package net.closelink.notificationservice.config.statemachine;

import de.tschumacher.simplestatemachine.configuration.DefaultSimpleStateMachineConfig;
import de.tschumacher.simplestatemachine.configuration.SimpleStateMachineConfig;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.common.delivered.GeneralDeliveredHandler;
import net.closelink.notificationservice.handler.offer.updated.state.common.invoiced.GeneralOrderInvoicedHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.acknowledge.AcknowledgedOrderHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.all.CustomerCanceledHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.confirm.ConfirmedOrderHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.delivered.DeliveredOrderHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry.EnquiryCanceledHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry.EnquiryExpiredHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry.EnquiryOrderHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.enquirydeclined.EnquiryDeclinedEnquiryHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.quote.QuoteDeclinedHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.quote.QuoteEnquiryHandler;
import net.closelink.notificationservice.handler.offer.updated.state.customer.quote.QuoteOrderHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CustomerOfferStateMachineConfig {

    @Bean(name = "customerStateMachine")
    public SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> customerSimpleStateMachineConfig(
        final CustomerCanceledHandler customerCanceledHandler,
        final ConfirmedOrderHandler confirmedOrderHandler,
        final QuoteEnquiryHandler quoteEnquiryHandler,
        final QuoteOrderHandler quoteOrderHandler,
        final EnquiryOrderHandler enquiryOrderHandler,
        final DeliveredOrderHandler deliveredOrderHandler,
        final EnquiryCanceledHandler enquiryCanceledHandler,
        final EnquiryExpiredHandler enquiryExpiredHandler,
        final QuoteDeclinedHandler quoteDeclinedHandler,
        final EnquiryDeclinedEnquiryHandler enquiryDeclinedEnquiryHandler,
        final AcknowledgedOrderHandler acknowledgedOrderHandler,
        final GeneralDeliveredHandler generalDeliveredHandler,
        final GeneralOrderInvoicedHandler generalOrderInvoicedHandler
    ) {
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config = new DefaultSimpleStateMachineConfig<>();
        configureCustomerEnquiry(config, enquiryOrderHandler, enquiryCanceledHandler, enquiryExpiredHandler);
        configureCustomerEnquiryQuote(
            config,
            quoteEnquiryHandler,
            quoteOrderHandler,
            enquiryCanceledHandler,
            quoteDeclinedHandler
        );
        configureCustomerDelivered(config, customerCanceledHandler, deliveredOrderHandler, generalOrderInvoicedHandler);
        configureCustomerCanceled(config, customerCanceledHandler, generalOrderInvoicedHandler);
        configureCustomerConfirmed(
            config,
            confirmedOrderHandler,
            customerCanceledHandler,
            generalDeliveredHandler,
            generalOrderInvoicedHandler
        );
        configureCustomerAcknowledged(
            config,
            acknowledgedOrderHandler,
            customerCanceledHandler,
            generalOrderInvoicedHandler
        );
        configureCustomerEnquiryDeclined(config, enquiryDeclinedEnquiryHandler);
        return config;
    }

    private void configureCustomerDelivered(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final CustomerCanceledHandler customerCanceledHandler,
        final DeliveredOrderHandler deliveredOrderHandler,
        final GeneralOrderInvoicedHandler generalOrderInvoicedHandler
    ) {
        config
            .configure(OfferState.DELIVERED)
            .permit(OfferState.ORDER, deliveredOrderHandler)
            .permit(OfferState.CANCELED, customerCanceledHandler)
            .permit(OfferState.INVOICED, generalOrderInvoicedHandler);
    }

    private void configureCustomerEnquiryQuote(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final QuoteEnquiryHandler quoteEnquiryHandler,
        final QuoteOrderHandler quoteOrderHandler,
        final EnquiryCanceledHandler enquiryCanceledHandler,
        final QuoteDeclinedHandler quoteDeclinedHandler
    ) {
        config
            .configure(OfferState.QUOTED)
            .permit(OfferState.ENQUIRY, quoteEnquiryHandler)
            .permit(OfferState.ORDER, quoteOrderHandler)
            .permit(OfferState.QUOTE_CANCELED, enquiryCanceledHandler)
            .permit(OfferState.QUOTE_DECLINED, quoteDeclinedHandler);
    }

    private void configureCustomerEnquiry(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final EnquiryOrderHandler enquiryOrderHandler,
        final EnquiryCanceledHandler enquiryCanceledHandler,
        final EnquiryExpiredHandler enquiryExpiredHandler
    ) {
        config
            .configure(OfferState.ENQUIRY)
            .permit(OfferState.ORDER, enquiryOrderHandler)
            .permit(OfferState.ENQUIRY_CANCELED, enquiryCanceledHandler)
            .permit(OfferState.ENQUIRY_EXPIRED, enquiryExpiredHandler);
    }

    private void configureCustomerEnquiryDeclined(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final EnquiryDeclinedEnquiryHandler enquiryDeclinedEnquiryHandler
    ) {
        config.configure(OfferState.ENQUIRY_DECLINED).permit(OfferState.ENQUIRY, enquiryDeclinedEnquiryHandler);
    }

    private void configureCustomerCanceled(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final CustomerCanceledHandler customerCanceledHandler,
        final GeneralOrderInvoicedHandler generalOrderInvoicedHandler
    ) {
        config
            .configure(OfferState.ORDER)
            .permit(OfferState.CANCELED, customerCanceledHandler)
            .permit(OfferState.INVOICED, generalOrderInvoicedHandler);
    }

    private void configureCustomerConfirmed(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final ConfirmedOrderHandler confirmedOrderHandler,
        final CustomerCanceledHandler customerCanceledHandler,
        final GeneralDeliveredHandler generalDeliveredHandler,
        final GeneralOrderInvoicedHandler generalOrderInvoicedHandler
    ) {
        config
            .configure(OfferState.CONFIRMED)
            .permit(OfferState.ORDER, confirmedOrderHandler)
            .permit(OfferState.DELIVERED, generalDeliveredHandler)
            .permit(OfferState.CANCELED, customerCanceledHandler)
            .permit(OfferState.INVOICED, generalOrderInvoicedHandler);
    }

    private void configureCustomerAcknowledged(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final AcknowledgedOrderHandler acknowledgedOrderHandler,
        final CustomerCanceledHandler customerCanceledHandler,
        final GeneralOrderInvoicedHandler generalOrderInvoicedHandler
    ) {
        config
            .configure(OfferState.ACKNOWLEDGED)
            .permit(OfferState.ORDER, acknowledgedOrderHandler)
            .permit(OfferState.CANCELED, customerCanceledHandler)
            .permit(OfferState.INVOICED, generalOrderInvoicedHandler);
    }
}
