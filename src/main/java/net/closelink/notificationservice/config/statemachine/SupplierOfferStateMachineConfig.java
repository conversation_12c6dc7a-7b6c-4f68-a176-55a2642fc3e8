package net.closelink.notificationservice.config.statemachine;

import de.tschumacher.simplestatemachine.configuration.DefaultSimpleStateMachineConfig;
import de.tschumacher.simplestatemachine.configuration.SimpleStateMachineConfig;
import de.tschumacher.simplestatemachine.configuration.state.StateConfiguration;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.common.delivered.SupplierDeliveredHandler;
import net.closelink.notificationservice.handler.offer.updated.state.common.invoiced.GeneralOrderInvoicedHandler;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.all.ConfirmedOrderChangedHandler;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.all.SupplierCanceledHandler;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.delivered.SupplierDeliveredConfirmedHandler;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.enquiry.EnquiryQuoteHandler;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.enquiry.SupplierEnquiryCanceledHandler;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.order.OrderConfirmedHandler;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.order.SupplierAcknowledgedHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SupplierOfferStateMachineConfig {

    @Bean(name = "supplierStateMachine")
    public SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> supplierSimpleStateMachineConfig(
        final EnquiryQuoteHandler enquiryQuoteHandler,
        final SupplierEnquiryCanceledHandler enquiryCanceledHandler,
        final OrderConfirmedHandler orderConfirmedHandler,
        final SupplierCanceledHandler supplierCanceledHandler,
        final SupplierDeliveredHandler supplierDeliveredHandler,
        final SupplierDeliveredConfirmedHandler deliveredConfirmHandler,
        final SupplierAcknowledgedHandler supplierAcknowledgedHandler,
        final GeneralOrderInvoicedHandler generalOrderInvoicedHandler,
        final ConfirmedOrderChangedHandler confirmedOrderChangedHandler
    ) {
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config = new DefaultSimpleStateMachineConfig<>();
        configureSupplierEnquiry(config, enquiryQuoteHandler, enquiryCanceledHandler);
        configureSupplierOrder(
            config,
            orderConfirmedHandler,
            supplierCanceledHandler,
            supplierAcknowledgedHandler,
            generalOrderInvoicedHandler
        );
        configureSupplierAcknowledged(
            config,
            supplierDeliveredHandler,
            orderConfirmedHandler,
            supplierCanceledHandler,
            generalOrderInvoicedHandler
        );
        configureSupplierConfirmed(
            config,
            supplierDeliveredHandler,
            supplierCanceledHandler,
            generalOrderInvoicedHandler,
            confirmedOrderChangedHandler
        );
        configureSupplierCanceled(config, enquiryCanceledHandler);
        configureSupplierAdjusted(config, orderConfirmedHandler, supplierDeliveredHandler);
        configureSupplierDelivered(config, deliveredConfirmHandler, generalOrderInvoicedHandler);
        return config;
    }

    private void configureSupplierDelivered(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final SupplierDeliveredConfirmedHandler deliveredConfirmHandler,
        final GeneralOrderInvoicedHandler generalOrderInvoicedHandler
    ) {
        final StateConfiguration<OfferState, OfferUpdatedEvent> configure = config.configure(OfferState.DELIVERED);
        configure
            .permit(OfferState.CONFIRMED, deliveredConfirmHandler)
            .permit(OfferState.INVOICED, generalOrderInvoicedHandler);
    }

    private void configureSupplierEnquiry(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final EnquiryQuoteHandler enquiryQuoteHandler,
        final SupplierEnquiryCanceledHandler enquiryCanceledHandler
    ) {
        final StateConfiguration<OfferState, OfferUpdatedEvent> configure = config.configure(OfferState.ENQUIRY);
        configure.permit(OfferState.QUOTED, enquiryQuoteHandler);
        configure.permit(OfferState.ENQUIRY_DECLINED, enquiryCanceledHandler);
    }

    private void configureSupplierOrder(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final OrderConfirmedHandler orderConfirmedHandler,
        final SupplierCanceledHandler supplierCanceledHandler,
        final SupplierAcknowledgedHandler supplierAcknowledgedHandler,
        final GeneralOrderInvoicedHandler generalOrderInvoicedHandler
    ) {
        final StateConfiguration<OfferState, OfferUpdatedEvent> orderStateConfiguration = config.configure(
            OfferState.ORDER
        );

        orderStateConfiguration
            .permit(OfferState.ACKNOWLEDGED, supplierAcknowledgedHandler)
            .permit(OfferState.CONFIRMED, orderConfirmedHandler)
            .permit(OfferState.CANCELED, supplierCanceledHandler)
            .permit(OfferState.INVOICED, generalOrderInvoicedHandler);
    }

    private void configureSupplierAcknowledged(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final SupplierDeliveredHandler supplierDeliveredHandler,
        final OrderConfirmedHandler orderConfirmedHandler,
        final SupplierCanceledHandler supplierCanceledHandler,
        final GeneralOrderInvoicedHandler generalAcknowledgedInvoicedHandler
    ) {
        final StateConfiguration<OfferState, OfferUpdatedEvent> acknowledgedStateConfiguration = config.configure(
            OfferState.ACKNOWLEDGED
        );

        acknowledgedStateConfiguration
            .permit(OfferState.DELIVERED, supplierDeliveredHandler)
            .permit(OfferState.CONFIRMED, orderConfirmedHandler)
            .permit(OfferState.CANCELED, supplierCanceledHandler)
            .permit(OfferState.INVOICED, generalAcknowledgedInvoicedHandler);
    }

    private void configureSupplierAdjusted(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final OrderConfirmedHandler orderConfirmedHandler,
        final SupplierDeliveredHandler supplierDeliveredHandler
    ) {
        var configure = config.configure(OfferState.SUPPLIER_ADJUSTED);
        configure
            .permit(OfferState.CONFIRMED, orderConfirmedHandler)
            .permit(OfferState.DELIVERED, supplierDeliveredHandler);
    }

    private void configureSupplierConfirmed(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final SupplierDeliveredHandler supplierDeliveredHandler,
        final SupplierCanceledHandler supplierCanceledHandler,
        final GeneralOrderInvoicedHandler generalConfirmedInvoicedHandler,
        final ConfirmedOrderChangedHandler confirmedOrderChangedHandler
    ) {
        final StateConfiguration<OfferState, OfferUpdatedEvent> configure = config.configure(OfferState.CONFIRMED);
        configure
            .permit(OfferState.DELIVERED, supplierDeliveredHandler)
            .permit(OfferState.CANCELED, supplierCanceledHandler)
            .permit(OfferState.INVOICED, generalConfirmedInvoicedHandler)
            .permit(OfferState.ORDER, confirmedOrderChangedHandler)
            .permit(OfferState.ACKNOWLEDGED, confirmedOrderChangedHandler);
    }

    private void configureSupplierCanceled(
        final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> config,
        final SupplierEnquiryCanceledHandler enquiryCanceledHandler
    ) {
        config.configure(OfferState.QUOTED).permit(OfferState.ENQUIRY_DECLINED, enquiryCanceledHandler);
    }
}
