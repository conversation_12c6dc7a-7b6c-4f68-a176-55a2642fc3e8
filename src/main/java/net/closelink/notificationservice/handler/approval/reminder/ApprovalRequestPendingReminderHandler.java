package net.closelink.notificationservice.handler.approval.reminder;

import com.google.common.base.Strings;
import java.util.Collections;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestPendingReminderEventMessage;
import net.closelink.cenqueue.objects.ApprovalRequest;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.Recipient;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.handler.approval.replacement.ApprovalRequestCreatedReplacementSupportService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.offer.OfferApiRestService;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class ApprovalRequestPendingReminderHandler implements EventHandler {

    private final NotificationService notificationService;
    private final OfferApiRestService offerApiRestService;
    private final ApprovalRequestCreatedReplacementSupportService approvalRequestCreatedReplacementSupportService;
    private static final String APPROVAL_REQUEST_TEMPLATE = "Approval_Controller_Reminder";
    private static final String APPROVAL_REQUEST_SUBJECT = "approval.reminder.subject";
    private static final String APPROVAL_REQUEST_SUBJECT_BUYER_REF = "approval.reminder.buyerRef.subject";
    private static final String APPROVAL_REQUEST_MESSAGE = "approval.reminder.message";

    @Override
    public void handle(final Event event) {
        final ApprovalRequestPendingReminderEventMessage approvalRequestPendingReminderEventMessage =
            createEventMessage(event);
        ApprovalRequest approvalRequest = approvalRequestPendingReminderEventMessage.getApprovalRequest();

        Recipient recipient = Recipient.builder()
            .emailAddress(approvalRequestPendingReminderEventMessage.getApproverEmailAddress())
            .type(Recipient.Type.TO)
            .build();

        OfferMessage offer = this.offerApiRestService.getOffer(approvalRequest.getOfferId());
        if (!"LUBES".equals(offer.getType())) {
            log.info("Offer does not have type LUBES but {}, exiting early", offer.getType());
            return;
        }

        Notification build = Notification.builder()
            .receiverType(ReceiverType.CUSTOM)
            .recipients(Collections.singletonList(recipient))
            .sendMail(true)
            .message(APPROVAL_REQUEST_MESSAGE)
            .subject(
                Strings.isNullOrEmpty(offer.getBuyerReference())
                    ? APPROVAL_REQUEST_SUBJECT
                    : APPROVAL_REQUEST_SUBJECT_BUYER_REF
            )
            .subject(APPROVAL_REQUEST_SUBJECT)
            .hidden(true)
            .offerId(approvalRequest.getOfferId())
            .template(APPROVAL_REQUEST_TEMPLATE)
            .senderId(approvalRequest.getCustomerId())
            .senderType(SenderType.CUSTOMER)
            .mailReplacements(
                approvalRequestCreatedReplacementSupportService.createReplacements(
                    approvalRequestPendingReminderEventMessage,
                    offer
                )
            )
            .subjectReplacements(approvalRequestCreatedReplacementSupportService.createSubjectReplacements(offer))
            .build();
        this.notificationService.notify(build);
    }

    private ApprovalRequestPendingReminderEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), ApprovalRequestPendingReminderEventMessage.class);
    }
}
