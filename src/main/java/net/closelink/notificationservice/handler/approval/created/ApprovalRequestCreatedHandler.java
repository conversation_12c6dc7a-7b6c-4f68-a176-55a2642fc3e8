package net.closelink.notificationservice.handler.approval.created;

import com.google.common.base.Strings;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestCreatedEventMessage;
import net.closelink.cenqueue.objects.ApprovalRequest;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.Recipient;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.handler.approval.replacement.ApprovalRequestCreatedReplacementSupportService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.offer.OfferApiRestService;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;
import net.closelink.notificationservice.service.rest.offerapprovalrequests.OfferApprovalRequestService;
import net.closelink.notificationservice.service.rest.offerapprovalrequests.OfferApprovalRequestsMessage;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class ApprovalRequestCreatedHandler implements EventHandler {

    private final NotificationService notificationService;
    private final ApprovalRequestCreatedReplacementSupportService approvalRequestCreatedReplacementSupportService;
    private final OfferApiRestService offerService;
    private final OfferApprovalRequestService offerApprovalRequestService;

    private static final String APPROVAL_REQUEST_TEMPLATE = "Approval_Controller_Initial";
    private static final String APPROVAL_REQUEST_ORDERED_TEMPLATE = "Approval_Controller_Regain";
    private static final String APPROVAL_REQUEST_SUBJECT = "approval.created.subject";
    private static final String APPROVAL_REQUEST_SUBJECT_BUYER_REF = "approval.created.buyerRef.subject";
    private static final String APPROVAL_REQUEST_MESSAGE = "approval.created.message";

    @Override
    public void handle(final Event event) {
        final ApprovalRequestCreatedEventMessage approvalRequestCreatedEventMessage = createEventMessage(event);
        ApprovalRequest approvalRequest = approvalRequestCreatedEventMessage.getApprovalRequest();

        OfferMessage offer = offerService.getOffer(approvalRequest.getOfferId());
        if (!"LUBES".equals(offer.getType())) {
            log.info("Offer does not have type LUBES but {}, exiting early", offer.getType());
            return;
        }

        List<OfferApprovalRequestsMessage.ApprovalRequestMessage> approvalRequests =
            offerApprovalRequestService.getApprovalRequestsForOfferId(approvalRequest.getOfferId());

        Recipient recipient = Recipient.builder()
            .emailAddress(approvalRequestCreatedEventMessage.getApproverEmailAddress())
            .type(Recipient.Type.TO)
            .build();

        Notification notification = Notification.builder()
            .receiverType(ReceiverType.CUSTOM)
            .recipients(Collections.singletonList(recipient))
            .sendMail(true)
            .message(APPROVAL_REQUEST_MESSAGE)
            .subject(
                Strings.isNullOrEmpty(offer.getBuyerReference())
                    ? APPROVAL_REQUEST_SUBJECT
                    : APPROVAL_REQUEST_SUBJECT_BUYER_REF
            )
            .hidden(true)
            .offerId(approvalRequest.getOfferId())
            .template(selectTemplateForOffer(approvalRequests.size()))
            .senderId(approvalRequest.getCustomerId())
            .senderType(SenderType.CUSTOMER)
            .mailReplacements(
                approvalRequestCreatedReplacementSupportService.createReplacements(
                    approvalRequestCreatedEventMessage,
                    offer
                )
            )
            .subjectReplacements(approvalRequestCreatedReplacementSupportService.createSubjectReplacements(offer))
            .build();
        this.notificationService.notify(notification);
    }

    private String selectTemplateForOffer(int approvalRequestsCount) {
        return approvalRequestsCount > 1 ? APPROVAL_REQUEST_ORDERED_TEMPLATE : APPROVAL_REQUEST_TEMPLATE;
    }

    private ApprovalRequestCreatedEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), ApprovalRequestCreatedEventMessage.class);
    }
}
