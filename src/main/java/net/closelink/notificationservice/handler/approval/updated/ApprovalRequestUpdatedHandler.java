package net.closelink.notificationservice.handler.approval.updated;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestUpdatedEventMessage;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.service.rest.offer.OfferApiRestService;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class ApprovalRequestUpdatedHandler implements EventHandler {

    private final ApprovalRequestUpdatedApprovedHandler approvalRequestUpdatedApprovedHandler;
    private final ApprovalRequestUpdatedDeclinedHandler approvalRequestUpdatedDeclinedHandler;
    private final OfferApiRestService offerService;

    @Override
    public void handle(final Event event) {
        ApprovalRequestUpdatedEventMessage approvalRequestUpdatedEventMessage =
            createApprovalRequestUpdatedEventMessage(event.getRawMessage());

        OfferMessage offer = offerService.getOffer(
            approvalRequestUpdatedEventMessage.getApprovalRequest().getOfferId()
        );

        if (!"LUBES".equals(offer.getType())) {
            log.info("Offer does not have type LUBES but {}, exiting early", offer.getType());
            return;
        }

        if (approvalRequestUpdatedEventMessage.getApprovalRequest().getState().equals("APPROVED")) {
            approvalRequestUpdatedApprovedHandler.handle(approvalRequestUpdatedEventMessage);
        }
        if (approvalRequestUpdatedEventMessage.getApprovalRequest().getState().equals("DECLINED")) {
            approvalRequestUpdatedDeclinedHandler.handle(approvalRequestUpdatedEventMessage);
        }
    }

    private ApprovalRequestUpdatedEventMessage createApprovalRequestUpdatedEventMessage(String rawMessage) {
        return GsonCoder.encode(rawMessage, ApprovalRequestUpdatedEventMessage.class);
    }
}
