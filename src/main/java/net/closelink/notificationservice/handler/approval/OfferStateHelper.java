package net.closelink.notificationservice.handler.approval;

import java.util.Arrays;
import java.util.List;

public class OfferStateHelper {

    public static boolean isOrderState(String offerState) {
        List<String> orderStates = Arrays.asList(
            "ORDER",
            "ADJUSTED",
            "ACKNOWLEDGED",
            "CONFIRMED",
            "CANCELED",
            "DELIVERED",
            "DELIVERY_CONFIRMED",
            "INVOICED"
        );

        return orderStates.contains(offerState);
    }
}
