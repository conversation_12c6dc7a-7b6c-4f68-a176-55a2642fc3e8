package net.closelink.notificationservice.handler.approval.replacement;

import java.util.Map;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestCreatedEventMessage;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestPendingReminderEventMessage;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;

public interface ApprovalRequestCreatedReplacementSupportService {
    Map<String, String> createReplacements(
        ApprovalRequestCreatedEventMessage approvalRequestCreatedEventMessage,
        OfferMessage offerMessage
    );

    Map<String, String> createReplacements(
        ApprovalRequestPendingReminderEventMessage approvalRequestPendingReminderEventMessage,
        OfferMessage offerMessage
    );

    Object[] createSubjectReplacements(OfferMessage offerMessage);
}
