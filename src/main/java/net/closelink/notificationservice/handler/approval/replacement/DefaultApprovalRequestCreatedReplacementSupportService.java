package net.closelink.notificationservice.handler.approval.replacement;

import com.google.common.base.Strings;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestCreatedEventMessage;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestPendingReminderEventMessage;
import net.closelink.cenqueue.objects.ApprovalRequest;
import net.closelink.notificationservice.replacement.offer.OfferReplacementService;
import net.closelink.notificationservice.replacement.offer.mapper.OfferMessageReplacementOfferMapper;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;
import net.closelink.notificationservice.service.rest.user.UserApiRestService;
import net.closelink.notificationservice.service.rest.user.domain.UserMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class DefaultApprovalRequestCreatedReplacementSupportService
    implements ApprovalRequestCreatedReplacementSupportService {

    private final OfferReplacementService offerReplacementService;
    private final UserApiRestService userApiRestService;
    private final VesselApiRestService vesselApiRestService;
    private final MessageSource messageSource;

    @Override
    public Map<String, String> createReplacements(
        ApprovalRequestCreatedEventMessage approvalRequestCreatedEventMessage,
        OfferMessage offerMessage
    ) {
        return mapReplacements(
            approvalRequestCreatedEventMessage.getApprovalRequest(),
            offerMessage,
            approvalRequestCreatedEventMessage.getApprovalLink()
        );
    }

    @Override
    public Map<String, String> createReplacements(
        ApprovalRequestPendingReminderEventMessage approvalRequestPendingReminderEventMessage,
        OfferMessage offerMessage
    ) {
        return mapReplacements(
            approvalRequestPendingReminderEventMessage.getApprovalRequest(),
            offerMessage,
            approvalRequestPendingReminderEventMessage.getApprovalLink()
        );
    }

    private Map<String, String> mapReplacements(
        ApprovalRequest approvalRequest,
        OfferMessage offerMessage,
        String approvalLink
    ) {
        final Map<String, String> replacements = new HashMap<>(
            offerReplacementService.create(OfferMessageReplacementOfferMapper.create(offerMessage))
        );
        replacements.put("approval_link", approvalLink);
        replacements.put("purchaser_name", createPurchaserNameReplacements(approvalRequest.getCreatedBy()));

        if (!Strings.isNullOrEmpty(approvalRequest.getRequestMessage())) {
            replacements.put("request_message", approvalRequest.getRequestMessage());
        }

        return replacements;
    }

    @Override
    public Object[] createSubjectReplacements(final OfferMessage offerMessage) {
        return new Object[] {
            getVesselName(offerMessage.getVesselId()),
            offerMessage.getOfferNumber(),
            offerMessage.getBuyerReference(),
        };
    }

    private String getVesselName(final String vesselId) {
        return this.vesselApiRestService.getVessel(vesselId).getName();
    }

    private String createPurchaserNameReplacements(final String userId) {
        if (userId == null) {
            return this.messageSource.getMessage("approvalRequest.system", null, Locale.ENGLISH);
        }

        UserMessage user = userApiRestService.getUser(userId);

        return user.getFirstname() + " " + user.getLastname();
    }
}
