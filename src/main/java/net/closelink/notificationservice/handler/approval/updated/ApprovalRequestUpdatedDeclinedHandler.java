package net.closelink.notificationservice.handler.approval.updated;

import com.google.common.base.Strings;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.domainobject.approvalrequest.ApprovalRequestUpdatedEventMessage;
import net.closelink.cenqueue.objects.ApprovalRequest;
import net.closelink.notificationservice.service.rest.offer.OfferApiRestService;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;
import net.closelink.notificationservice.service.rest.offerapprovalrequests.OfferApprovalRequestService;
import net.closelink.notificationservice.service.rest.offerapprovalrequests.OfferApprovalRequestsMessage;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class ApprovalRequestUpdatedDeclined<PERSON>and<PERSON> implements ApprovalRequestUpdatedStateHandler {

    private final ApprovalRequestUpdatedNotificationSender approvalRequestUpdatedNotificationSender;
    private final OfferApiRestService offerService;
    private final OfferApprovalRequestService offerApprovalRequestService;

    @Override
    public void handle(final ApprovalRequestUpdatedEventMessage approvalRequestUpdatedEventMessage) {
        ApprovalRequest approvalRequest = approvalRequestUpdatedEventMessage.getApprovalRequest();
        OfferMessage offer = offerService.getOffer(approvalRequest.getOfferId());

        if (!"LUBES".equals(offer.getType())) {
            log.info("Offer does not have type LUBES but {}, exiting early", offer.getType());
            return;
        }

        List<OfferApprovalRequestsMessage.ApprovalRequestMessage> approvalRequests =
            offerApprovalRequestService.getApprovalRequestsForOfferId(approvalRequest.getOfferId());

        approvalRequestUpdatedNotificationSender.sendNotification(
            approvalRequestUpdatedEventMessage.getApprovalRequest(),
            getTemplateForOffer(approvalRequests.size()),
            getSubjectForOffer(offer, approvalRequests.size())
        );
    }

    private String getTemplateForOffer(int approvalRequestsCount) {
        return approvalRequestsCount > 1 ? "Approval_Customer_Regain_Declined" : "Approval_Customer_Declined";
    }

    private String getSubjectForOffer(OfferMessage offerMessage, int approvalRequestsCount) {
        if (approvalRequestsCount > 1) {
            return Strings.isNullOrEmpty(offerMessage.getBuyerReference())
                ? "approvalRequest.regain.declined.subject"
                : "approvalRequest.regain.declined.subjectBuyerRef";
        }

        return Strings.isNullOrEmpty(offerMessage.getBuyerReference())
            ? "approvalRequest.declined.subject"
            : "approvalRequest.declined.subjectBuyerRef";
    }
}
