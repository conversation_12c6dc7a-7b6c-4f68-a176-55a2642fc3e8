package net.closelink.notificationservice.handler.approval.updated;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.objects.ApprovalRequest;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.replacement.offer.OfferReplacementService;
import net.closelink.notificationservice.replacement.offer.mapper.OfferMessageReplacementOfferMapper;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.offer.OfferApiRestService;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.support.RecipientsCreatorKt;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class ApprovalRequestUpdatedNotificationSender {

    private final WebAppReplacementService webappReplacementService;
    private final OfferReplacementService offerReplacementService;
    private final VesselApiRestService vesselApiRestService;
    private final OfferApiRestService offerApiRestService;
    private final NotificationService notificationService;

    public void sendNotification(ApprovalRequest approvalRequest, String templateName, String subject) {
        var offer = offerApiRestService.getOffer(approvalRequest.getOfferId());

        if (!"LUBES".equals(offer.getType())) {
            log.info("Offer does not have type LUBES but {}, exiting early", offer.getType());
            return;
        }

        var vessel = this.vesselApiRestService.getVessel(offer.getVesselId());
        var recipients = RecipientsCreatorKt.createRecipientListForVesselNotification(
            vessel.getEmail(),
            vessel.getMailNotificationSettings().getOrderUpdateSettings()
        );
        var receiverType = recipients.isEmpty() ? ReceiverType.CUSTOMER : ReceiverType.CUSTOM;

        Notification notification = Notification.builder()
            .template(templateName)
            .subject(subject)
            .sendMail(true)
            .offerId(offer.getId())
            .hidden(true)
            .receiverId(offer.getCustomerId())
            .receiverType(receiverType)
            .recipients(recipients)
            .senderType(SenderType.SYSTEM)
            .mailReplacements(createEmailReplacements(offer, approvalRequest))
            .subjectReplacements(createSubjectReplacements(offer, vessel.getName()))
            .category(NotificationCategory.OFFER_UPDATE)
            .build();

        notificationService.notify(notification);
    }

    private Map<String, String> createEmailReplacements(OfferMessage offer, ApprovalRequest approvalRequest) {
        Map<String, String> replacements = new HashMap<>();

        replacements.put("approval_request_controller_email_address", approvalRequest.getControllerEmailAddress());
        replacements.put(
            "approval_request_response_message",
            getStringOrNotAvailable(approvalRequest.getResponseMessage())
        );

        replacements.putAll(
            this.webappReplacementService.create(
                    OfferMessageReplacementOfferMapper.create(offer),
                    ReceiverType.CUSTOMER
                )
        );
        replacements.putAll(this.offerReplacementService.create(OfferMessageReplacementOfferMapper.create(offer)));

        return replacements;
    }

    private String getStringOrNotAvailable(String string) {
        return Optional.ofNullable(string).orElse("N/A");
    }

    private String[] createSubjectReplacements(OfferMessage offer, String vesselName) {
        return new String[] { vesselName, offer.getOfferNumber(), offer.getBuyerReference() };
    }
}
