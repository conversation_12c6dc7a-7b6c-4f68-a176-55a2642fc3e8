package net.closelink.notificationservice.handler.order.updated;

import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.order.OrderUpdatedEventMessage;
import net.closelink.cenqueue.domainvalue.order.OrderUpdatedEventType;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.notificationservice.exception.NoHandlerFoundException;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.handler.order.updated.support.OrderUpdatedEvent;
import net.closelink.notificationservice.handler.order.updated.support.OrderUpdatedEventTypeHandler;
import net.closelink.notificationservice.handler.order.updated.support.OrderUpdatedHandlerRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OrderUpdatedHand<PERSON> implements EventHandler {

    private final OrderUpdatedHandlerRegistry orderUpdatedHandlerRegistry;

    @Autowired
    public OrderUpdatedHandler(final OrderUpdatedHandlerRegistry orderUpdatedHandlerRegistry) {
        super();
        this.orderUpdatedHandlerRegistry = orderUpdatedHandlerRegistry;
    }

    @Override
    public void handle(final Event event) {
        final OrderUpdatedEventMessage eventMessage = createEventMessage(event);

        OrderType type = eventMessage.getOrder().getType();
        if (type != OrderType.LUBES) {
            log.info("Order does not have type LUBES but {}, exiting early", type);
            return;
        }

        findHandler(eventMessage.getEventType()).handle(createOrderUpdatedEvent(event, eventMessage));
    }

    private OrderUpdatedEvent createOrderUpdatedEvent(final Event event, final OrderUpdatedEventMessage eventMessage) {
        return OrderUpdatedEvent.builder().event(event).eventMessage(eventMessage).build();
    }

    private OrderUpdatedEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), OrderUpdatedEventMessage.class);
    }

    private OrderUpdatedEventTypeHandler findHandler(final OrderUpdatedEventType eventType) {
        final OrderUpdatedEventTypeHandler handler = this.orderUpdatedHandlerRegistry.get(eventType);

        if (handler == null) {
            throw new NoHandlerFoundException("No Handler found for OrderUpdatedEventType: " + eventType);
        }
        return handler;
    }
}
