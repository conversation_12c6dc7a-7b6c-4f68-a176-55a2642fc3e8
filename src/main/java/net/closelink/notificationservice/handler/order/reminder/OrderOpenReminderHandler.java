package net.closelink.notificationservice.handler.order.reminder;

import com.google.common.base.Strings;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.order.OrderOpenReminderEventMessage;
import net.closelink.cenqueue.objects.Order;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.replacement.order.OrderReplacementService;
import net.closelink.notificationservice.replacement.order.mapper.ReplacementOrderMapper;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.support.RecipientsCreatorKt;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@AllArgsConstructor
public class OrderOpenReminderHandler implements EventHandler {

    public static final String MESSAGE = "openTradeReminder.message";
    public static final String SUBJECT = "openTradeReminder.subject";
    public static final String SUBJECT_BUYER_REF = "openTradeReminderBuyerRef.subject";
    public static final String TEMPLATE = "Trade_Customer_Open_Reminder";

    private final NotificationService notificationService;
    private final VesselApiRestService vesselApiRestService;
    private final OrderReplacementService orderReplacementService;
    private final WebAppReplacementService webappReplacementService;

    @Override
    public void handle(final Event event) {
        final OrderOpenReminderEventMessage eventMessage = createEventMessage(event);

        OrderType type = eventMessage.getOrder().getType();
        if (type != OrderType.LUBES) {
            log.info("Order does not have type LUBES but {}, exiting early", type);
            return;
        }

        this.notificationService.notify(createNotification(eventMessage.getOrder()));
    }

    private Notification createNotification(final Order order) {
        var vessel = vesselApiRestService.getVessel(order.getVesselId());
        var recipients = RecipientsCreatorKt.createRecipientListForVesselNotification(
            vessel.getEmail(),
            vessel.getMailNotificationSettings().getOrderUpdateSettings()
        );

        var receiverType = recipients.isEmpty() ? ReceiverType.CUSTOMER : ReceiverType.CUSTOM;

        return Notification.builder()
            .orderId(order.getId())
            .hidden(true)
            .sendMail(true)
            .message(MESSAGE)
            .subject(Strings.isNullOrEmpty(order.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .subjectReplacements(createSubjectReplacements(order, vessel.getName()))
            .template(TEMPLATE)
            .mailReplacements(createReplacements(order))
            .receiverType(receiverType)
            .recipients(recipients)
            .receiverId(order.getCustomerId())
            .senderType(SenderType.SYSTEM)
            .category(NotificationCategory.OPEN_ENQUIRY_REMINDER)
            .build();
    }

    private Map<String, String> createReplacements(final Order order) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.putAll(this.webappReplacementService.create(order.getId()));
        replacements.putAll(this.orderReplacementService.create(ReplacementOrderMapper.replace(order)));
        return replacements;
    }

    protected Object[] createSubjectReplacements(final Order order, String vesselName) {
        return new Object[] { vesselName, order.getOrderNumber(), order.getBuyerReference() };
    }

    private OrderOpenReminderEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), OrderOpenReminderEventMessage.class);
    }
}
