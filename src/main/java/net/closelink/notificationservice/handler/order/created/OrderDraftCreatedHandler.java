package net.closelink.notificationservice.handler.order.created;

import com.google.common.base.Strings;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.domainobject.order.OrderCreatedEventMessage;
import net.closelink.cenqueue.objects.Order;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.Recipient;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.replacement.order.OrderReplacementService;
import net.closelink.notificationservice.replacement.order.mapper.ReplacementOrderMapper;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class OrderDraftCreatedHandler implements OfferCreatedEventHandler {

    private static final String DRAFT_CREATED_TEMPLATE = "Customer_New_Draft";
    private static final String DRAFT_CREATED_SUBJECT = "order.draftcreated.subject";

    private final NotificationService notificationService;
    private final VesselApiRestService vesselApiRestService;
    private final OrderReplacementService orderReplacementService;
    private final WebAppReplacementService webappReplacementService;

    @Override
    public void handle(final OrderCreatedEventMessage orderCreatedEventMessage) {
        Order order = orderCreatedEventMessage.getOrder();

        // We do not want to send out any emails for drafts created in the Closelink application.
        // If it was created by the Closelink application, the `source` property is not set.
        if (Strings.isNullOrEmpty(order.getSource())) {
            return;
        }

        Notification notification = createNotification(order);
        this.notificationService.notify(notification);
    }

    private Notification createNotification(Order order) {
        var vessel = this.vesselApiRestService.getVessel(order.getVesselId());
        var recipients = createRecipientsList(vessel);
        var receiverType = recipients.isEmpty() ? ReceiverType.CUSTOMER : ReceiverType.CUSTOM;

        return Notification.builder()
            .orderId(order.getId())
            .receiverType(receiverType)
            .recipients(recipients)
            .receiverId(order.getCustomerId())
            .senderType(SenderType.SYSTEM)
            .sendMail(true)
            .hidden(true)
            .subject(DRAFT_CREATED_SUBJECT)
            .subjectReplacements(createSubjectReplacements(order, vessel.getName()))
            .template(DRAFT_CREATED_TEMPLATE)
            .mailReplacements(createReplacements(order))
            .build();
    }

    @NotNull
    private static List<Recipient> createRecipientsList(VesselMessage vessel) {
        var orderUpdateSettings = vessel.getMailNotificationSettings().getOrderUpdateSettings();

        if (!orderUpdateSettings.isEnabled()) {
            return List.of();
        }

        return orderUpdateSettings
            .getRecipients()
            .stream()
            .map(mail -> Recipient.builder().emailAddress(mail).build())
            .toList();
    }

    private Map<String, String> createReplacements(Order order) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.putAll(this.webappReplacementService.createDraft(order.getId()));
        replacements.putAll(this.orderReplacementService.create(ReplacementOrderMapper.replace(order)));
        return replacements;
    }

    protected Object[] createSubjectReplacements(Order order, String vesselName) {
        return new Object[] { getOrderReference(order), vesselName };
    }

    private String getOrderReference(final Order order) {
        if (order.getBuyerReference() != null) {
            return order.getBuyerReference();
        }
        return order.getOrderNumber();
    }
}
