package net.closelink.notificationservice.handler.order.created;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.order.OrderCreatedEventMessage;
import net.closelink.cenqueue.types.OrderState;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.notificationservice.handler.EventHandler;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class OrderCreatedHandler implements EventHandler {

    private final OrderDraftCreatedHandler orderDraftCreatedHandler;

    @Override
    public void handle(Event event) {
        var eventMessage = createEventMessage(event, OrderCreatedEventMessage.class);

        OrderType type = eventMessage.getOrder().getType();
        if (type != OrderType.LUBES) {
            log.info("Order does not have type LUBES but {}, exiting early", type);
            return;
        }

        if (OrderState.DRAFT.equals(eventMessage.getOrder().getState())) {
            orderDraftCreatedHandler.handle(eventMessage);
        }
    }
}
