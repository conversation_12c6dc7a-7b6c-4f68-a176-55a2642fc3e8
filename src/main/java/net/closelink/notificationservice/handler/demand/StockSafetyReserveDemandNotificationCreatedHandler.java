package net.closelink.notificationservice.handler.demand;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.demand.StockDemandNotificationEventMessage;
import net.closelink.cenqueue.objects.StockDemand;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.coredata.CoreDataService;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.product.ProductApiRestService;
import net.closelink.notificationservice.service.rest.product.domain.ProductMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import net.closelink.notificationservice.service.rest.vesseltank.VesselTankApiRestService;
import net.closelink.notificationservice.service.rest.vesseltank.VesselTanksMessage;
import net.closelink.notificationservice.support.CollectionHandlingSupport;
import net.closelink.notificationservice.support.RecipientsCreatorKt;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class StockSafetyReserveDemandNotificationCreatedHandler implements EventHandler {

    private final NotificationService notificationService;
    private final CustomerApiRestService customerApiRestService;
    private final VesselApiRestService vesselApiRestService;
    private final VesselTankApiRestService vesselTankApiRestService;
    private final ProductApiRestService productApiRestService;
    private final WebAppReplacementService webappReplacementService;
    private final CoreDataService coreDataService;

    @Override
    public void handle(final Event event) {
        final StockDemandNotificationEventMessage eventMessage = createEventMessage(event);
        final Notification notification = createStockDemandNotification(eventMessage);
        this.notificationService.notify(notification);
    }

    private Notification createStockDemandNotification(
        final StockDemandNotificationEventMessage stockDemandNotificationEventMessage
    ) {
        List<StockDemand> demands = stockDemandNotificationEventMessage.getStockDemands();

        CustomerMessage customerMessage = customerApiRestService.getCustomer(
            stockDemandNotificationEventMessage.getCustomerId()
        );

        VesselMessage vesselMessage = vesselApiRestService.getVessel(stockDemandNotificationEventMessage.getVesselId());

        List<String> vesselTankIds = demands.stream().map(StockDemand::getVesselTankId).collect(Collectors.toList());
        List<VesselTanksMessage.VesselTankMessage> vesselTanks = vesselTankApiRestService.getVesselTanks(vesselTankIds);

        Set<String> productIds = vesselTanks
            .stream()
            .map(VesselTanksMessage.VesselTankMessage::getDefaultProductId)
            .collect(Collectors.toSet());
        Set<ProductMessage> products = productApiRestService.getProducts(productIds);

        Map<String, String> mailReplacements = new HashMap<>();

        Map<String, String> vesselDetailPageUrlReplacements = webappReplacementService.createVesselDetailPage(
            vesselMessage.getId()
        );

        mailReplacements.putAll(createDemandReplacements(demands, vesselTanks, products));
        mailReplacements.putAll(createCommonReplacement(customerMessage, vesselMessage));
        mailReplacements.putAll(vesselDetailPageUrlReplacements);

        var recipients = RecipientsCreatorKt.createRecipientListForVesselNotification(
            vesselMessage.getEmail(),
            vesselMessage.getMailNotificationSettings().getStockWarningSettings()
        );
        var receiverType = recipients.isEmpty() ? ReceiverType.CUSTOMER : ReceiverType.CUSTOM;

        return Notification.builder()
            .receiverId(customerMessage.getId())
            .receiverType(receiverType)
            .recipients(recipients)
            .senderType(SenderType.SYSTEM)
            .sendMail(true)
            .subject("stockDemand.safetyReserve.subject")
            .template("Customer_Safety_Reserve_Alert")
            .subjectReplacements(createSubjectReplacement(vesselMessage))
            .mailReplacements(mailReplacements)
            .category(NotificationCategory.SAFETY_RESERVE_LEVEL_REMINDER)
            .build();
    }

    private HashMap<String, String> createDemandReplacements(
        List<StockDemand> stockDemands,
        List<VesselTanksMessage.VesselTankMessage> vesselTanks,
        Set<ProductMessage> products
    ) {
        StringBuilder stockDemandList = new StringBuilder();

        stockDemands
            .stream()
            .filter(CollectionHandlingSupport.distinctByKey(StockDemand::getVesselTankId))
            .forEach(demand -> {
                var vesselTank = vesselTanks
                    .stream()
                    .filter(tank -> tank.getId().equals(demand.getVesselTankId()))
                    .findFirst()
                    .orElseGet(() -> {
                        log.warn("No vessel tank with id {} found", demand.getVesselTankId());
                        return null;
                    });

                if (vesselTank == null) return;

                String tankName = Optional.ofNullable(vesselTank.getCategory())
                    .map(coreDataService::getHumanReadableValueForVesselTankCategory)
                    .orElse((vesselTank.getName()));

                ProductMessage productMessage = products
                    .stream()
                    .filter(product -> product.getId().equals(vesselTank.getDefaultProductId()))
                    .findFirst()
                    .orElse(null);

                String demandLine = createReplacements(tankName, productMessage);

                stockDemandList.append(demandLine);
            });

        HashMap<String, String> replacement = new HashMap<>();

        String html = String.format("<ul>%s</ul>", stockDemandList);

        replacement.put("stock_demand_list", html);
        return replacement;
    }

    private String createReplacements(String tankName, ProductMessage productMessage) {
        return Optional.ofNullable(productMessage)
            .map(product ->
                String.format(
                    "<li>%s - %s (%s)</li>",
                    tankName,
                    coreDataService.getHumanReadableValueForProductGroup(product.getGroup()),
                    product.getName()
                )
            )
            .orElse(String.format("<li>%s</li>", tankName));
    }

    private Map<String, String> createCommonReplacement(
        final CustomerMessage customerMessage,
        VesselMessage vesselMessage
    ) {
        final Map<String, String> replacement = new HashMap<>();
        replacement.put("customer_name", customerMessage.getName());
        replacement.put("vessel_name", vesselMessage.getName());

        return replacement;
    }

    private Object[] createSubjectReplacement(final VesselMessage vessel) {
        return new Object[] { vessel.getName() };
    }

    private StockDemandNotificationEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), StockDemandNotificationEventMessage.class);
    }
}
