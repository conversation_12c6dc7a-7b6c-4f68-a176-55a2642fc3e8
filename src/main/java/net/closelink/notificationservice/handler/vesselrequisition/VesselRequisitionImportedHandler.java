package net.closelink.notificationservice.handler.vesselrequisition;

import com.amazonaws.util.StringUtils;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.vesselrequisition.VesselRequisitionCreatedEventMessage;
import net.closelink.cenqueue.domainobject.vesselrequisition.VesselRequisitionImportedEventMessage;
import net.closelink.notificationservice.domainobject.*;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.order.OrderApiRestService;
import net.closelink.notificationservice.service.rest.order.domain.OrderMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class VesselRequisitionImportedHandler implements EventHandler {

    private VesselApiRestService vesselApiRestService;
    private NotificationService notificationService;

    @Override
    public void handle(Event event) {
        VesselRequisitionImportedEventMessage vesselRequisitionImportedEventMessage = createEventMessage(event);

        VesselMessage vessel = vesselApiRestService.getVessel(vesselRequisitionImportedEventMessage.getVesselId());

        if (StringUtils.isNullOrEmpty(vessel.getEmail())) {
            return;
        }

        Notification notification = Notification.builder()
            .receiverType(ReceiverType.CUSTOM)
            .senderType(SenderType.CUSTOMER)
            .senderId(vessel.getCustomerId())
            .sendMail(true)
            .hidden(true)
            .subject("vesselRequisition.imported.subject")
            .template("VesselRequisitionImported")
            .mailReplacements(createReplacements(vessel.getName()))
            .recipients(
                Collections.singletonList(
                    Recipient.builder().emailAddress(vessel.getEmail()).name(vessel.getName()).build()
                )
            )
            .build();

        notificationService.notify(notification);
    }

    private Map<String, String> createReplacements(String vesselName) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.put("vessel_name", vesselName);

        return replacements;
    }

    private VesselRequisitionImportedEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), VesselRequisitionImportedEventMessage.class);
    }
}
