package net.closelink.notificationservice.handler.vesselrequisition;

import com.amazonaws.util.StringUtils;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.vesselrequisition.VesselRequisitionCreatedEventMessage;
import net.closelink.notificationservice.domainobject.Attachment;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.Recipient;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class VesselRequisitionCreatedHandler implements EventHandler {

    private VesselApiRestService vesselApiRestService;
    private CustomerApiRestService customerApiRestService;
    private NotificationService notificationService;

    @Override
    public void handle(Event event) {
        VesselRequisitionCreatedEventMessage vesselRequisitionCreatedEventMessage = createEventMessage(event);

        if (!vesselRequisitionCreatedEventMessage.getSendEmail()) {
            return;
        }

        VesselMessage vessel = vesselApiRestService.getVessel(vesselRequisitionCreatedEventMessage.getVesselId());

        CustomerMessage customer = customerApiRestService.getCustomer(vessel.getCustomerId());

        Notification notification = Notification.builder()
            .receiverType(ReceiverType.CUSTOM)
            .senderType(SenderType.CUSTOMER)
            .senderId(customer.getId())
            .sendMail(true)
            .hidden(true)
            .subject("vesselRequisition.created.subject")
            .subjectReplacements(createSubjectReplacements(customer.getName()))
            .template("VesselRequisitionCreated")
            .mailReplacements(createReplacements(vessel.getName(), customer.getName()))
            .recipients(
                Collections.singletonList(
                    Recipient.builder().emailAddress(vessel.getEmail()).name(vessel.getName()).build()
                )
            )
            .attachments(
                Collections.singletonList(
                    Attachment.builder()
                        .url(vesselRequisitionCreatedEventMessage.getFileUrl())
                        .name(vesselRequisitionCreatedEventMessage.getFileName())
                        .build()
                )
            )
            .build();

        notificationService.notify(notification);
    }

    private Map<String, String> createReplacements(String vesselName, String customerName) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.put("customer_name", customerName);
        replacements.put("vessel_name", vesselName);

        return replacements;
    }

    private Object[] createSubjectReplacements(String customerName) {
        return new Object[] { customerName };
    }

    private VesselRequisitionCreatedEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), VesselRequisitionCreatedEventMessage.class);
    }
}
