package net.closelink.notificationservice.handler.user;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.user.UserInvitedEventMessage;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.exception.NoHandlerFoundException;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.service.notification.NotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserInvitedHandler implements EventHandler {

    private List<UserInvitedNotificationCreator> userInvitedNotificationCreatorList = new ArrayList<>();
    private NotificationService notificationService;

    @Autowired
    public UserInvitedHandler(
        GeneralUserInvitedNotificationCreator generalUserInvitedNotificationCreator,
        AdminUserInvitedNotificationCreator adminUserInvitedNotificationCreator,
        NotificationService notificationService
    ) {
        this.notificationService = notificationService;

        this.userInvitedNotificationCreatorList.add(generalUserInvitedNotificationCreator);
        this.userInvitedNotificationCreatorList.add(adminUserInvitedNotificationCreator);
    }

    @Override
    public void handle(final Event event) {
        final UserInvitedEventMessage userInvitedEventMessage = createEventMessage(event);
        UserType userType = event.getEventTrigger().getCompanyType();

        UserInvitedNotificationCreator notificationCreator = findNotificationCreator(userType).orElseThrow(() ->
            new NoHandlerFoundException(String.format("No Handler found for userType %s", userType))
        );

        Notification notification = notificationCreator.createNotification(userInvitedEventMessage);
        this.notificationService.notify(notification);
    }

    private UserInvitedEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), UserInvitedEventMessage.class);
    }

    private Optional<UserInvitedNotificationCreator> findNotificationCreator(UserType userType) {
        return this.userInvitedNotificationCreatorList.stream()
            .filter(notificationCreator -> notificationCreator.supports(userType))
            .findAny();
    }
}
