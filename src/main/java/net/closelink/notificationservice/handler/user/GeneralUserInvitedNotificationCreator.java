package net.closelink.notificationservice.handler.user;

import java.util.HashMap;
import java.util.Map;
import net.closelink.cenqueue.domainobject.user.UserInvitedEventMessage;
import net.closelink.cenqueue.objects.User;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import org.springframework.stereotype.Component;

@Component
public class GeneralUserInvitedNotificationCreator implements UserInvitedNotificationCreator {

    private static final String ONBOARDING_TEMPLATE = "User_Onboarding";
    private static final String ONBOARDING_SUBJECT = "user.onboarding.subject";
    private static final String ONBOARDING_MESSAGE = "user.onboarding.message";

    @Override
    public Notification createNotification(UserInvitedEventMessage userInvitedEventMessage) {
        User user = userInvitedEventMessage.getUser();

        return Notification.builder()
            .receiverType(ReceiverType.USER)
            .receiverId(user.getId())
            .sendMail(true)
            .message(ONBOARDING_MESSAGE)
            .subject(ONBOARDING_SUBJECT)
            .template(ONBOARDING_TEMPLATE)
            .senderId(user.getGroupId())
            .senderType(getSenderType(user))
            .mailReplacements(createMailReplacements(userInvitedEventMessage))
            .build();
    }

    private SenderType getSenderType(final User user) {
        if (UserType.CUSTOMER.equals(user.getUserType())) {
            return SenderType.CUSTOMER_GROUP;
        } else {
            return SenderType.SUPPLIER_GROUP;
        }
    }

    private Map<String, String> createMailReplacements(UserInvitedEventMessage userInvitedEventMessage) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.put("onboarding_link", userInvitedEventMessage.getOnboardingLink());
        return replacements;
    }

    @Override
    public boolean supports(UserType userType) {
        return userType.equals(UserType.CUSTOMER) || userType.equals(UserType.SUPPLIER);
    }
}
