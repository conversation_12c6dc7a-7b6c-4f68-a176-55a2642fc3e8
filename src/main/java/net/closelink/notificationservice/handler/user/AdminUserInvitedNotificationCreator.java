package net.closelink.notificationservice.handler.user;

import java.util.HashMap;
import java.util.Map;
import net.closelink.cenqueue.domainobject.user.UserInvitedEventMessage;
import net.closelink.cenqueue.objects.User;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import org.springframework.stereotype.Component;

@Component
public class AdminUserInvitedNotificationCreator implements UserInvitedNotificationCreator {

    private static final String ONBOARDING_TEMPLATE = "User_Onboarding_Initial";
    private static final String ONBOARDING_SUBJECT = "user.onboarding.subject";
    private static final String ONBOARDING_MESSAGE = "user.onboarding.message";

    @Override
    public Notification createNotification(UserInvitedEventMessage userInvitedEventMessage) {
        User user = userInvitedEventMessage.getUser();

        return Notification.builder()
            .receiverType(ReceiverType.USER)
            .receiverId(user.getId())
            .sendMail(true)
            .message(ONBOARDING_MESSAGE)
            .subject(ONBOARDING_SUBJECT)
            .template(ONBOARDING_TEMPLATE)
            .senderType(SenderType.SYSTEM)
            .mailReplacements(createMailReplacements(userInvitedEventMessage))
            .build();
    }

    private Map<String, String> createMailReplacements(UserInvitedEventMessage userInvitedEventMessage) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.put("user_first_name", userInvitedEventMessage.getUser().getFirstname());
        replacements.put("user_last_name", userInvitedEventMessage.getUser().getLastname());
        replacements.put("onboarding_link", userInvitedEventMessage.getOnboardingLink());
        return replacements;
    }

    @Override
    public boolean supports(UserType userType) {
        return userType.equals(UserType.ADMIN);
    }
}
