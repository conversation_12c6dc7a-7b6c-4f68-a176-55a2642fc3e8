package net.closelink.notificationservice.handler.user;

import net.closelink.cenqueue.domainobject.user.UserInvitedEventMessage;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.domainobject.Notification;

public interface UserInvitedNotificationCreator {
    Notification createNotification(UserInvitedEventMessage userInvitedEventMessage);

    boolean supports(UserType userType);
}
