package net.closelink.notificationservice.handler.price;

import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.price.PriceListUploadedEventMessage;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.replacement.price.PriceListUploadReplacer;
import net.closelink.notificationservice.service.notification.NotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PriceListUploadedHandler implements EventHandler {

    private static final String UPLOAD_MESSAGE = "system.upload.message";
    private static final String UPLOAD_SUBJECT = "system.upload.subject";
    private static final String TEMPLATE = "system";

    private final NotificationService notificationService;

    @Autowired
    public PriceListUploadedHandler(final NotificationService notificationService) {
        super();
        this.notificationService = notificationService;
    }

    @Override
    public void handle(final Event event) {
        if (event.getEventTrigger() != null && event.getEventTrigger().getCompanyType() != UserType.CUSTOMER) {
            log.info("Ignoring PRICE_LIST_UPLOADED event for non customers");
            return;
        }

        final PriceListUploadedEventMessage eventMessage = createEventMessage(event);

        final Notification notification = createNotification(
            eventMessage.getCustomerId(),
            eventMessage.getSupplierGroupId()
        );

        this.notificationService.notify(notification);
    }

    private Notification createNotification(final String customerId, final String supplierGroupId) {
        return Notification.builder()
            .receiverType(ReceiverType.SYSTEM)
            .senderType(SenderType.SYSTEM)
            .sendMail(true)
            .message(UPLOAD_MESSAGE)
            .subject(UPLOAD_SUBJECT)
            .template(TEMPLATE)
            .mailReplacements(PriceListUploadReplacer.createReplacements(customerId, supplierGroupId))
            .build();
    }

    private PriceListUploadedEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), PriceListUploadedEventMessage.class);
    }
}
