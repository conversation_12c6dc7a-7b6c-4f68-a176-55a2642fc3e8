package net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge;

import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Surcharge;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class SurchargeAddedHandler implements OfferUpdatedEventTypeHandler {

    private static final String ACTIVITYLOG_SURCHARGE = "activitylog.surchargeadded";
    private static final String ACTIVITYLOG_WAIVER = "activitylog.waiveradded";

    private final BaseActivityLogService activityLogService;
    private final SurchargeNameFormatter surchargeNameFormatter;

    @Override
    public void handle(final OfferUpdatedEvent orderUpdatedEvent) {
        final Surcharge newValue = EncodeSupport.encodeSurcharge(orderUpdatedEvent.getEventMessage().getNewValue());

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                SurchargeHelper.getMessage(newValue, ACTIVITYLOG_SURCHARGE, ACTIVITYLOG_WAIVER),
                new Object[] { surchargeNameFormatter.getDisplayName(newValue) }
            );
    }
}
