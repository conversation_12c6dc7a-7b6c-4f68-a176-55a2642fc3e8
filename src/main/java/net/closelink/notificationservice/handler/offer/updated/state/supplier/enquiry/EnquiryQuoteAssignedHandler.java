package net.closelink.notificationservice.handler.offer.updated.state.supplier.enquiry;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class EnquiryQuoteAssignedHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE_OFFER = "enquiryQuote.message";
    private static final String SUBJECT_OFFER = "enquiryQuote.subject";
    private static final String SUBJECT_OFFER_BUYER_REF = "enquiryQuoteBuyerRef.subject";
    private static final String TEMPLATE_OFFER = "Order_Customer_New_Quote";

    private final SupplierStateHandlerSupport supplierStateHandlerSupport;

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        Notification notification = createQuoteNotification(offerUpdatedEvent);
        supplierStateHandlerSupport.notify(notification);
        return offerUpdatedEvent;
    }

    private Notification createQuoteNotification(final OfferUpdatedEvent event) {
        final Notification.NotificationBuilder builder =
            this.supplierStateHandlerSupport.createNotificationBuilder(event);

        Offer offer = event.getEventMessage().getOffer();

        return builder
            .message(MESSAGE_OFFER)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT_OFFER : SUBJECT_OFFER_BUYER_REF)
            .template(TEMPLATE_OFFER)
            .build();
    }
}
