package net.closelink.notificationservice.handler.offer.updated.state.supplier.enquiry;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class SupplierSpotEnquiryCanceledHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE = "supplierEnquiryCancelledTrade.message";
    private static final String SUBJECT = "supplierEnquiryCancelledTrade.subject";
    private static final String SUBJECT_BUYER_REF = "supplierEnquiryCancelledTradeBuyerRef.subject";
    private static final String TEMPLATE = "Order_Customer_Enquiry_Rejected_Trade";

    private SupplierStateHandlerSupport supplierStateHandlerSupport;

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        final Offer offer = offerUpdatedEvent.getEventMessage().getOffer();
        final Notification.NotificationBuilder builder =
            this.supplierStateHandlerSupport.createNotificationBuilder(offerUpdatedEvent);

        Notification notification = builder
            .message(MESSAGE)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .template(TEMPLATE)
            .sendMail(offer.getDateQuoted() != null)
            .messageReplacements(this.supplierStateHandlerSupport.createCancelMessageReplacements(offer))
            .subjectReplacements(this.supplierStateHandlerSupport.createSpotSubjectReplacements(offer))
            .build();

        this.supplierStateHandlerSupport.notify(notification);

        return offerUpdatedEvent;
    }
}
