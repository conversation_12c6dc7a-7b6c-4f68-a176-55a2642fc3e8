package net.closelink.notificationservice.handler.offer.updated.fields.activity.supplier;

import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.replacement.subject.supplier.SupplierSubjectReplacementService;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class DefaultOrderEmailNotificationCreator implements EmailNotificationCreator {

    private static final String SUPPLIER_OFFER_SUBJECT = "activitylog.subject.supplier.order";
    private static final String SUPPLIER_OFFER_SUBJECT_BUYER_REF = "activitylog.subject.supplier.orderBuyerRef";
    private static final String SUPPLIER_OFFER_TEMPLATE = "Order_Supplier_Order_Customer_Changes";

    private final SupplierSubjectReplacementService supplierSubjectReplacementService;

    @Override
    public Notification createNotification(Notification.NotificationBuilder notificationBuilder, Offer offer) {
        return notificationBuilder
            .subject(
                Strings.isNullOrEmpty(offer.getBuyerReference())
                    ? SUPPLIER_OFFER_SUBJECT
                    : SUPPLIER_OFFER_SUBJECT_BUYER_REF
            )
            .subjectReplacements(this.supplierSubjectReplacementService.createSubjectReplacements(offer))
            .template(SUPPLIER_OFFER_TEMPLATE)
            .groupMail(true)
            .sendMail(true)
            .build();
    }
}
