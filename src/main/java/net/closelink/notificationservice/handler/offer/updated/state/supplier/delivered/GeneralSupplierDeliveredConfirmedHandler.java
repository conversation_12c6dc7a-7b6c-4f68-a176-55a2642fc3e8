package net.closelink.notificationservice.handler.offer.updated.state.supplier.delivered;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GeneralSupplierDeliveredConfirmedHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE = "deliveredConfirm.message";
    private static final String SUBJECT = "deliveredConfirm.subject";
    private static final String SUBJECT_BUYER_REF = "deliveredConfirmBuyerRef.subject";
    private static final String TEMPLATE = "Order_Customer_Order_Supplier_Changes";

    private final SupplierStateHandlerSupport supplierStateHandlerSupport;

    @Autowired
    public GeneralSupplierDeliveredConfirmedHandler(final SupplierStateHandlerSupport supplierStateHandlerSupport) {
        this.supplierStateHandlerSupport = supplierStateHandlerSupport;
    }

    @Override
    public OfferUpdatedEvent handle(final StateChange<OfferState> stateChange, final OfferUpdatedEvent event) {
        final Notification.NotificationBuilder builder =
            this.supplierStateHandlerSupport.createNotificationBuilder(event);

        Offer offer = event.getEventMessage().getOffer();

        final Notification notification = builder
            .message(MESSAGE)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .template(TEMPLATE)
            .build();

        this.supplierStateHandlerSupport.notify(notification);
        return event;
    }
}
