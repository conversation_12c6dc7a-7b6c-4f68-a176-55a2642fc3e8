package net.closelink.notificationservice.handler.offer.updated.state.customer.all;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.replacement.subject.supplier.SupplierSubjectReplacementService;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ForwardedOrderCustomerAbortedHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String SUBJECT = "forwarded.orderCustomerCancel.subject";
    private static final String SUBJECT_BUYER_REF = "forwarded.orderCustomerCancel.subjectBuyerRef";
    private static final String MESSAGE = "customerCancel.message";
    private static final String TEMPLATE = "Supplier_Order_Forwarded_Canceled";

    private final CustomerStateHandlerSupport customerStateHandlerSupport;
    private final SupplierSubjectReplacementService supplierSubjectReplacementService;

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        Notification.NotificationBuilder notificationBuilder = customerStateHandlerSupport.createNotificationBuilder(
            offerUpdatedEvent
        );
        Offer offer = offerUpdatedEvent.getEventMessage().getOffer();

        Notification notification = notificationBuilder
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .message(MESSAGE)
            .messageReplacements(customerStateHandlerSupport.createCancelMessageReplacements(offer))
            .subjectReplacements(supplierSubjectReplacementService.createSubjectReplacements(offer))
            .template(TEMPLATE)
            .build();

        customerStateHandlerSupport.notify(notification);

        return offerUpdatedEvent;
    }
}
