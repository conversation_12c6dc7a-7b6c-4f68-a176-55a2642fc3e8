package net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge;

import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Surcharge;
import net.closelink.cenqueue.values.Money;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.support.EncodeSupport;
import net.closelink.utils.Formatter;
import org.joda.money.BigMoney;
import org.joda.money.CurrencyUnit;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class SurchargeValueChangedHandler implements OfferUpdatedEventTypeHandler {

    private static final String ACTIVITYLOG_SURCHARGE = "activitylog.surchargevalue";
    private static final String ACTIVITYLOG_WAIVER = "activitylog.waivervalue";

    private final BaseActivityLogService activityLogService;
    private final SurchargeNameFormatter surchargeNameFormatter;

    @Override
    public void handle(final OfferUpdatedEvent orderUpdatedEvent) {
        final Surcharge newValue = EncodeSupport.encodeSurcharge(orderUpdatedEvent.getEventMessage().getNewValue());
        final Surcharge oldValue = EncodeSupport.encodeSurcharge(orderUpdatedEvent.getEventMessage().getOldValue());

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                SurchargeHelper.getMessage(newValue, ACTIVITYLOG_SURCHARGE, ACTIVITYLOG_WAIVER),
                new Object[] {
                    surchargeNameFormatter.getDisplayName(newValue),
                    formatMoney(oldValue.getValue()),
                    formatMoney(newValue.getValue()),
                }
            );
    }

    private String formatMoney(final Money value) {
        if (value == null) {
            return null;
        }

        return Formatter.formatPrice(createMoney(value));
    }

    private static BigMoney createMoney(Money money) {
        if (money == null) return null;
        return BigMoney.of(CurrencyUnit.of(money.getCurrency()), money.getValue());
    }
}
