package net.closelink.notificationservice.handler.offer.updated.state.common.invoiced;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class GeneralOrderInvoicedHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String CUSTOMER_SUBJECT = "customerInvoiced.subject";
    private static final String CUSTOMER_SUBJECT_BUYER_REF = "customerInvoicedBuyerRef.subject";
    private static final String CUSTOMER_MESSAGE = "customerInvoiced.message";
    private static final String CUSTOMER_TEMPLATE = "Order_Customer_Order_Invoiced";

    private final SupplierStateHandlerSupport supplierStateHandlerSupport;

    @Override
    public OfferUpdatedEvent handle(final StateChange<OfferState> stateChange, final OfferUpdatedEvent event) {
        notifyCustomer(event);

        return event;
    }

    private void notifyCustomer(OfferUpdatedEvent event) {
        var offer = event.getEventMessage().getOffer();

        var notification =
            this.supplierStateHandlerSupport.createNotificationBuilder(event)
                .message(CUSTOMER_MESSAGE)
                .subject(
                    Strings.isNullOrEmpty(offer.getBuyerReference()) ? CUSTOMER_SUBJECT : CUSTOMER_SUBJECT_BUYER_REF
                )
                .subjectReplacements(this.supplierStateHandlerSupport.createInvoicedSubjectReplacements(offer))
                .template(CUSTOMER_TEMPLATE)
                .build();

        this.supplierStateHandlerSupport.notify(notification);
    }
}
