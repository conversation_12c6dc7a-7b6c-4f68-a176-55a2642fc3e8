package net.closelink.notificationservice.handler.offer.updated.fields.handler.samplekit;

import net.closelink.cenqueue.objects.Samplekit;
import net.closelink.cenqueue.values.Money;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.support.EncodeSupport;
import net.closelink.utils.Formatter;
import org.joda.money.BigMoney;
import org.joda.money.CurrencyUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SamplekitValueChangedHandler implements OfferUpdatedEventTypeHandler {

    private static final String ACTIVITYLOG = "activitylog.samplekitvalue";

    private final BaseActivityLogService activityLogService;

    @Autowired
    public SamplekitValueChangedHandler(final BaseActivityLogService activityLogService) {
        this.activityLogService = activityLogService;
    }

    @Override
    public void handle(final OfferUpdatedEvent orderUpdatedEvent) {
        final Samplekit newValue = EncodeSupport.encodeSamplekit(orderUpdatedEvent.getEventMessage().getNewValue());
        final Samplekit oldValue = EncodeSupport.encodeSamplekit(orderUpdatedEvent.getEventMessage().getOldValue());

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                ACTIVITYLOG,
                new Object[] { newValue.getName(), formatMoney(oldValue.getValue()), formatMoney(newValue.getValue()) }
            );
    }

    private String formatMoney(final Money value) {
        if (value == null) {
            return null;
        }

        return Formatter.formatPrice(createMoney(value));
    }

    private static BigMoney createMoney(Money money) {
        if (money == null) return null;
        return BigMoney.of(CurrencyUnit.of(money.getCurrency()), money.getValue());
    }
}
