package net.closelink.notificationservice.handler.offer.updated.state.supplier.enquiry;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class EnquiryQuoteSpotHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE_ORDER = "enquiryQuoteTrade.message";
    private static final String SUBJECT_ORDER = "enquiryQuoteTrade.subject";
    private static final String SUBJECT_ORDER_BUYER_REF = "enquiryQuoteTradeBuyerRef.subject";
    private static final String TEMPLATE_ORDER = "Order_Customer_New_Quote_Trade";

    private final SupplierStateHandlerSupport supplierStateHandlerSupport;

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        Notification notification = createSpotQuoteNotification(offerUpdatedEvent);
        supplierStateHandlerSupport.notify(notification);

        return offerUpdatedEvent;
    }

    private Notification createSpotQuoteNotification(final OfferUpdatedEvent event) {
        final Notification.NotificationBuilder builder =
            this.supplierStateHandlerSupport.createNotificationBuilder(event);

        Offer offer = event.getEventMessage().getOffer();

        return builder
            .message(MESSAGE_ORDER)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT_ORDER : SUBJECT_ORDER_BUYER_REF)
            .subjectReplacements(
                this.supplierStateHandlerSupport.createSpotSubjectReplacements(event.getEventMessage().getOffer())
            )
            .template(TEMPLATE_ORDER)
            .build();
    }
}
