package net.closelink.notificationservice.handler.offer.updated.state.customer.quote;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class QuoteOrderSpotHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE_OFFER = "quoteOrderTrade.message";
    private static final String SUBJECT_OFFER = "quoteOrderTrade.subject";
    private static final String SUBJECT_OFFER_BUYER_REF = "quoteOrderTradeBuyerRef.subject";
    private static final String TEMPLATE_OFFER = "Order_Supplier_Enquiry_To_Order_Trade";

    private final CustomerStateHandlerSupport customerStateHandlerSupport;

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        Notification notification = createOfferNotification(offerUpdatedEvent);

        this.customerStateHandlerSupport.notify(notification);

        return offerUpdatedEvent;
    }

    private Notification createOfferNotification(final OfferUpdatedEvent event) {
        final Notification.NotificationBuilder builder =
            this.customerStateHandlerSupport.createNotificationBuilder(event);

        Offer offer = event.getEventMessage().getOffer();

        return builder
            .message(MESSAGE_OFFER)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT_OFFER : SUBJECT_OFFER_BUYER_REF)
            .template(TEMPLATE_OFFER)
            .build();
    }
}
