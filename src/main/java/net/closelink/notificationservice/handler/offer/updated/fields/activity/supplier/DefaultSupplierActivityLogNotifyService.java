package net.closelink.notificationservice.handler.offer.updated.fields.activity.supplier;

import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.support.RecipientsSupportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DefaultSupplierActivityLogNotifyService implements SupplierActivityLogNotifyService {

    private NotificationService notificationService;

    private EmailNotificationCreatorRegistry enquiryEmailNotificationCreatorRegistry;
    private EmailNotificationCreatorRegistry orderEmailNotificationCreatorRegistry;

    private RecipientsSupportService recipientsSupportService;

    @Autowired
    public DefaultSupplierActivityLogNotifyService(
        NotificationService notificationService,
        SpotEnquiryEmailNotificationCreator spotEnquiryNotificationCreator,
        AssignedEnquiryEmailNotificationCreator assignedEnquiryNotificationCreator,
        ForwardedEnquiryEmailNotificationCreator forwardedEnquiryNotificationCreator,
        DefaultOrderEmailNotificationCreator defaultOrderNotificationCreator,
        ForwardedOrderEmailNotificationCreator forwardedOrderNotificationCreator,
        RecipientsSupportService recipientsSupportService
    ) {
        this.notificationService = notificationService;

        EmailNotificationCreatorRegistry enquiryEmailNotificationCreatorRegistry =
            new EmailNotificationCreatorRegistry();

        enquiryEmailNotificationCreatorRegistry.put(EnquiryType.SPOT, spotEnquiryNotificationCreator);
        enquiryEmailNotificationCreatorRegistry.put(EnquiryType.ASSIGNED, assignedEnquiryNotificationCreator);
        enquiryEmailNotificationCreatorRegistry.put(EnquiryType.FORWARDED, forwardedEnquiryNotificationCreator);

        this.enquiryEmailNotificationCreatorRegistry = enquiryEmailNotificationCreatorRegistry;

        EmailNotificationCreatorRegistry orderEmailNotificationCreatorRegistry = new EmailNotificationCreatorRegistry();

        orderEmailNotificationCreatorRegistry.put(EnquiryType.FORWARDED, forwardedOrderNotificationCreator);
        orderEmailNotificationCreatorRegistry.put(EnquiryType.SPOT, defaultOrderNotificationCreator);
        orderEmailNotificationCreatorRegistry.put(EnquiryType.ASSIGNED, defaultOrderNotificationCreator);

        this.orderEmailNotificationCreatorRegistry = orderEmailNotificationCreatorRegistry;
        this.recipientsSupportService = recipientsSupportService;
    }

    @Override
    public void notify(
        final OfferUpdatedEvent offerUpdatedEvent,
        final Notification.NotificationBuilder notificationBuilder
    ) {
        final Offer offer = offerUpdatedEvent.getEventMessage().getOffer();
        Notification notification;

        String userId = offerUpdatedEvent.getEvent().getEventTrigger().getUserId();
        if (userId != null) {
            notificationBuilder.senderType(SenderType.USER).senderId(userId);
        } else {
            notificationBuilder
                .senderType(SenderType.CUSTOMER)
                .senderId(offerUpdatedEvent.getEvent().getEventTrigger().getCompanyId());
        }

        var recipientInfo = recipientsSupportService.buildSupplierRecipientInfo(offer);
        notificationBuilder.receiverType(recipientInfo.getReceiverType());
        notificationBuilder.recipients(recipientInfo.getRecipients());
        notificationBuilder.receiverId(offer.getSupplierId());

        if (!offerUpdatedEvent.getEventMessage().getStateChanged() && shouldSendEmailForChanges(offer)) {
            log.info("Will create an email notification for supplier for offer with ID {}", offer.getId());
            notification = createEmailNotification(notificationBuilder, offer);
        } else {
            notification = notificationBuilder.build();
        }

        this.notificationService.notify(notification);
    }

    private Notification createEmailNotification(
        final Notification.NotificationBuilder notificationBuilder,
        final Offer offer
    ) {
        OfferState offerState = offer.getState();

        if (OfferState.ENQUIRY.equals(offerState) || isQuotedSpotOrder(offer)) {
            return createEnquiryNotification(notificationBuilder, offer);
        }

        if (
            OfferState.ORDER.equals(offerState) ||
            OfferState.CUSTOMER_ADJUSTED.equals(offerState) ||
            OfferState.SUPPLIER_ADJUSTED.equals(offerState) ||
            OfferState.CONFIRMED.equals(offerState) ||
            OfferState.ACKNOWLEDGED.equals(offerState)
        ) {
            return createOrderNotification(notificationBuilder, offer);
        }

        return notificationBuilder.build();
    }

    private Notification createOrderNotification(
        final Notification.NotificationBuilder notificationBuilder,
        final Offer offer
    ) {
        EmailNotificationCreator emailNotificationCreator = orderEmailNotificationCreatorRegistry.get(
            offer.getEnquiryType()
        );

        return emailNotificationCreator.createNotification(notificationBuilder, offer);
    }

    private Notification createEnquiryNotification(
        final Notification.NotificationBuilder notificationBuilder,
        final Offer offer
    ) {
        EmailNotificationCreator emailNotificationCreator = enquiryEmailNotificationCreatorRegistry.get(
            offer.getEnquiryType()
        );

        return emailNotificationCreator.createNotification(notificationBuilder, offer);
    }

    private boolean shouldSendEmailForChanges(final Offer offer) {
        OfferState offerState = offer.getState();

        return (
            OfferState.ORDER.equals(offerState) ||
            OfferState.CUSTOMER_ADJUSTED.equals(offerState) ||
            OfferState.SUPPLIER_ADJUSTED.equals(offerState) ||
            OfferState.ENQUIRY.equals(offerState) ||
            OfferState.CONFIRMED.equals(offerState) ||
            OfferState.ACKNOWLEDGED.equals(offerState) ||
            isQuotedSpotOrder(offer)
        );
    }

    private boolean isQuotedSpotOrder(final Offer offer) {
        return OfferState.QUOTED.equals(offer.getState()) && EnquiryType.SPOT.equals(offer.getEnquiryType());
    }
}
