package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import lombok.AllArgsConstructor;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class FileAddedChangedHandler implements OfferUpdatedEventTypeHandler {

    private final BaseActivityLogService activityLogService;

    @Override
    public void handle(OfferUpdatedEvent orderUpdatedEvent) {
        this.activityLogService.createActivityLog(orderUpdatedEvent, "activitylog.file-added", new Object[] {});
    }
}
