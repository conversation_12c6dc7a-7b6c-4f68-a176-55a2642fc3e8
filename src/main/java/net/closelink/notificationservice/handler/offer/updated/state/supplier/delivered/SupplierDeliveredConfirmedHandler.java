package net.closelink.notificationservice.handler.offer.updated.state.supplier.delivered;

import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.customer.StateChangeHandlerRegistry;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SupplierDeliveredConfirmedHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private final StateChangeHandlerRegistry stateChangeHandlerRegistry;

    @Autowired
    public SupplierDeliveredConfirmedHandler(
        GeneralSupplierDeliveredConfirmedHandler generalSupplierDeliveredConfirmedHandler
    ) {
        this.stateChangeHandlerRegistry = makeStateChangeHandlerRegistry(generalSupplierDeliveredConfirmedHandler);
    }

    private static StateChangeHandlerRegistry makeStateChangeHandlerRegistry(
        GeneralSupplierDeliveredConfirmedHandler generalSupplierDeliveredConfirmedHandler
    ) {
        StateChangeHandlerRegistry stateChangeHandlerRegistry = new StateChangeHandlerRegistry();

        stateChangeHandlerRegistry.put(EnquiryType.ASSIGNED, generalSupplierDeliveredConfirmedHandler);
        stateChangeHandlerRegistry.put(EnquiryType.SPOT, generalSupplierDeliveredConfirmedHandler);
        stateChangeHandlerRegistry.put(EnquiryType.FORWARDED, generalSupplierDeliveredConfirmedHandler);
        return stateChangeHandlerRegistry;
    }

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        EnquiryType enquiryType = offerUpdatedEvent.getEventMessage().getOffer().getEnquiryType();

        StateChangeHandler<OfferState, OfferUpdatedEvent> stateChangeHandler = stateChangeHandlerRegistry.get(
            enquiryType
        );

        return stateChangeHandler.handle(stateChange, offerUpdatedEvent);
    }
}
