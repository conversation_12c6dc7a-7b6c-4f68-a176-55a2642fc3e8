package net.closelink.notificationservice.handler.offer.updated.state.customer.quote;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class QuoteOrderAssignedHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE_ORDER = "quoteOrder.message";
    private static final String SUBJECT = "quoteOrder.subject";
    private static final String SUBJECT_BUYER_REF = "quoteOrderBuyerRef.subject";
    private static final String TEMPLATE_ORDER = "Order_Supplier_Enquiry_To_Order";

    private final CustomerStateHandlerSupport customerStateHandlerSupport;

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        Notification notification = createSpotOrderNotification(offerUpdatedEvent);

        customerStateHandlerSupport.notify(notification);

        return offerUpdatedEvent;
    }

    private Notification createSpotOrderNotification(final OfferUpdatedEvent event) {
        final Notification.NotificationBuilder builder =
            this.customerStateHandlerSupport.createNotificationBuilder(event);

        Offer offer = event.getEventMessage().getOffer();

        return builder
            .message(MESSAGE_ORDER)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .template(TEMPLATE_ORDER)
            .build();
    }
}
