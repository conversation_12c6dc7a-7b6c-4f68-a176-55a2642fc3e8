package net.closelink.notificationservice.handler.offer.updated.state.common.delivered;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class GeneralDeliveredHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String SUPPLIER_SUBJECT = "delivered.customer.subject";
    private static final String SUPPLIER_SUBJECT_BUYER_REF = "deliveredBuyerRef.customer.subject";
    private static final String SUPPLIER_MESSAGE = "delivered.customer.message";
    private static final String SUPPLIER_TEMPLATE = "Order_Supplier_Order_Delivered_New";

    private static final String CUSTOMER_SUBJECT = "delivered.supplier.subject";
    private static final String CUSTOMER_SUBJECT_BUYER_REF = "deliveredBuyerRef.supplier.subject";
    private static final String CUSTOMER_MESSAGE = "delivered.supplier.message";
    private static final String CUSTOMER_TEMPLATE = "Order_Customer_Order_Delivered_New";

    private final SupplierStateHandlerSupport supplierStateHandlerSupport;
    private final CustomerStateHandlerSupport customerStateHandlerSupport;

    @Override
    public OfferUpdatedEvent handle(final StateChange<OfferState> stateChange, final OfferUpdatedEvent event) {
        // Do not send "Delivered" Email to a Supplier of Type "FORWARDED"
        if (!EnquiryType.FORWARDED.equals(event.getEventMessage().getOffer().getEnquiryType())) {
            notifySupplier(event);
        }
        notifyCustomer(event);

        return event;
    }

    private void notifySupplier(OfferUpdatedEvent event) {
        var offer = event.getEventMessage().getOffer();

        var notification =
            this.customerStateHandlerSupport.createNotificationBuilder(event)
                .message(SUPPLIER_MESSAGE)
                .subject(
                    Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUPPLIER_SUBJECT : SUPPLIER_SUBJECT_BUYER_REF
                )
                .template(SUPPLIER_TEMPLATE)
                .hidden(true)
                .build();

        this.customerStateHandlerSupport.notify(notification);
    }

    private void notifyCustomer(OfferUpdatedEvent event) {
        var offer = event.getEventMessage().getOffer();

        var notification =
            this.supplierStateHandlerSupport.createNotificationBuilder(event)
                .message(CUSTOMER_MESSAGE)
                .subject(
                    Strings.isNullOrEmpty(offer.getBuyerReference()) ? CUSTOMER_SUBJECT : CUSTOMER_SUBJECT_BUYER_REF
                )
                .template(CUSTOMER_TEMPLATE)
                .build();

        this.supplierStateHandlerSupport.notify(notification);
    }
}
