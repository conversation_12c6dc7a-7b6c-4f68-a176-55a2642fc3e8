package net.closelink.notificationservice.handler.offer.updated.fields.translation;

import java.util.Locale;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

@Service
public class DefaultTranslationSupportService implements TranslationSupportService {

    private static final String ACTIVITYLOG_NA = "activitylog.na";
    private final MessageSource messageSource;

    @Autowired
    public DefaultTranslationSupportService(final MessageSource messageSource) {
        super();
        this.messageSource = messageSource;
    }

    @Override
    public Object translate(final Object value) {
        if (value == null) {
            return this.messageSource.getMessage(ACTIVITYLOG_NA, null, Locale.ENGLISH);
        }
        return value;
    }

    @Override
    public Object[] translate(final Object[] values) {
        for (int i = 0; i < values.length; i++) {
            values[i] = translate(values[i]);
        }
        return values;
    }
}
