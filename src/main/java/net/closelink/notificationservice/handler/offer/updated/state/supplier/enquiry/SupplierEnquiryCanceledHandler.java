package net.closelink.notificationservice.handler.offer.updated.state.supplier.enquiry;

import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.customer.StateChangeHandlerRegistry;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SupplierEnquiryCanceledHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private final StateChangeHandlerRegistry stateChangeHandlerRegistry;

    @Autowired
    public SupplierEnquiryCanceledHandler(
        SupplierSpotEnquiryCanceledHandler supplierSpotEnquiryCanceledHandler,
        SupplierAssignedEnquiryCanceledHandler supplierAssignedEnquiryCanceledHandler
    ) {
        this.stateChangeHandlerRegistry = makeStateChangeHandlerRegistry(
            supplierSpotEnquiryCanceledHandler,
            supplierAssignedEnquiryCanceledHandler
        );
    }

    private static StateChangeHandlerRegistry makeStateChangeHandlerRegistry(
        SupplierSpotEnquiryCanceledHandler supplierSpotEnquiryCanceledHandler,
        SupplierAssignedEnquiryCanceledHandler supplierAssignedEnquiryCanceledHandler
    ) {
        StateChangeHandlerRegistry stateChangeHandlerRegistry = new StateChangeHandlerRegistry();

        stateChangeHandlerRegistry.put(EnquiryType.ASSIGNED, supplierAssignedEnquiryCanceledHandler);
        stateChangeHandlerRegistry.put(EnquiryType.FORWARDED, supplierAssignedEnquiryCanceledHandler);
        stateChangeHandlerRegistry.put(EnquiryType.SPOT, supplierSpotEnquiryCanceledHandler);
        return stateChangeHandlerRegistry;
    }

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        EnquiryType enquiryType = offerUpdatedEvent.getEventMessage().getOffer().getEnquiryType();

        StateChangeHandler<OfferState, OfferUpdatedEvent> stateChangeHandler = stateChangeHandlerRegistry.get(
            enquiryType
        );

        return stateChangeHandler.handle(stateChange, offerUpdatedEvent);
    }
}
