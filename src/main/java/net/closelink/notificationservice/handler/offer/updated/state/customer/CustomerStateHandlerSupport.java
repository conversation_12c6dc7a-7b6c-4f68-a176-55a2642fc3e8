package net.closelink.notificationservice.handler.offer.updated.state.customer;

import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;

public interface CustomerStateHandlerSupport {
    Notification.NotificationBuilder createNotificationBuilder(OfferUpdatedEvent event);

    void notify(Notification notification);

    Object[] createCancelMessageReplacements(Offer offer);
}
