package net.closelink.notificationservice.handler.offer.updated.fields.activity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import net.closelink.cenqueue.domainobject.EventTrigger;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.customer.CustomerActivityLogNotifyService;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.supplier.SupplierActivityLogNotifyService;
import net.closelink.notificationservice.handler.offer.updated.fields.translation.TranslationSupportService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.replacement.offer.OfferReplacementService;
import net.closelink.notificationservice.replacement.offer.mapper.OfferReplacementOfferMapper;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DefaultBaseActivityLogService implements BaseActivityLogService {

    private final CustomerApiRestService customerApiRestService;
    private final SupplierApiRestService supplierApiRestService;
    private final CustomerActivityLogNotifyService customerActivityLogService;
    private final SupplierActivityLogNotifyService supplierActivityLogService;
    private final TranslationSupportService translationSupportService;
    private final OfferReplacementService offerReplacementService;
    private final WebAppReplacementService webappReplacementService;

    @Autowired
    public DefaultBaseActivityLogService(
        final CustomerApiRestService customerApiRestService,
        final SupplierApiRestService supplierApiRestService,
        final CustomerActivityLogNotifyService customerActivityLogService,
        final SupplierActivityLogNotifyService supplierActivityLogService,
        final TranslationSupportService translationSupportService,
        final OfferReplacementService offerReplacementService,
        final WebAppReplacementService webappReplacementService
    ) {
        super();
        this.customerApiRestService = customerApiRestService;
        this.supplierApiRestService = supplierApiRestService;
        this.customerActivityLogService = customerActivityLogService;
        this.supplierActivityLogService = supplierActivityLogService;
        this.translationSupportService = translationSupportService;
        this.offerReplacementService = offerReplacementService;
        this.webappReplacementService = webappReplacementService;
    }

    @Override
    public void createActivityLog(
        final OfferUpdatedEvent offerUpdatedEvent,
        final String messageKey,
        final Object[] replacements
    ) {
        final Notification.NotificationBuilder notificationBuilder = createNotificationBuilder(
            offerUpdatedEvent,
            messageKey,
            replacements
        );

        notifyCompany(offerUpdatedEvent, notificationBuilder);
    }

    private Notification.NotificationBuilder createNotificationBuilder(
        final OfferUpdatedEvent orderUpdatedEvent,
        final String messageKey,
        final Object[] replacements
    ) {
        final Offer offer = orderUpdatedEvent.getEventMessage().getOffer();
        final EventTrigger eventTrigger = orderUpdatedEvent.getEvent().getEventTrigger();
        return Notification.builder()
            .offerId(offer.getId())
            .message(messageKey)
            .messageReplacements(createMessageReplacements(orderUpdatedEvent, replacements))
            .mailReplacements(createMailReplacements(offer, eventTrigger))
            .category(NotificationCategory.OFFER_UPDATE);
    }

    private Map<String, String> createMailReplacements(final Offer offer, final EventTrigger eventTrigger) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.putAll(
            this.webappReplacementService.create(
                    OfferReplacementOfferMapper.create(offer),
                    getMessageReceiverType(eventTrigger.getCompanyType())
                )
        );
        replacements.putAll(this.offerReplacementService.create(OfferReplacementOfferMapper.create(offer)));
        return replacements;
    }

    private void notifyCompany(
        final OfferUpdatedEvent orderUpdatedEvent,
        final Notification.NotificationBuilder notificationBuilder
    ) {
        if (isLastChangedBySupplier(orderUpdatedEvent.getEvent().getEventTrigger().getCompanyType())) {
            this.customerActivityLogService.notify(orderUpdatedEvent, notificationBuilder);
        } else {
            this.supplierActivityLogService.notify(orderUpdatedEvent, notificationBuilder);
        }
    }

    private Object[] createMessageReplacements(final OfferUpdatedEvent orderUpdatedEvent, final Object[] replacements) {
        final List<Object> messageReplacements = new ArrayList<>();
        messageReplacements.add(getCompanyName(orderUpdatedEvent.getEvent().getEventTrigger()));
        final Object[] translatedReplacements = this.translationSupportService.translate(replacements);
        messageReplacements.addAll(Arrays.asList(translatedReplacements));
        return messageReplacements.toArray();
    }

    private String getCompanyName(final EventTrigger eventTrigger) {
        if (isLastChangedBySupplier(eventTrigger.getCompanyType())) {
            return getSupplierName(eventTrigger.getCompanyId());
        } else {
            return getCustomerName(eventTrigger.getCompanyId());
        }
    }

    private ReceiverType getMessageReceiverType(final UserType lastChanger) {
        if (isLastChangedBySupplier(lastChanger)) {
            return ReceiverType.CUSTOMER;
        } else {
            return ReceiverType.SUPPLIER;
        }
    }

    private boolean isLastChangedBySupplier(final UserType lastChanger) {
        return UserType.SUPPLIER.equals(lastChanger);
    }

    private String getCustomerName(final String customerId) {
        return this.customerApiRestService.getCustomer(customerId).getName();
    }

    private String getSupplierName(final String supplierId) {
        return this.supplierApiRestService.getSupplier(supplierId).getName();
    }
}
