package net.closelink.notificationservice.handler.offer.reminder;

import com.google.common.base.Strings;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.offer.OfferConfirmReminderEventMessage;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.replacement.offer.OfferReplacementService;
import net.closelink.notificationservice.replacement.offer.mapper.OfferReplacementOfferMapper;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class OfferConfirmReminderHandler implements EventHandler {

    public static final String MESSAGE = "offerConfirmReminder.message";
    public static final String SUBJECT = "offerConfirmReminder.subject";
    public static final String SUBJECT_BUYER_REF = "offerConfirmReminder.subjectBuyerRef";
    public static final String TEMPLATE = "Order_Supplier_Reminder";

    private final NotificationService notificationService;
    private final OfferReplacementService offerReplacementService;
    private final VesselApiRestService vesselApiRestService;
    private final WebAppReplacementService webappReplacementService;

    @Override
    public void handle(final Event event) {
        final OfferConfirmReminderEventMessage eventMessage = createEventMessage(event);

        OrderType type = eventMessage.getOffer().getType();
        if (type != OrderType.LUBES) {
            log.info("Offer does not have type LUBES but {}, exiting early", type);
            return;
        }

        if (eventMessage.getOffer().getEnquiryType() == EnquiryType.FORWARDED) {
            return;
        }

        this.notificationService.notify(createNotification(eventMessage.getOffer()));
    }

    private Notification createNotification(final Offer offer) {
        return Notification.builder()
            .offerId(offer.getId())
            .hidden(true)
            .sendMail(true)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .subjectReplacements(createSubjectReplacements(offer))
            .template(TEMPLATE)
            .mailReplacements(createReplacements(offer))
            .receiverType(ReceiverType.SUPPLIER)
            .receiverId(offer.getSupplierId())
            .senderType(SenderType.SYSTEM)
            .category(NotificationCategory.SYSTEM_REMINDER)
            .build();
    }

    private Map<String, String> createReplacements(final Offer offer) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.putAll(
            this.webappReplacementService.create(OfferReplacementOfferMapper.create(offer), ReceiverType.SUPPLIER)
        );
        replacements.putAll(this.offerReplacementService.create(OfferReplacementOfferMapper.create(offer)));
        return replacements;
    }

    private Object[] createSubjectReplacements(final Offer offer) {
        return new Object[] { getVesselName(offer.getVesselId()), offer.getOfferNumber(), offer.getBuyerReference() };
    }

    private String getVesselName(final String vesselId) {
        return this.vesselApiRestService.getVessel(vesselId).getName();
    }

    private OfferConfirmReminderEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), OfferConfirmReminderEventMessage.class);
    }
}
