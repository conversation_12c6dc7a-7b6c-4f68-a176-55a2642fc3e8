package net.closelink.notificationservice.handler.offer.updated;

import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.offer.OfferUpdatedEventMessage;
import net.closelink.cenqueue.domainvalue.offer.OfferUpdatedEventType;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.notificationservice.exception.NoHandlerFoundException;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedHandlerRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OfferUpdatedHandler implements EventHandler {

    private final OfferUpdatedHandlerRegistry offerUpdatedHandlerRegistry;

    @Autowired
    public OfferUpdatedHandler(final OfferUpdatedHandlerRegistry offerUpdatedHandlerRegistry) {
        super();
        this.offerUpdatedHandlerRegistry = offerUpdatedHandlerRegistry;
    }

    @Override
    public void handle(final Event event) {
        final OfferUpdatedEventMessage eventMessage = createEventMessage(event);

        OrderType type = eventMessage.getOffer().getType();
        if (type != OrderType.LUBES) {
            log.info("Offer does not have type LUBES but {}, exiting early", type);
            return;
        }

        findHandler(eventMessage.getEventType()).handle(createOrderUpdatedEvent(event, eventMessage));
    }

    private OfferUpdatedEvent createOrderUpdatedEvent(final Event event, final OfferUpdatedEventMessage eventMessage) {
        return OfferUpdatedEvent.builder().event(event).eventMessage(eventMessage).build();
    }

    private OfferUpdatedEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), OfferUpdatedEventMessage.class);
    }

    private OfferUpdatedEventTypeHandler findHandler(final OfferUpdatedEventType eventType) {
        final OfferUpdatedEventTypeHandler handler = this.offerUpdatedHandlerRegistry.get(eventType);

        if (handler == null) {
            throw new NoHandlerFoundException("No Handler found for OfferUpdatedEventType: " + eventType);
        }
        return handler;
    }
}
