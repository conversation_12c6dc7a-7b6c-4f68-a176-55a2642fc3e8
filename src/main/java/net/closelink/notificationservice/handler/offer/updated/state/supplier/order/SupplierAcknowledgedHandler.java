package net.closelink.notificationservice.handler.offer.updated.state.supplier.order;

import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.customer.StateChangeHandlerRegistry;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SupplierAcknowledgedHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private final StateChangeHandlerRegistry stateChangeHandlerRegistry;

    @Autowired
    public SupplierAcknowledgedHandler(GeneralSupplierAcknowledgedHandler generalSupplierAcknowledgedHandler) {
        this.stateChangeHandlerRegistry = makeStateChangeHandlerRegistry(generalSupplierAcknowledgedHandler);
    }

    private static StateChangeHandlerRegistry makeStateChangeHandlerRegistry(
        GeneralSupplierAcknowledgedHandler generalSupplierAcknowledgedHandler
    ) {
        StateChangeHandlerRegistry stateChangeHandlerRegistry = new StateChangeHandlerRegistry();

        stateChangeHandlerRegistry.put(EnquiryType.ASSIGNED, generalSupplierAcknowledgedHandler);
        stateChangeHandlerRegistry.put(EnquiryType.SPOT, generalSupplierAcknowledgedHandler);
        stateChangeHandlerRegistry.put(EnquiryType.FORWARDED, generalSupplierAcknowledgedHandler);
        return stateChangeHandlerRegistry;
    }

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        EnquiryType enquiryType = offerUpdatedEvent.getEventMessage().getOffer().getEnquiryType();

        StateChangeHandler<OfferState, OfferUpdatedEvent> stateChangeHandler = stateChangeHandlerRegistry.get(
            enquiryType
        );

        return stateChangeHandler.handle(stateChange, offerUpdatedEvent);
    }
}
