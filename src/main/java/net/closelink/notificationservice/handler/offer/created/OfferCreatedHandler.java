package net.closelink.notificationservice.handler.offer.created;

import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.offer.OfferUpdatedEventMessage;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OfferCreated<PERSON>and<PERSON> implements EventHandler {

    private final OfferUpdatedEventHandlerRegistry offerUpdatedEventHandlerRegistry;

    @Autowired
    public OfferCreatedHandler(
        SpotOfferCreatedHandler spotOfferCreatedHandler,
        AssignedOfferCreated<PERSON>andler assignedOfferCreated<PERSON><PERSON><PERSON>,
        ForwardedOfferCreatedHandler forwardedOfferCreatedHandler
    ) {
        super();
        this.offerUpdatedEventHandlerRegistry = makeStateChangeHandlerRegistry(
            spotOfferCreatedHandler,
            assignedOfferCreatedHandler,
            forwardedOfferCreatedHandler
        );
    }

    private static OfferUpdatedEventHandlerRegistry makeStateChangeHandlerRegistry(
        SpotOfferCreatedHandler spotOfferCreatedHandler,
        AssignedOfferCreatedHandler assignedOfferCreatedHandler,
        ForwardedOfferCreatedHandler forwardedOfferCreatedHandler
    ) {
        OfferUpdatedEventHandlerRegistry registry = new OfferUpdatedEventHandlerRegistry();

        registry.put(EnquiryType.ASSIGNED, assignedOfferCreatedHandler);
        registry.put(EnquiryType.SPOT, spotOfferCreatedHandler);
        registry.put(EnquiryType.FORWARDED, forwardedOfferCreatedHandler);
        return registry;
    }

    @Override
    public void handle(final Event event) {
        final OfferUpdatedEventMessage eventMessage = createEventMessage(event);
        final OfferUpdatedEvent offerUpdatedEvent = createOrderUpdatedEvent(event, eventMessage);

        OrderType type = eventMessage.getOffer().getType();
        if (type != OrderType.LUBES) {
            log.info("Offer does not have type LUBES but {}, exiting early", type);
            return;
        }

        EnquiryType enquiryType = offerUpdatedEvent.getEventMessage().getOffer().getEnquiryType();
        OfferUpdatedEventHandler handler = offerUpdatedEventHandlerRegistry.get(enquiryType);

        handler.handle(offerUpdatedEvent);
    }

    private OfferUpdatedEvent createOrderUpdatedEvent(final Event event, final OfferUpdatedEventMessage eventMessage) {
        return OfferUpdatedEvent.builder().event(event).eventMessage(eventMessage).build();
    }

    private OfferUpdatedEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), OfferUpdatedEventMessage.class);
    }
}
