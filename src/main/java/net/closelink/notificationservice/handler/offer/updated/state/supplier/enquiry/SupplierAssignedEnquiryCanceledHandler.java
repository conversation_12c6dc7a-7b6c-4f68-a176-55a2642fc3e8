package net.closelink.notificationservice.handler.offer.updated.state.supplier.enquiry;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class SupplierAssignedEnquiryCanceledHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private SupplierStateHandlerSupport supplierStateHandlerSupport;

    private static final String MESSAGE = "supplierEnquiryCancelled.message";
    private static final String SUBJECT = "supplierEnquiryCancelled.subject";
    private static final String SUBJECT_BUYER_REF = "supplierEnquiryCancelledBuyerRef.subject";
    private static final String TEMPLATE = "Order_Customer_Enquiry_Rejected";

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        final Offer offer = offerUpdatedEvent.getEventMessage().getOffer();

        final Notification.NotificationBuilder builder =
            this.supplierStateHandlerSupport.createNotificationBuilder(offerUpdatedEvent);

        Notification notification = builder
            .message(MESSAGE)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .template(TEMPLATE)
            .messageReplacements(this.supplierStateHandlerSupport.createCancelMessageReplacements(offer))
            .build();

        this.supplierStateHandlerSupport.notify(notification);

        return offerUpdatedEvent;
    }
}
