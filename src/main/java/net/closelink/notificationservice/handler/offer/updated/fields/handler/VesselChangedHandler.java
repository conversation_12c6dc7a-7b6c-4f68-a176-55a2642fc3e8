package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class VesselChangedHandler implements OfferUpdatedEventTypeHandler {

    private static final String ACTIVITYLOG = "activitylog.vessel";
    private final BaseActivityLogService activityLogService;
    private final VesselApiRestService ApiRestService;

    @Autowired
    public VesselChangedHandler(
        final BaseActivityLogService activityLogService,
        final VesselApiRestService ApiRestService
    ) {
        this.activityLogService = activityLogService;
        this.ApiRestService = ApiRestService;
    }

    @Override
    public void handle(final OfferUpdatedEvent orderUpdatedEvent) {
        final String newValue = EncodeSupport.encodeString(orderUpdatedEvent.getEventMessage().getNewValue());
        final String oldValue = EncodeSupport.encodeString(orderUpdatedEvent.getEventMessage().getOldValue());

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                ACTIVITYLOG,
                new Object[] { findVessel(oldValue), findVessel(newValue) }
            );
    }

    private String findVessel(final String vesselId) {
        if (vesselId == null) {
            return null;
        }

        return this.ApiRestService.getVessel(vesselId).getName();
    }
}
