package net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AssignedEnquiryOrderHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE = "enquiryOrder.message";
    private static final String SUBJECT = "enquiryOrder.subject";
    private static final String SUBJECT_BUYER_REF = "enquiryOrderBuyerRef.subject";
    private static final String TEMPLATE = "Order_Supplier_Enquiry_To_Order";

    private final CustomerStateHandlerSupport customerStateHandlerSupport;

    @Autowired
    public AssignedEnquiryOrderHandler(final CustomerStateHandlerSupport customerStateHandlerSupport) {
        this.customerStateHandlerSupport = customerStateHandlerSupport;
    }

    @Override
    public OfferUpdatedEvent handle(final StateChange<OfferState> stateChange, final OfferUpdatedEvent event) {
        final Notification.NotificationBuilder builder =
            this.customerStateHandlerSupport.createNotificationBuilder(event);

        final Notification notification = builder
            .message(MESSAGE)
            .subject(
                Strings.isNullOrEmpty(event.getEventMessage().getOffer().getBuyerReference())
                    ? SUBJECT
                    : SUBJECT_BUYER_REF
            )
            .template(TEMPLATE)
            .build();

        this.customerStateHandlerSupport.notify(notification);
        return event;
    }
}
