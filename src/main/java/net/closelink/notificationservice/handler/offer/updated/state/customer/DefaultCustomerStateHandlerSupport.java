package net.closelink.notificationservice.handler.offer.updated.state.customer;

import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.replacement.offer.OfferReplacementService;
import net.closelink.notificationservice.replacement.offer.cancel.OfferReplacementSupportService;
import net.closelink.notificationservice.replacement.offer.mapper.OfferReplacementOfferMapper;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.support.RecipientsSupportService;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class DefaultCustomerStateHandlerSupport implements CustomerStateHandlerSupport {

    private final NotificationService notificationService;
    private final VesselApiRestService vesselApiRestService;
    private final CustomerApiRestService customerApiRestService;
    private final OfferReplacementService offerReplacementService;
    private final OfferReplacementSupportService offerReplacementSupportService;
    private final WebAppReplacementService webappReplacementService;
    private final RecipientsSupportService recipientsSupportService;

    public static final String FORWARDED_SENDER_MAIL = "<EMAIL>";

    @Override
    public Notification.NotificationBuilder createNotificationBuilder(final OfferUpdatedEvent event) {
        final Offer offer = event.getEventMessage().getOffer();

        final var recipientInfo = recipientsSupportService.buildSupplierRecipientInfo(offer);

        var isForwardedOffer = EnquiryType.FORWARDED.equals(offer.getEnquiryType());
        return setupSenderProperties(Notification.builder(), event)
            .customSenderEmailAddress(isForwardedOffer ? FORWARDED_SENDER_MAIL : null)
            .customSenderName(isForwardedOffer ? getCustomerName(offer) : null)
            .sendMail(true)
            .offerId(offer.getId())
            .receiverType(recipientInfo.getReceiverType())
            .recipients(recipientInfo.getRecipients())
            .receiverId(offer.getSupplierId())
            .mailReplacements(createReplacements(offer))
            .subjectReplacements(createSubjectReplacements(offer))
            .category(NotificationCategory.OFFER_STATE_CHANGE);
    }

    private Notification.NotificationBuilder setupSenderProperties(
        Notification.NotificationBuilder builder,
        OfferUpdatedEvent event
    ) {
        var companyType = event.getEvent().getEventTrigger().getCompanyType();

        if (UserType.SYSTEM.equals(companyType)) {
            builder.senderType(SenderType.SYSTEM);
            return builder;
        }

        String userId = event.getEvent().getEventTrigger().getUserId();
        if (userId != null) {
            builder.senderType(SenderType.USER).senderId(userId);
            return builder;
        }

        builder.senderId(event.getEvent().getEventTrigger().getCompanyId());

        switch (event.getEvent().getEventTrigger().getCompanyType()) {
            case CUSTOMER -> builder.senderType(SenderType.CUSTOMER);
            case SUPPLIER -> builder.senderType(SenderType.SUPPLIER);
        }

        return builder;
    }

    @Override
    public void notify(final Notification notification) {
        this.notificationService.notify(notification);
    }

    private Object[] createSubjectReplacements(final Offer offer) {
        return new Object[] {
            getVesselName(offer.getVesselId()),
            offer.getOfferNumber(),
            getCustomerName(offer),
            offer.getBuyerReference(),
        };
    }

    private String getVesselName(final String vesselId) {
        return this.vesselApiRestService.getVessel(vesselId).getName();
    }

    private String getCustomerName(final Offer offer) {
        return this.customerApiRestService.getCustomer(offer.getCustomerId()).getName();
    }

    private Map<String, String> createReplacements(final Offer offer) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.putAll(
            this.webappReplacementService.create(OfferReplacementOfferMapper.create(offer), ReceiverType.SUPPLIER)
        );
        replacements.putAll(this.offerReplacementService.create(OfferReplacementOfferMapper.create(offer)));
        return replacements;
    }

    @Override
    public String[] createCancelMessageReplacements(final Offer offer) {
        return new String[] {
            this.offerReplacementSupportService.createCancelReasonReplacement(offer.getCancelReason()),
        };
    }
}
