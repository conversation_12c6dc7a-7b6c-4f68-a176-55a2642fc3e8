package net.closelink.notificationservice.handler.offer.updated.state.customer.acknowledge;

import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.supplier.ForwardedOrderEmailNotificationCreator;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ForwardedAcknowledgedOrderHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE = "acknowledgedOrder.message";
    private final CustomerStateHandlerSupport customerStateHandlerSupport;
    private final ForwardedOrderEmailNotificationCreator forwardedOrderEmailNotificationCreator;

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        Offer offer = offerUpdatedEvent.getEventMessage().getOffer();

        Notification.NotificationBuilder notificationBuilder = customerStateHandlerSupport.createNotificationBuilder(
            offerUpdatedEvent
        );
        notificationBuilder.message(MESSAGE);

        Notification notification = forwardedOrderEmailNotificationCreator.createNotification(
            notificationBuilder,
            offer
        );

        customerStateHandlerSupport.notify(notification);

        return offerUpdatedEvent;
    }
}
