package net.closelink.notificationservice.handler.offer.updated.state.supplier.all;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GeneralSupplierCanceledHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE = "supplierCancelled.message";
    private static final String SUBJECT = "supplierCancelled.subject";
    private static final String SUBJECT_BUYER_REF = "supplierCancelledBuyerRef.subject";
    private static final String TEMPLATE = "Order_Customer_Order_Rejected";

    private final SupplierStateHandlerSupport supplierStateHandlerSupport;

    @Autowired
    public GeneralSupplierCanceledHandler(final SupplierStateHandlerSupport supplierStateHandlerSupport) {
        this.supplierStateHandlerSupport = supplierStateHandlerSupport;
    }

    @Override
    public OfferUpdatedEvent handle(final StateChange<OfferState> stateChange, final OfferUpdatedEvent event) {
        final Offer offer = event.getEventMessage().getOffer();

        final Notification.NotificationBuilder builder =
            this.supplierStateHandlerSupport.createNotificationBuilder(event);
        final Notification notification = builder
            .message(MESSAGE)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .template(TEMPLATE)
            .messageReplacements(this.supplierStateHandlerSupport.createCancelMessageReplacements(offer))
            .build();

        this.supplierStateHandlerSupport.notify(notification);
        return event;
    }
}
