package net.closelink.notificationservice.handler.offer.updated.fields.activity.supplier;

import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.replacement.subject.supplier.SupplierSubjectReplacementService;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class AssignedEnquiryEmailNotificationCreator implements EmailNotificationCreator {

    private static final String SUPPLIER_ENQUIRY_SUBJECT = "activitylog.subject.supplier.enquiry";
    private static final String SUPPLIER_ENQUIRY_SUBJECT_BUYER_REF = "activitylog.subject.supplier.enquiryBuyerRef";
    private static final String SUPPLIER_ENQUIRY_TEMPLATE = "Order_Supplier_Enquiry_Customer_Changes";

    private final SupplierSubjectReplacementService supplierSubjectReplacementService;

    @Override
    public Notification createNotification(Notification.NotificationBuilder notificationBuilder, Offer offer) {
        return notificationBuilder
            .subject(
                Strings.isNullOrEmpty(offer.getBuyerReference())
                    ? SUPPLIER_ENQUIRY_SUBJECT
                    : SUPPLIER_ENQUIRY_SUBJECT_BUYER_REF
            )
            .subjectReplacements(this.supplierSubjectReplacementService.createSubjectReplacements(offer))
            .template(SUPPLIER_ENQUIRY_TEMPLATE)
            .groupMail(true)
            .sendMail(true)
            .build();
    }
}
