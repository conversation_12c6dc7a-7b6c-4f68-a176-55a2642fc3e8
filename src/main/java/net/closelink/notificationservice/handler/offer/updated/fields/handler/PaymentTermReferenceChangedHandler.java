package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.service.rest.coredata.CoreDataService;
import net.closelink.notificationservice.service.rest.coredata.EnumMessage;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class PaymentTermReferenceChangedHandler implements OfferUpdatedEventTypeHandler {

    private static final String ACTIVITYLOG = "activitylog.paymenttermreferencedays";
    private static final String ACTIVITYLOG_NA = "activitylog.na";

    private final BaseActivityLogService activityLogService;
    private final CoreDataService coreDataService;
    private final MessageSource messageSource;

    @Override
    public void handle(OfferUpdatedEvent orderUpdatedEvent) {
        var oldValue = EncodeSupport.encodeOffer(orderUpdatedEvent.getEventMessage().getOldValue());
        var newValue = EncodeSupport.encodeOffer(orderUpdatedEvent.getEventMessage().getNewValue());

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                ACTIVITYLOG,
                translatePaymentDays(oldValue, newValue)
            );
    }

    private String[] translatePaymentDays(Offer oldValue, Offer newValue) {
        var humanReadableValues = coreDataService.getHumanReadableValueForPaymentTermReference();
        var oldReference = oldValue.getPaymentTermReference();
        var newReference = newValue.getPaymentTermReference();
        var oldReferenceDays = oldValue.getPaymentTermReferenceDays();
        var newReferenceDays = newValue.getPaymentTermReferenceDays();

        if (oldReference == null) {
            return new String[] {
                messageSource.getMessage(ACTIVITYLOG_NA, null, Locale.ENGLISH),
                determineReferenceString(humanReadableValues, newReferenceDays, newReference),
            };
        }

        return new String[] {
            determineReferenceString(humanReadableValues, oldReferenceDays, oldReference),
            determineReferenceString(humanReadableValues, newReferenceDays, newReference),
        };
    }

    private String determineReferenceString(
        Map<String, EnumMessage.DetailEnumMessage> humanReadableValues,
        Long referenceDays,
        String reference
    ) {
        if ("PREPAYMENT".equals(reference)) {
            return humanReadableValues.get(reference).getHumanReadableValue();
        }
        return String.format("%s %s", referenceDays, getHumanReadableValue(humanReadableValues, reference));
    }

    private String getHumanReadableValue(Map<String, EnumMessage.DetailEnumMessage> humanReadableValues, String key) {
        return Optional.ofNullable(humanReadableValues.get(key))
            .map(EnumMessage.DetailEnumMessage::getHumanReadableValue)
            .orElseGet(() -> messageSource.getMessage(ACTIVITYLOG_NA, null, Locale.ENGLISH));
    }
}
