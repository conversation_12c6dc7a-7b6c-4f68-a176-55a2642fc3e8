package net.closelink.notificationservice.handler.offer.updated.state.customer.enquirydeclined;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AssignedEnquiryDeclinedEnquiryHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE = "enquiryDeclinedEnquiry.message";
    private static final String SUBJECT = "enquiryDeclinedEnquiry.subject";
    private static final String SUBJECT_BUYER_REF = "enquiryDeclinedEnquiryBuyerRef.subject";
    private static final String TEMPLATE = "Order_Supplier_Enquiry_Customer_Changes";

    private final CustomerStateHandlerSupport customerStateHandlerSupport;

    @Autowired
    public AssignedEnquiryDeclinedEnquiryHandler(final CustomerStateHandlerSupport customerStateHandlerSupport) {
        this.customerStateHandlerSupport = customerStateHandlerSupport;
    }

    @Override
    public OfferUpdatedEvent handle(final StateChange<OfferState> stateChange, final OfferUpdatedEvent event) {
        final Notification.NotificationBuilder builder =
            this.customerStateHandlerSupport.createNotificationBuilder(event);

        Offer offer = event.getEventMessage().getOffer();

        final Notification notification = builder
            .message(MESSAGE)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .template(TEMPLATE)
            .build();

        this.customerStateHandlerSupport.notify(notification);
        return event;
    }
}
