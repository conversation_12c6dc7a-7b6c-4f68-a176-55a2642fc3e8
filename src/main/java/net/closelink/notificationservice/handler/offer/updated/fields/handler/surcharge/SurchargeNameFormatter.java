package net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge;

import java.util.HashMap;
import java.util.Optional;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Surcharge;
import net.closelink.notificationservice.service.rest.coredata.CoreDataService;
import net.closelink.notificationservice.service.rest.coredata.EnumMessage;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class SurchargeNameFormatter {

    private final CoreDataService coreDataService;

    public String getDisplayName(Surcharge surcharge) {
        if (surcharge.getSurchargeType().equals("CUSTOM")) {
            return surcharge.getName();
        }

        HashMap<String, EnumMessage.DetailEnumMessage> surchargeTypeEnumMap =
            coreDataService.getHumanReadableValueForSurchargeTypeMessage();

        return extractHumanReadableSurchargeType(surchargeTypeEnumMap, surcharge);
    }

    private String extractHumanReadableSurchargeType(
        HashMap<String, EnumMessage.DetailEnumMessage> surchargeTypeEnumMap,
        Surcharge surcharge
    ) {
        EnumMessage.DetailEnumMessage detailEnumMessage = surchargeTypeEnumMap.get(surcharge.getSurchargeType());
        return Optional.ofNullable(detailEnumMessage)
            .map(EnumMessage.DetailEnumMessage::getHumanReadableValue)
            .orElse(null);
    }
}
