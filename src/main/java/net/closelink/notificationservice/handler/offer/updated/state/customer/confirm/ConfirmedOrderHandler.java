package net.closelink.notificationservice.handler.offer.updated.state.customer.confirm;

import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.customer.StateChangeHandlerRegistry;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ConfirmedOrderHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private StateChangeHandlerRegistry stateChangeHandlerRegistry;

    @Autowired
    public ConfirmedOrderHandler(
        GeneralConfirmedOrderHandler generalConfirmedOrderHandler,
        ForwardedConfirmedOrderHandler forwardedConfirmedOrderHandler
    ) {
        this.stateChangeHandlerRegistry = makeStateChangeHandlerRegistry(
            generalConfirmedOrderHandler,
            forwardedConfirmedOrderHandler
        );
    }

    private static StateChangeHandlerRegistry makeStateChangeHandlerRegistry(
        GeneralConfirmedOrderHandler generalConfirmedOrderHandler,
        ForwardedConfirmedOrderHandler forwardedConfirmedOrderHandler
    ) {
        StateChangeHandlerRegistry stateChangeHandlerRegistry = new StateChangeHandlerRegistry();

        stateChangeHandlerRegistry.put(EnquiryType.ASSIGNED, generalConfirmedOrderHandler);
        stateChangeHandlerRegistry.put(EnquiryType.SPOT, generalConfirmedOrderHandler);
        stateChangeHandlerRegistry.put(EnquiryType.FORWARDED, forwardedConfirmedOrderHandler);
        return stateChangeHandlerRegistry;
    }

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        EnquiryType enquiryType = offerUpdatedEvent.getEventMessage().getOffer().getEnquiryType();

        StateChangeHandler<OfferState, OfferUpdatedEvent> stateChangeHandler = stateChangeHandlerRegistry.get(
            enquiryType
        );

        return stateChangeHandler.handle(stateChange, offerUpdatedEvent);
    }
}
