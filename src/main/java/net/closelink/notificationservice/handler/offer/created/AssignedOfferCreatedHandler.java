package net.closelink.notificationservice.handler.offer.created;

import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class AssignedOfferCreatedHandler implements OfferUpdatedEventHandler {

    private static final String OFFER_MESSAGE = "draftEnquiry.message";
    private static final String OFFER_SUBJECT = "draftEnquiry.subject";
    private static final String OFFER_SUBJECT_BUYER_REF = "draftEnquiryBuyerRef.subject";
    private static final String OFFER_TEMPLATE = "Order_Supplier_New_Enquiry";

    private final CustomerStateHandlerSupport customerStateHandlerSupport;

    @Override
    public void handle(OfferUpdatedEvent offerUpdatedEvent) {
        Notification notification = createEnquiryNotification(offerUpdatedEvent);

        customerStateHandlerSupport.notify(notification);
    }

    private Notification createEnquiryNotification(final OfferUpdatedEvent event) {
        final Notification.NotificationBuilder builder =
            this.customerStateHandlerSupport.createNotificationBuilder(event);

        Offer offer = event.getEventMessage().getOffer();

        return builder
            .message(OFFER_MESSAGE)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? OFFER_SUBJECT : OFFER_SUBJECT_BUYER_REF)
            .template(OFFER_TEMPLATE)
            .build();
    }
}
