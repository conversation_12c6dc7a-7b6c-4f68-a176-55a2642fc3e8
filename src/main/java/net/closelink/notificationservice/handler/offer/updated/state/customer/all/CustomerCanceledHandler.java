package net.closelink.notificationservice.handler.offer.updated.state.customer.all;

import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.customer.StateChangeHandlerRegistry;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CustomerCanceledHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private StateChangeHandlerRegistry stateChangeHandlerRegistry;

    @Autowired
    public CustomerCanceledHandler(
        GeneralCustomerCanceledHandler generalCustomerCanceledHandler,
        ForwardedCustomerCanceledHandler forwardedCustomerCanceledHandler
    ) {
        StateChangeHandlerRegistry stateChangeHandlerRegistry = new StateChangeHandlerRegistry();

        stateChangeHandlerRegistry.put(EnquiryType.ASSIGNED, generalCustomerCanceledHandler);
        stateChangeHandlerRegistry.put(EnquiryType.SPOT, generalCustomerCanceledHandler);
        stateChangeHandlerRegistry.put(EnquiryType.FORWARDED, forwardedCustomerCanceledHandler);

        this.stateChangeHandlerRegistry = stateChangeHandlerRegistry;
    }

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        EnquiryType enquiryType = offerUpdatedEvent.getEventMessage().getOffer().getEnquiryType();
        StateChangeHandler<OfferState, OfferUpdatedEvent> handler = this.stateChangeHandlerRegistry.get(enquiryType);

        return handler.handle(stateChange, offerUpdatedEvent);
    }
}
