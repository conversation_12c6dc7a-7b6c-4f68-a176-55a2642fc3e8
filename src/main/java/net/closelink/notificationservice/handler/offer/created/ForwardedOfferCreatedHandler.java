package net.closelink.notificationservice.handler.offer.created;

import com.google.common.base.Strings;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.domainobject.Attachment;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.replacement.subject.supplier.SupplierSubjectReplacementService;
import net.closelink.notificationservice.service.attachment.ForwardedEnquiryAttachmentCreator;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ForwardedOfferCreatedHandler implements OfferUpdatedEventHandler {

    private static final String MESSAGE = "draftEnquiry.message";
    private static final String SUBJECT = "forwarded.newEnquiry.subject";
    private static final String SUBJECT_BUYER_REF = "forwarded.newEnquiry.subjectBuyerRef";
    private static final String TEMPLATE = "Supplier_New_Enquiry_Forwarded";

    private CustomerStateHandlerSupport customerStateHandlerSupport;
    private SupplierSubjectReplacementService subjectReplacementService;
    private ForwardedEnquiryAttachmentCreator forwardedEnquiryAttachmentCreator;

    @Override
    public void handle(OfferUpdatedEvent offerUpdatedEvent) {
        Notification notification = createNotification(offerUpdatedEvent);

        customerStateHandlerSupport.notify(notification);
    }

    private Notification createNotification(OfferUpdatedEvent offerUpdatedEvent) {
        Offer offer = offerUpdatedEvent.getEventMessage().getOffer();

        final Notification.NotificationBuilder builder =
            this.customerStateHandlerSupport.createNotificationBuilder(offerUpdatedEvent);

        builder
            .message(MESSAGE)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .subjectReplacements(subjectReplacementService.createSubjectReplacements(offer))
            .template(TEMPLATE)
            .attachments(forwardedEnquiryAttachmentCreator.createAttachmentForOffer(offer));

        return builder.build();
    }
}
