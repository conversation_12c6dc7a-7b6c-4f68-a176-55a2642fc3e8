package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class NoticeDaysChangedHandler implements OfferUpdatedEventTypeHandler {

    private static final String ACTIVITYLOG_NOTICEDAYS = "activitylog.noticedays";

    private final BaseActivityLogService activityLogService;

    @Autowired
    public NoticeDaysChangedHandler(final BaseActivityLogService activityLogService) {
        this.activityLogService = activityLogService;
    }

    @Override
    public void handle(final OfferUpdatedEvent orderUpdatedEvent) {
        final Long newValue = EncodeSupport.encodeLong(orderUpdatedEvent.getEventMessage().getNewValue());
        final Long oldValue = EncodeSupport.encodeLong(orderUpdatedEvent.getEventMessage().getOldValue());

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                ACTIVITYLOG_NOTICEDAYS,
                new Object[] { oldValue, newValue }
            );
    }
}
