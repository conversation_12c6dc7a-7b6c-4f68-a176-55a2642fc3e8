package net.closelink.notificationservice.handler.offer.updated.fields.activity.supplier;

import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.replacement.subject.supplier.SupplierSubjectReplacementService;
import net.closelink.notificationservice.service.attachment.ForwardedEnquiryAttachmentCreator;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ForwardedEnquiryEmailNotificationCreator implements EmailNotificationCreator {

    private static final String SUPPLIER_FORWARDED_ENQUIRY_SUBJECT = "activitylog.subject.forwarded.supplier.enquiry";
    private static final String SUPPLIER_FORWARDED_ENQUIRY_SUBJECT_BUYER_REF =
        "activitylog.subjectBuyerRef.forwarded.supplier.enquiry";
    private static final String SUPPLIER_FORWARDED_ENQUIRY_TEMPLATE = "Supplier_Enquiry_Forwarded_Customer_Changes";

    private SupplierSubjectReplacementService supplierSubjectReplacementService;
    private ForwardedEnquiryAttachmentCreator forwardedEnquiryAttachmentCreator;

    @Override
    public Notification createNotification(Notification.NotificationBuilder notificationBuilder, Offer offer) {
        return notificationBuilder
            .subject(
                Strings.isNullOrEmpty(offer.getBuyerReference())
                    ? SUPPLIER_FORWARDED_ENQUIRY_SUBJECT
                    : SUPPLIER_FORWARDED_ENQUIRY_SUBJECT_BUYER_REF
            )
            .subjectReplacements(supplierSubjectReplacementService.createSubjectReplacements(offer))
            .template(SUPPLIER_FORWARDED_ENQUIRY_TEMPLATE)
            .attachments(forwardedEnquiryAttachmentCreator.createAttachmentForOffer(offer))
            .groupMail(true)
            .sendMail(true)
            .build();
    }
}
