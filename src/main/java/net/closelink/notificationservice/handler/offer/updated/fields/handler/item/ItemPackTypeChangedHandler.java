package net.closelink.notificationservice.handler.offer.updated.fields.handler.item;

import java.util.Locale;
import java.util.Optional;
import net.closelink.cenqueue.objects.Item;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.service.rest.product.ProductApiRestService;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

@Service
public class ItemPackTypeChangedHandler implements OfferUpdatedEventTypeHandler {

    private static final String ACTIVITYLOG = "activitylog.itempacktype";

    private final BaseActivityLogService activityLogService;
    private final ProductApiRestService ApiRestService;
    private final MessageSource messageSource;

    @Autowired
    public ItemPackTypeChangedHandler(
        final BaseActivityLogService activityLogService,
        final ProductApiRestService ApiRestService,
        final MessageSource messageSource
    ) {
        this.activityLogService = activityLogService;
        this.ApiRestService = ApiRestService;
        this.messageSource = messageSource;
    }

    @Override
    public void handle(final OfferUpdatedEvent orderUpdatedEvent) {
        final Item newValue = EncodeSupport.encodeItem(orderUpdatedEvent.getEventMessage().getNewValue());
        final Item oldValue = EncodeSupport.encodeItem(orderUpdatedEvent.getEventMessage().getOldValue());

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                ACTIVITYLOG,
                new Object[] {
                    findProductName(newValue.getProductId()),
                    translatePackType(oldValue.getPackType()),
                    translatePackType(newValue.getPackType()),
                }
            );
    }

    private String findProductName(final String productId) {
        return Optional.ofNullable(productId).map(it -> this.ApiRestService.getProduct(it).getName()).orElse(null);
    }

    private String translatePackType(final String packType) {
        return Optional.ofNullable(packType)
            .map(it -> this.messageSource.getMessage(it, null, Locale.ENGLISH))
            .orElse(null);
    }
}
