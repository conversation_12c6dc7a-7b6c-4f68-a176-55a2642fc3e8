package net.closelink.notificationservice.handler.offer.updated.state.customer.delivered;

import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.customer.StateChangeHandlerRegistry;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DeliveredOrderHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private StateChangeHandlerRegistry stateChangeHandlerRegistry;

    @Autowired
    public DeliveredOrderHandler(
        GeneralDeliveredOrderHandler generalDeliveredOrderHandler,
        ForwardedDeliveredOrderHandler forwardedDeliveredOrderHandler
    ) {
        this.stateChangeHandlerRegistry = makeStateChangeHandlerRegistry(
            generalDeliveredOrderHandler,
            forwardedDeliveredOrderHandler
        );
    }

    private static StateChangeHandlerRegistry makeStateChangeHandlerRegistry(
        GeneralDeliveredOrderHandler generalDeliveredOrderHandler,
        ForwardedDeliveredOrderHandler forwardedDeliveredOrderHandler
    ) {
        StateChangeHandlerRegistry stateChangeHandlerRegistry = new StateChangeHandlerRegistry();

        stateChangeHandlerRegistry.put(EnquiryType.ASSIGNED, generalDeliveredOrderHandler);
        stateChangeHandlerRegistry.put(EnquiryType.SPOT, generalDeliveredOrderHandler);
        stateChangeHandlerRegistry.put(EnquiryType.FORWARDED, forwardedDeliveredOrderHandler);
        return stateChangeHandlerRegistry;
    }

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        EnquiryType enquiryType = offerUpdatedEvent.getEventMessage().getOffer().getEnquiryType();

        StateChangeHandler<OfferState, OfferUpdatedEvent> stateChangeHandler = stateChangeHandlerRegistry.get(
            enquiryType
        );

        return stateChangeHandler.handle(stateChange, offerUpdatedEvent);
    }
}
