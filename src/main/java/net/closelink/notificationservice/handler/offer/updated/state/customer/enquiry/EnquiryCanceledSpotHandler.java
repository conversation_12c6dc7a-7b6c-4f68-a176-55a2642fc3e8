package net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class EnquiryCanceledSpotHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String ORDER_QUOTE_MESSAGE = "quoteQuoteCancelled.message";
    private static final String ORDER_QUOTE_SUBJECT = "quoteQuoteCancelled.subject";
    private static final String ORDER_QUOTE_SUBJECT_BUYER_REF = "quoteQuoteCancelledBuyerRef.subject";
    private static final String ORDER_QUOTE_TEMPLATE = "Order_Supplier_Quote_Canceled_Trade";

    private static final String ORDER_ENQUIRY_MESSAGE = "enquiryEnquiryCancelled.message";
    private static final String ORDER_ENQUIRY_SUBJECT = "enquiryEnquiryCancelled.subject";
    private static final String ORDER_ENQUIRY_SUBJECT_BUYER_REF = "enquiryEnquiryCancelledBuyerRef.subject";
    private static final String ORDER_ENQUIRY_TEMPLATE = "Order_Supplier_Enquiry_Canceled_Trade";

    private final CustomerStateHandlerSupport customerStateHandlerSupport;

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        Notification notification = createOrderCancelNotification(offerUpdatedEvent);

        this.customerStateHandlerSupport.notify(notification);

        return offerUpdatedEvent;
    }

    private Notification createOrderCancelNotification(final OfferUpdatedEvent event) {
        final Offer offer = event.getEventMessage().getOffer();
        if (offer.getDateQuoted() != null) {
            return createOrderQuoteCanceledNotification(event);
        } else {
            return createOrderEnquiryCanceledNotification(event);
        }
    }

    private Notification createOrderEnquiryCanceledNotification(final OfferUpdatedEvent event) {
        final Offer offer = event.getEventMessage().getOffer();
        final Notification.NotificationBuilder builder =
            this.customerStateHandlerSupport.createNotificationBuilder(event);
        return builder
            .message(ORDER_ENQUIRY_MESSAGE)
            .subject(
                Strings.isNullOrEmpty(offer.getBuyerReference())
                    ? ORDER_ENQUIRY_SUBJECT
                    : ORDER_ENQUIRY_SUBJECT_BUYER_REF
            )
            .template(ORDER_ENQUIRY_TEMPLATE)
            .messageReplacements(this.customerStateHandlerSupport.createCancelMessageReplacements(offer))
            .build();
    }

    private Notification createOrderQuoteCanceledNotification(final OfferUpdatedEvent event) {
        final Offer offer = event.getEventMessage().getOffer();
        final Notification.NotificationBuilder builder =
            this.customerStateHandlerSupport.createNotificationBuilder(event);
        return builder
            .message(ORDER_QUOTE_MESSAGE)
            .subject(
                Strings.isNullOrEmpty(offer.getBuyerReference()) ? ORDER_QUOTE_SUBJECT : ORDER_QUOTE_SUBJECT_BUYER_REF
            )
            .template(ORDER_QUOTE_TEMPLATE)
            .messageReplacements(this.customerStateHandlerSupport.createCancelMessageReplacements(offer))
            .build();
    }
}
