package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.service.rest.port.PortApiRestService;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PortChangedHandler implements OfferUpdatedEventTypeHandler {

    private static final String ACTIVITYLOG = "activitylog.port";
    private final BaseActivityLogService activityLogService;
    private final PortApiRestService ApiRestService;

    @Autowired
    public PortChangedHandler(
        final BaseActivityLogService activityLogService,
        final PortApiRestService ApiRestService
    ) {
        this.activityLogService = activityLogService;
        this.ApiRestService = ApiRestService;
    }

    @Override
    public void handle(final OfferUpdatedEvent orderUpdatedEvent) {
        final String newValue = EncodeSupport.encodeString(orderUpdatedEvent.getEventMessage().getNewValue());
        final String oldValue = EncodeSupport.encodeString(orderUpdatedEvent.getEventMessage().getOldValue());

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                ACTIVITYLOG,
                new Object[] { findPort(oldValue), findPort(newValue) }
            );
    }

    private String findPort(final String portId) {
        if (portId == null) {
            return null;
        }

        return this.ApiRestService.getPort(portId).getName();
    }
}
