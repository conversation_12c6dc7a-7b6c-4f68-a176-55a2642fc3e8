package net.closelink.notificationservice.handler.offer.updated.state;

import de.tschumacher.simplestatemachine.configuration.SimpleStateMachineConfig;
import de.tschumacher.simplestatemachine.exception.TransitionNotAllowedException;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.exception.NoHandlerFoundException;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OfferUpdatedStateChangedHandler implements OfferUpdatedEventTypeHandler {

    private final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> customerStateMachineConfig;
    private final SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> supplierStateMachineConfig;
    private SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> systemStateMachineConfig;

    @Autowired
    public OfferUpdatedStateChangedHandler(
        @Qualifier("customerStateMachine") final SimpleStateMachineConfig<
            OfferState,
            OfferUpdatedEvent
        > customerStateMachineConfig,
        @Qualifier("supplierStateMachine") final SimpleStateMachineConfig<
            OfferState,
            OfferUpdatedEvent
        > supplierStateMachineConfig,
        @Qualifier("systemStateMachine") final SimpleStateMachineConfig<
            OfferState,
            OfferUpdatedEvent
        > systemStateMachineConfig
    ) {
        super();
        this.customerStateMachineConfig = customerStateMachineConfig;
        this.supplierStateMachineConfig = supplierStateMachineConfig;
        this.systemStateMachineConfig = systemStateMachineConfig;
    }

    @Override
    public void handle(final OfferUpdatedEvent orderUpdatedEvent) {
        final OfferState oldState = toOrderState(orderUpdatedEvent.getEventMessage().getOldValue());
        final OfferState newState = toOrderState(orderUpdatedEvent.getEventMessage().getNewValue());
        final UserType companyType = orderUpdatedEvent.getEvent().getEventTrigger().getCompanyType();

        performStateChange(orderUpdatedEvent, oldState, newState, companyType);
    }

    private void performStateChange(
        final OfferUpdatedEvent orderUpdatedEvent,
        final OfferState oldState,
        final OfferState newState,
        final UserType companyType
    ) {
        try {
            findStateMachineConifg(companyType).createMachine(oldState).change(newState, orderUpdatedEvent);
        } catch (final TransitionNotAllowedException e) {
            log.warn("No state handler found for oldState ({}) to newState ({})", oldState, newState);
        } catch (final NoHandlerFoundException e) {
            log.warn(e.getMessage());
        }
    }

    private SimpleStateMachineConfig<OfferState, OfferUpdatedEvent> findStateMachineConifg(final UserType userType) {
        if (UserType.CUSTOMER.equals(userType)) {
            return this.customerStateMachineConfig;
        } else if (UserType.SUPPLIER.equals(userType)) {
            return this.supplierStateMachineConfig;
        } else if (UserType.SYSTEM.equals(userType)) {
            return this.systemStateMachineConfig;
        }
        throw new NoHandlerFoundException("No state handler found for userType: " + userType);
    }

    private OfferState toOrderState(final String value) {
        return EncodeSupport.encodeOfferState(value);
    }
}
