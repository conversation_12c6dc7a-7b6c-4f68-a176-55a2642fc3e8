package net.closelink.notificationservice.handler.offer.message;

import com.google.common.base.Strings;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.offer.OfferMessageEventMessage;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.cenqueue.types.OrderState;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.mapper.EnumMapper;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.replacement.offer.OfferReplacementService;
import net.closelink.notificationservice.replacement.offer.mapper.OfferReplacementOfferMapper;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.notification.NotificationPreferenceService;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.message.MessageApiRestService;
import net.closelink.notificationservice.service.rest.message.domain.MessageMessage;
import net.closelink.notificationservice.service.rest.message.domain.MessageReceiverType;
import net.closelink.notificationservice.service.rest.message.domain.RecipientMessage;
import net.closelink.notificationservice.service.rest.order.OrderApiRestService;
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import net.closelink.notificationservice.support.RecipientsCreatorKt;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@AllArgsConstructor
public class OfferMessageHandler implements EventHandler {

    private static final Locale LOCALE = Locale.ENGLISH;
    private static final String OFFER_MESSAGE_SUPPLIER_SUBJECT = "message.subject.supplier";
    private static final String OFFER_MESSAGE_SUPPLIER_SUBJECT_BUYER_REF = "message.subjectBuyerRef.supplier";
    private static final String OFFER_MESSAGE_SUPPLIER_ORDER_TEMPLATE = "New_Messages_Supplier_Customer_Order";
    private static final String OFFER_MESSAGE_SUPPLIER_ENQUIRY_TEMPLATE = "New_Messages_Supplier_Customer_Enquiry";
    private static final String SPOT_MESSAGE_SUPPLIER_ENQUIRY_TEMPLATE = "New_Messages_Supplier_Customer_Trade_Enquiry";

    private static final String OFFER_MESSAGE_CUSTOMER_SUBJECT = "message.subject.customer";
    private static final String OFFER_MESSAGE_CUSTOMER_SUBJECT_BUYER_REF = "message.subjectBuyerRef.customer";
    private static final String OFFER_MESSAGE_CUSTOMER_ORDER_TEMPLATE = "New_Messages_Customer_Supplier_Order";
    private static final String OFFER_MESSAGE_CUSTOMER_ENQUIRY_TEMPLATE = "New_Messages_Customer_Supplier_Enquiry";
    private static final String SPOT_MESSAGE_CUSTOMER_SUBJECT = "message.subject.trade.customer";
    private static final String SPOT_MESSAGE_CUSTOMER_SUBJECT_BUYER_REF = "message.subjectBuyerRef.trade.customer";
    private static final String SPOT_MESSAGE_CUSTOMER_ENQUIRY_TEMPLATE = "New_Messages_Customer_Supplier_Trade_Enquiry";

    private final MessageSource messageSource;
    private final MessageApiRestService messageApiRestService;
    private final OrderApiRestService orderApiRestService;
    private final VesselApiRestService vesselApiRestService;
    private final CustomerApiRestService customerApiRestService;
    private final SupplierApiRestService supplierApiRestService;
    private final OfferReplacementService orderReplacementService;
    private final WebAppReplacementService webappReplacementService;
    private final NotificationPreferenceService notificationPreferenceService;

    @Override
    public void handle(final Event event) {
        final OfferMessageEventMessage eventMessage = createEventMessage(event);
        final MessageMessage message = getMessage(eventMessage.getMessageId());

        var offer = eventMessage.getOffer();
        OrderType type = offer.getType();
        if (type != OrderType.LUBES) {
            log.info("Offer does not have type LUBES but {}, exiting early", type);
            return;
        }

        // Don't do anything for FORWARDED offers because supplier should not receive any communication.
        if (offer.getEnquiryType() == EnquiryType.FORWARDED) {
            return;
        }

        // There should be no communication between customer and supplier until the spot supplier quoted the enquiry.
        if (EnquiryType.SPOT.equals(offer.getEnquiryType()) && OfferState.ENQUIRY.equals(offer.getState())) {
            return;
        }

        var shouldSendNotification = notificationPreferenceService.shouldSendNotification(
            EnumMapper.map(ReceiverType.class, message.getReceiverType()),
            message.getReceiverId(),
            NotificationCategory.NEW_CHAT_MESSAGE
        );

        if (!shouldSendNotification) {
            log.info(
                "Notification ignored and not sent because it is disabled in the settings for receiver: {}",
                message.getReceiverId()
            );
            return;
        }

        var vesselMessage = vesselApiRestService.getVessel(offer.getVesselId());

        updateMessageWithMailing(offer, message, vesselMessage);
    }

    private void updateMessageWithMailing(
        final Offer offer,
        final MessageMessage message,
        VesselMessage vesselMessage
    ) {
        switch (message.getReceiverType()) {
            case SUPPLIER:
                updateSupplierMessage(message, offer, vesselMessage);
                break;
            case CUSTOMER:
                updateCustomerMessage(message, offer, vesselMessage);
                break;
            default:
                break;
        }
    }

    private void updateSupplierMessage(
        final MessageMessage chatMessage,
        final Offer offer,
        VesselMessage vesselMessage
    ) {
        final MessageMessage message = createOrderMessage(
            chatMessage,
            offer,
            Strings.isNullOrEmpty(offer.getBuyerReference())
                ? OFFER_MESSAGE_SUPPLIER_SUBJECT
                : OFFER_MESSAGE_SUPPLIER_SUBJECT_BUYER_REF,
            findSupplierTemplate(offer),
            createSupplierSubjectReplacements(offer, vesselMessage.getName())
        );

        postMessage(message);
    }

    private Object[] createSupplierSubjectReplacements(final Offer offer, final String vesselName) {
        return new Object[] { vesselName, offer.getOfferNumber(), getCustomerName(offer), offer.getBuyerReference() };
    }

    private String findSupplierTemplate(final Offer offer) {
        if (isInOrderState(offer.getState())) {
            return OFFER_MESSAGE_SUPPLIER_ORDER_TEMPLATE;
        } else {
            if (EnquiryType.SPOT.equals(offer.getEnquiryType())) {
                return SPOT_MESSAGE_SUPPLIER_ENQUIRY_TEMPLATE;
            }
            return OFFER_MESSAGE_SUPPLIER_ENQUIRY_TEMPLATE;
        }
    }

    private void updateCustomerMessage(
        final MessageMessage chatMessage,
        final Offer offer,
        VesselMessage vesselMessage
    ) {
        final MessageMessage message = createOrderMessage(
            chatMessage,
            offer,
            findCustomerSubject(offer),
            findCustomerTemplate(offer),
            createCustomerSubjectReplacements(offer, vesselMessage.getName())
        );

        var recipients = RecipientsCreatorKt.createRecipientListForVesselNotification(
            vesselMessage.getEmail(),
            vesselMessage.getMailNotificationSettings().getOrderUpdateSettings()
        );

        var receiverType = recipients.isEmpty() ? MessageReceiverType.CUSTOMER : MessageReceiverType.CUSTOM;
        message.setReceiverType(receiverType);

        message.setRecipients(
            recipients
                .stream()
                .map(recipient ->
                    RecipientMessage.builder()
                        .name(recipient.getName())
                        .emailAddress(recipient.getEmailAddress())
                        .type(RecipientMessage.Type.valueOf(recipient.getType().name()))
                        .build()
                )
                .toList()
        );

        postMessage(message);
    }

    private String findCustomerSubject(final Offer offer) {
        if (EnquiryType.SPOT.equals(offer.getEnquiryType())) {
            if (!Strings.isNullOrEmpty(offer.getBuyerReference())) {
                return SPOT_MESSAGE_CUSTOMER_SUBJECT_BUYER_REF;
            }
            return SPOT_MESSAGE_CUSTOMER_SUBJECT;
        }
        if (!Strings.isNullOrEmpty(offer.getBuyerReference())) {
            return OFFER_MESSAGE_CUSTOMER_SUBJECT_BUYER_REF;
        }
        return OFFER_MESSAGE_CUSTOMER_SUBJECT;
    }

    private Object[] createCustomerSubjectReplacements(final Offer offer, final String vesselName) {
        return new Object[] { vesselName, getPublicId(offer), getSupplierName(offer), offer.getBuyerReference() };
    }

    private String findCustomerTemplate(final Offer offer) {
        if (isInOrderState(offer.getState())) {
            return OFFER_MESSAGE_CUSTOMER_ORDER_TEMPLATE;
        } else {
            if (EnquiryType.SPOT.equals(offer.getEnquiryType())) {
                return SPOT_MESSAGE_CUSTOMER_ENQUIRY_TEMPLATE;
            }
            return OFFER_MESSAGE_CUSTOMER_ENQUIRY_TEMPLATE;
        }
    }

    private MessageMessage createOrderMessage(
        final MessageMessage message,
        final Offer offer,
        final String subject,
        final String template,
        final Object[] subjectReplacements
    ) {
        message.setHidden(true);
        message.setGroupMail(true);
        message.setSendMail(true);
        message.setSubject(translate(subject, subjectReplacements));
        message.setReplacements(
            createReplacements(offer, EnumMapper.map(ReceiverType.class, message.getReceiverType()))
        );
        message.setTemplate(template);
        return message;
    }

    private Map<String, String> createReplacements(final Offer offer, final ReceiverType messageReceiverType) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.putAll(
            this.webappReplacementService.create(OfferReplacementOfferMapper.create(offer), messageReceiverType)
        );
        replacements.putAll(this.orderReplacementService.create(OfferReplacementOfferMapper.create(offer)));
        return replacements;
    }

    private String translate(final String message, final Object[] messageReplacements) {
        return this.messageSource.getMessage(message, messageReplacements, LOCALE);
    }

    private MessageMessage getMessage(final String messageId) {
        return this.messageApiRestService.getMessage(messageId);
    }

    private void postMessage(final MessageMessage message) {
        this.messageApiRestService.createMessage(message);
    }

    private String getPublicId(final Offer offer) {
        if (EnquiryType.SPOT.equals(offer.getEnquiryType())) {
            return getOrderNumber(offer.getOrderId());
        }
        return offer.getOfferNumber();
    }

    private String getOrderNumber(final String orderId) {
        return this.orderApiRestService.getOrder(orderId).getOrderNumber();
    }

    private String getCustomerName(final Offer offer) {
        return this.customerApiRestService.getCustomer(offer.getCustomerId()).getName();
    }

    private String getSupplierName(final Offer offer) {
        return this.supplierApiRestService.getSupplier(offer.getSupplierId()).getName();
    }

    private OfferMessageEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), OfferMessageEventMessage.class);
    }

    private boolean isInOrderState(final OfferState offerState) {
        return Arrays.asList(
            OfferState.ORDER,
            OfferState.CUSTOMER_ADJUSTED,
            OfferState.SUPPLIER_ADJUSTED,
            OfferState.ADJUSTED,
            OfferState.CONFIRMED,
            OfferState.ACKNOWLEDGED,
            OrderState.CANCELED,
            OfferState.DELIVERED,
            OfferState.DELIVERY_CONFIRMED,
            OfferState.INVOICED
        ).contains(offerState);
    }
}
