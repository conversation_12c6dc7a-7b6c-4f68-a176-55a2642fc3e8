package net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class EnquiryCanceledAssignedHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String OFFER_MESSAGE = "enquiryCustomerCancel.message";
    private static final String OFFER_SUBJECT = "enquiryCustomerCancel.subject";
    private static final String OFFER_BUYER_REF_SUBJECT = "enquiryCustomerCancelBuyerRef.subject";
    private static final String OFFER_TEMPLATE = "Order_Supplier_Enquiry_Canceled";

    private CustomerStateHandlerSupport customerStateHandlerSupport;

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        Notification notification = createOfferCancelNotification(offerUpdatedEvent);

        customerStateHandlerSupport.notify(notification);

        return offerUpdatedEvent;
    }

    private Notification createOfferCancelNotification(final OfferUpdatedEvent event) {
        final Offer offer = event.getEventMessage().getOffer();
        final Notification.NotificationBuilder builder =
            this.customerStateHandlerSupport.createNotificationBuilder(event);
        return builder
            .message(OFFER_MESSAGE)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? OFFER_SUBJECT : OFFER_BUYER_REF_SUBJECT)
            .template(OFFER_TEMPLATE)
            .messageReplacements(this.customerStateHandlerSupport.createCancelMessageReplacements(offer))
            .build();
    }
}
