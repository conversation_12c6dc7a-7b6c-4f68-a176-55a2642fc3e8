package net.closelink.notificationservice.handler.offer.updated.state.customer;

import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.created.HandlerRegistry;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;

public class StateChangeHandlerRegistry
    extends HandlerRegistry<EnquiryType, StateChangeHandler<OfferState, OfferUpdatedEvent>> {

    private static final long serialVersionUID = 1L;
}
