package net.closelink.notificationservice.handler.offer.updated.fields.activity.customer;

import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.replacement.offer.mapper.OfferReplacementOfferMapper;
import net.closelink.notificationservice.replacement.subject.customer.CustomerSubjectReplacementService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.support.RecipientsCreatorKt;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@AllArgsConstructor
public class DefaultCustomerActivityLogNotifyService implements CustomerActivityLogNotifyService {

    private static final String CUSTOMER_QUOTED_SUBJECT = "activitylog.subject.customer.quoted";
    private static final String CUSTOMER_QUOTED_SUBJECT_BUYER_REF = "activitylog.subject.customer.quotedBuyerRef";
    private static final String CUSTOMER_QUOTED_TEMPLATE = "Order_Customer_New_Quote";

    private static final String SPOT_CUSTOMER_QUOTED_SUBJECT = "activitylog.subject.trade.customer.quoted";
    private static final String SPOT_CUSTOMER_QUOTED_SUBJECT_BUYER_REF =
        "activitylog.subject.trade.customer.quotedBuyerRef";
    private static final String SPOT_CUSTOMER_QUOTED_TEMPLATE = "Order_Customer_New_Quote_Trade";

    private static final String CUSTOMER_OFFER_SUBJECT = "activitylog.subject.customer.order";
    private static final String CUSTOMER_OFFER_SUBJECT_BUYER_REF = "activitylog.subject.customer.orderBuyerRef";
    private static final String CUSTOMER_OFFER_TEMPLATE = "Order_Customer_Order_Supplier_Changes";

    private final NotificationService notificationService;
    private final CustomerSubjectReplacementService customerSubjectReplacementService;
    private final VesselApiRestService vesselApiRestService;

    @Override
    public void notify(
        final OfferUpdatedEvent offerUpdatedEvent,
        final Notification.NotificationBuilder notificationBuilder
    ) {
        final Offer offer = offerUpdatedEvent.getEventMessage().getOffer();
        if (!offerUpdatedEvent.getEventMessage().getStateChanged() && shouldSendEmailForChanges(offer)) {
            log.info("Will create an email notification for customer for offer with ID {}", offer.getId());
            createCustomerNotification(offer, notificationBuilder);
        }

        String userId = offerUpdatedEvent.getEvent().getEventTrigger().getUserId();
        if (userId != null) {
            notificationBuilder.senderType(SenderType.USER).senderId(userId);
        } else {
            notificationBuilder
                .senderType(SenderType.SUPPLIER)
                .senderId(offerUpdatedEvent.getEvent().getEventTrigger().getCompanyId());
        }

        var vessel = this.vesselApiRestService.getVessel(offer.getVesselId());
        var recipients = RecipientsCreatorKt.createRecipientListForVesselNotification(
            vessel.getEmail(),
            vessel.getMailNotificationSettings().getOrderUpdateSettings()
        );
        var receiverType = recipients.isEmpty() ? ReceiverType.CUSTOMER : ReceiverType.CUSTOM;

        notificationBuilder.receiverType(receiverType);
        notificationBuilder.receiverId(offer.getCustomerId());
        notificationBuilder.recipients(recipients);
        notify(notificationBuilder);
    }

    private void notify(final Notification.NotificationBuilder notificationBuilder) {
        this.notificationService.notify(notificationBuilder.build());
    }

    private void createCustomerNotification(
        final Offer updateOffer,
        final Notification.NotificationBuilder notificationBuilder
    ) {
        if (OfferState.QUOTED.equals(updateOffer.getState())) {
            createQuoteNotification(updateOffer, notificationBuilder);
        } else {
            createOrderChangedNotification(notificationBuilder, updateOffer);
        }
        notificationBuilder.groupMail(true).sendMail(true);
    }

    private void createOrderChangedNotification(
        final Notification.NotificationBuilder notificationBuilder,
        final Offer offer
    ) {
        notificationBuilder
            .subject(
                Strings.isNullOrEmpty(offer.getBuyerReference())
                    ? CUSTOMER_OFFER_SUBJECT
                    : CUSTOMER_OFFER_SUBJECT_BUYER_REF
            )
            .subjectReplacements(
                this.customerSubjectReplacementService.createSubjectReplacements(
                        OfferReplacementOfferMapper.create(offer)
                    )
            )
            .template(CUSTOMER_OFFER_TEMPLATE);
    }

    private void createQuoteNotification(
        final Offer updateOffer,
        final Notification.NotificationBuilder notificationBuilder
    ) {
        if (EnquiryType.SPOT.equals(updateOffer.getEnquiryType())) {
            createSpotQuoteNotification(updateOffer, notificationBuilder);
        } else {
            createAssignedQuoteNotification(updateOffer, notificationBuilder);
        }
    }

    private void createSpotQuoteNotification(
        final Offer updateOffer,
        final Notification.NotificationBuilder notificationBuilder
    ) {
        notificationBuilder
            .subject(
                Strings.isNullOrEmpty(updateOffer.getBuyerReference())
                    ? SPOT_CUSTOMER_QUOTED_SUBJECT
                    : SPOT_CUSTOMER_QUOTED_SUBJECT_BUYER_REF
            )
            .subjectReplacements(
                this.customerSubjectReplacementService.createSpotSubjectReplacements(
                        OfferReplacementOfferMapper.create(updateOffer)
                    )
            )
            .template(SPOT_CUSTOMER_QUOTED_TEMPLATE);
    }

    private void createAssignedQuoteNotification(
        final Offer updateOffer,
        final Notification.NotificationBuilder notificationBuilder
    ) {
        notificationBuilder
            .subject(
                Strings.isNullOrEmpty(updateOffer.getBuyerReference())
                    ? CUSTOMER_QUOTED_SUBJECT
                    : CUSTOMER_QUOTED_SUBJECT_BUYER_REF
            )
            .subjectReplacements(
                this.customerSubjectReplacementService.createSubjectReplacements(
                        OfferReplacementOfferMapper.create(updateOffer)
                    )
            )
            .template(CUSTOMER_QUOTED_TEMPLATE);
    }

    private boolean shouldSendEmailForChanges(final Offer offer) {
        return (
            OfferState.QUOTED.equals(offer.getState()) ||
            OfferState.ORDER.equals(offer.getState()) ||
            OfferState.CUSTOMER_ADJUSTED.equals(offer.getState()) ||
            OfferState.SUPPLIER_ADJUSTED.equals(offer.getState()) ||
            OfferState.CONFIRMED.equals(offer.getState()) ||
            OfferState.ACKNOWLEDGED.equals(offer.getState())
        );
    }
}
