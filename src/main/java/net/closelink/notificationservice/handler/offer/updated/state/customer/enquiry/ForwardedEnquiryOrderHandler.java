package net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.replacement.subject.supplier.SupplierSubjectReplacementService;
import net.closelink.notificationservice.service.attachment.ForwardedOrderAttachmentCreator;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ForwardedEnquiryOrder<PERSON>and<PERSON> implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE = "enquiryOrder.message";
    private static final String SUBJECT = "forwarded.newOrder.subject";
    private static final String SUBJECT_BUYER_REF = "forwarded.newOrder.subjectBuyerRef";
    private static final String TEMPLATE = "Supplier_Enquiry_Forwarded_To_Order";

    private final SupplierSubjectReplacementService subjectReplacementService;
    private final ForwardedOrderAttachmentCreator forwardedOrderAttachmentCreator;
    private final CustomerStateHandlerSupport customerStateHandlerSupport;

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        Offer offer = offerUpdatedEvent.getEventMessage().getOffer();
        Notification.NotificationBuilder notificationBuilder = customerStateHandlerSupport.createNotificationBuilder(
            offerUpdatedEvent
        );

        notificationBuilder
            .message(MESSAGE)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .subjectReplacements(subjectReplacementService.createSubjectReplacements(offer))
            .template(TEMPLATE)
            .attachments(forwardedOrderAttachmentCreator.createAttachmentForOffer(offer));

        customerStateHandlerSupport.notify(notificationBuilder.build());

        return offerUpdatedEvent;
    }
}
