package net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge;

import net.closelink.cenqueue.objects.Surcharge;

public abstract class SurchargeHelper {

    protected static String getMessage(Surcharge surcharge, String messageSurcharge, String messageWaiver) {
        if (surcharge.getValue() != null && surcharge.getValue().getValue().longValue() < 0) {
            return messageWaiver;
        }

        return messageSurcharge;
    }
}
