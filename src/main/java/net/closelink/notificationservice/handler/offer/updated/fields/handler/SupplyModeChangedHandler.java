package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import java.util.Locale;
import java.util.Optional;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.objects.SupplyModeDetails;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class SupplyModeChangedHandler implements OfferUpdatedEventTypeHandler {

    private static final String ACTIVITYLOG = "activitylog.supplymode";
    private final BaseActivityLogService activityLogService;
    private final MessageSource messageSource;

    @Override
    public void handle(final OfferUpdatedEvent orderUpdatedEvent) {
        var newValue = GsonCoder.encode(orderUpdatedEvent.getEventMessage().getNewValue(), SupplyModeDetails.class);
        var oldValue = GsonCoder.encode(orderUpdatedEvent.getEventMessage().getOldValue(), SupplyModeDetails.class);

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                ACTIVITYLOG,
                new Object[] { translate(oldValue.getSupplyMode()), translate(newValue.getSupplyMode()) }
            );
    }

    private String translate(final String supplyMode) {
        return Optional.ofNullable(supplyMode)
            .map(mode -> this.messageSource.getMessage(mode, null, Locale.ENGLISH))
            .orElse(null);
    }
}
