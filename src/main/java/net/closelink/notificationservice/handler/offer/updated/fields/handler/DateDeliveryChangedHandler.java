package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import java.time.OffsetDateTime;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DateDeliveryChangedHandler implements OfferUpdatedEventTypeHandler {

    private static final String ACTIVITYLOG = "activitylog.deliverydate";
    private final BaseActivityLogService activityLogService;

    @Autowired
    public DateDeliveryChangedHandler(final BaseActivityLogService activityLogService) {
        super();
        this.activityLogService = activityLogService;
    }

    @Override
    public void handle(final OfferUpdatedEvent orderUpdatedEvent) {
        final OffsetDateTime newValue = EncodeSupport.encodeOffsetDateTime(
            orderUpdatedEvent.getEventMessage().getNewValue()
        );
        final OffsetDateTime oldValue = EncodeSupport.encodeOffsetDateTime(
            orderUpdatedEvent.getEventMessage().getOldValue()
        );

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                ACTIVITYLOG,
                new Object[] { oldValue.toString(), newValue.toString() }
            );
    }
}
