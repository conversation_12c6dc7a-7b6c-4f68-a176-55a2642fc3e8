package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class BuyerRefChangedHandler implements OfferUpdatedEventTypeHandler {

    private final BaseActivityLogService activityLogService;

    @Override
    public void handle(OfferUpdatedEvent offerUpdatedEvent) {
        final Offer offer = offerUpdatedEvent.getEventMessage().getOffer();

        log.info("Handling BuyerRefChanged event. OfferId: {}. BuyerRef: {}", offer.getId(), offer.getBuyerReference());

        final String newValue = EncodeSupport.encodeString(offerUpdatedEvent.getEventMessage().getNewValue());
        final String oldValue = EncodeSupport.encodeString(offerUpdatedEvent.getEventMessage().getOldValue());

        this.activityLogService.createActivityLog(
                offerUpdatedEvent,
                "activitylog.buyerref",
                new Object[] { oldValue, newValue }
            );
        log.info("Successfully sent notification for BuyerRefChanged event");
    }
}
