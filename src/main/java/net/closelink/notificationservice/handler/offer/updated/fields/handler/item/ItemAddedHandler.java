package net.closelink.notificationservice.handler.offer.updated.fields.handler.item;

import net.closelink.cenqueue.objects.Item;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.service.rest.product.ProductApiRestService;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ItemAddedHandler implements OfferUpdatedEventTypeHandler {

    private static final String ACTIVITYLOG = "activitylog.itemadded";
    private final BaseActivityLogService activityLogService;
    private final ProductApiRestService ApiRestService;

    @Autowired
    public ItemAddedHandler(
        final BaseActivityLogService activityLogService,
        final ProductApiRestService ApiRestService
    ) {
        this.activityLogService = activityLogService;
        this.ApiRestService = ApiRestService;
    }

    @Override
    public void handle(final OfferUpdatedEvent orderUpdatedEvent) {
        final Item newValue = EncodeSupport.encodeItem(orderUpdatedEvent.getEventMessage().getNewValue());

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                ACTIVITYLOG,
                new Object[] { findProductName(newValue.getProductId()) }
            );
    }

    private String findProductName(final String productId) {
        if (productId == null) {
            return null;
        }

        return this.ApiRestService.getProduct(productId).getName();
    }
}
