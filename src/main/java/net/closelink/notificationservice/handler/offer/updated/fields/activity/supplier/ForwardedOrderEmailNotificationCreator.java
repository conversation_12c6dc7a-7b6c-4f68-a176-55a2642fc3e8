package net.closelink.notificationservice.handler.offer.updated.fields.activity.supplier;

import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.replacement.subject.supplier.SupplierSubjectReplacementService;
import net.closelink.notificationservice.service.attachment.ForwardedOrderAttachmentCreator;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ForwardedOrderEmailNotificationCreator implements EmailNotificationCreator {

    private static final String SUPPLIER_FORWARDED_ORDER_SUBJECT = "activitylog.subject.forwarded.supplier.order";
    private static final String SUPPLIER_FORWARDED_ORDER_SUBJECT_BUYER_REF =
        "activitylog.subjectBuyerRef.forwarded.supplier.order";
    private static final String SUPPLIER_FORWARDED_ORDER_TEMPLATE = "Supplier_Order_Forwarded_Customer_Changes";
    private static final String FORWARDED_SENDER_MAIL = "<EMAIL>";

    private final SupplierSubjectReplacementService supplierSubjectReplacementService;
    private final ForwardedOrderAttachmentCreator forwardedOrderAttachmentCreator;
    private final CustomerApiRestService customerApiRestService;

    @Override
    public Notification createNotification(Notification.NotificationBuilder notificationBuilder, Offer offer) {
        return notificationBuilder
            .subject(
                Strings.isNullOrEmpty(offer.getBuyerReference())
                    ? SUPPLIER_FORWARDED_ORDER_SUBJECT
                    : SUPPLIER_FORWARDED_ORDER_SUBJECT_BUYER_REF
            )
            .template(SUPPLIER_FORWARDED_ORDER_TEMPLATE)
            .subjectReplacements(supplierSubjectReplacementService.createSubjectReplacements(offer))
            .attachments(forwardedOrderAttachmentCreator.createAttachmentForOffer(offer))
            .groupMail(true)
            .sendMail(true)
            .customSenderEmailAddress(FORWARDED_SENDER_MAIL)
            .customSenderName(getCustomerName(offer))
            .build();
    }

    private String getCustomerName(Offer offer) {
        return customerApiRestService.getCustomer(offer.getCustomerId()).getName();
    }
}
