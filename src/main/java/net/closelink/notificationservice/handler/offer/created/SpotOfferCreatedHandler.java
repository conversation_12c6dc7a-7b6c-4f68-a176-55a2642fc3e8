package net.closelink.notificationservice.handler.offer.created;

import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class SpotOfferCreatedHandler implements OfferUpdatedEventHandler {

    private static final String ORDER_MESSAGE = "draftTradeEnquiry.message";
    private static final String ORDER_SUBJECT = "draftTradeEnquiry.subject";
    private static final String ORDER_SUBJECT_BUYER_REF = "draftTradeEnquiryBuyerRef.subject";
    private static final String ORDER_TEMPLATE = "Order_Supplier_New_Enquiry_Trade";

    private final CustomerStateHandlerSupport customerStateHandlerSupport;

    @Override
    public void handle(OfferUpdatedEvent offerUpdatedEvent) {
        Notification notification = createSpotEnquiryNotification(offerUpdatedEvent);

        customerStateHandlerSupport.notify(notification);
    }

    private Notification createSpotEnquiryNotification(final OfferUpdatedEvent event) {
        final Notification.NotificationBuilder builder =
            this.customerStateHandlerSupport.createNotificationBuilder(event);

        Offer offer = event.getEventMessage().getOffer();

        return builder
            .message(ORDER_MESSAGE)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? ORDER_SUBJECT : ORDER_SUBJECT_BUYER_REF)
            .template(ORDER_TEMPLATE)
            .hidden(true)
            .build();
    }
}
