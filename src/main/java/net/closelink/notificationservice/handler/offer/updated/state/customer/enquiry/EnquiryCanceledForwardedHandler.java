package net.closelink.notificationservice.handler.offer.updated.state.customer.enquiry;

import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.customer.all.ForwardedEnquiryCustomerAbortedHandler;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class EnquiryCanceledForwardedHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private final ForwardedEnquiryCustomerAbortedHandler forwardedEnquiryCustomerAbortedHandlerHandler;

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        return forwardedEnquiryCustomerAbortedHandlerHandler.handle(stateChange, offerUpdatedEvent);
    }
}
