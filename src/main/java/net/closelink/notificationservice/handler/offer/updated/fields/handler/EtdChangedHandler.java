package net.closelink.notificationservice.handler.offer.updated.fields.handler;

import lombok.AllArgsConstructor;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class EtdChangedHandler implements OfferUpdatedEventTypeHandler {

    private final BaseActivityLogService activityLogService;

    @Override
    public void handle(OfferUpdatedEvent orderUpdatedEvent) {
        final String newValue = EncodeSupport.encodeString(orderUpdatedEvent.getEventMessage().getNewValue());
        final String oldValue = EncodeSupport.encodeString(orderUpdatedEvent.getEventMessage().getOldValue());

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                "activitylog.etd",
                new Object[] { oldValue, newValue }
            );
    }
}
