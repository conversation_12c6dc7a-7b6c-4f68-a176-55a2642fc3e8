package net.closelink.notificationservice.handler.offer.updated.state.customer.all;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GeneralCustomerCanceledHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE = "customerCancel.message";
    private static final String SUBJECT = "customerCancel.subject";
    private static final String SUBJECT_BUYER_REF = "customerCancelBuyerRef.subject";
    private static final String TEMPLATE = "Order_Supplier_Order_Canceled";

    private final CustomerStateHandlerSupport customerStateHandlerSupport;

    @Autowired
    public GeneralCustomerCanceledHandler(final CustomerStateHandlerSupport customerStateHandlerSupport) {
        this.customerStateHandlerSupport = customerStateHandlerSupport;
    }

    @Override
    public OfferUpdatedEvent handle(final StateChange<OfferState> stateChange, final OfferUpdatedEvent event) {
        final Notification.NotificationBuilder builder =
            this.customerStateHandlerSupport.createNotificationBuilder(event);

        Offer offer = event.getEventMessage().getOffer();

        final Notification notification = builder
            .message(MESSAGE)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .template(TEMPLATE)
            .messageReplacements(
                this.customerStateHandlerSupport.createCancelMessageReplacements(event.getEventMessage().getOffer())
            )
            .build();

        this.customerStateHandlerSupport.notify(notification);

        return event;
    }
}
