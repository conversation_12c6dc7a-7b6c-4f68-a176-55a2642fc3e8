package net.closelink.notificationservice.handler.offer.reminder;

import com.google.common.base.Strings;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.offer.OfferDeliveryReminderEventMessage;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OrderType;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.replacement.offer.OfferReplacementService;
import net.closelink.notificationservice.replacement.offer.mapper.OfferReplacementOfferMapper;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import net.closelink.notificationservice.support.RecipientsCreatorKt;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@AllArgsConstructor
public class OfferDeliveryReminderHandler implements EventHandler {

    public static final String MESSAGE = "deliveryReminder.message";
    public static final String SUBJECT = "deliveryReminder.subject";
    public static final String SUBJECT_BUYER_REF = "deliveryReminderBuyerRef.subject";
    public static final String TEMPLATE = "Order_Customer_Supply_Reminder";

    private final NotificationService notificationService;
    private final VesselApiRestService vesselApiRestService;
    private final OfferReplacementService offerReplacementService;
    private final WebAppReplacementService webappReplacementService;

    @Override
    public void handle(final Event event) {
        final OfferDeliveryReminderEventMessage eventMessage = createEventMessage(event);

        OrderType type = eventMessage.getOffer().getType();
        if (type != OrderType.LUBES) {
            log.info("Offer does not have type LUBES but {}, exiting early", type);
            return;
        }

        var vessel = vesselApiRestService.getVessel(eventMessage.getOffer().getVesselId());
        this.notificationService.notify(createNotification(eventMessage.getOffer(), vessel));
    }

    public Notification createNotification(final Offer offer, VesselMessage vessel) {
        var recipients = RecipientsCreatorKt.createRecipientListForVesselNotification(
            vessel.getEmail(),
            vessel.getMailNotificationSettings().getOrderUpdateSettings()
        );
        var receiverType = recipients.isEmpty() ? ReceiverType.CUSTOMER : ReceiverType.CUSTOM;

        return Notification.builder()
            .offerId(offer.getId())
            .hidden(true)
            .sendMail(true)
            .message(MESSAGE)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .subjectReplacements(createSubjectReplacements(offer, vessel.getName()))
            .template(TEMPLATE)
            .mailReplacements(createReplacements(offer))
            .receiverType(receiverType)
            .recipients(recipients)
            .receiverId(offer.getCustomerId())
            .senderType(SenderType.SYSTEM)
            .category(NotificationCategory.ORDER_DELIVERY_REMINDER)
            .build();
    }

    private Map<String, String> createReplacements(final Offer offer) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.putAll(
            this.webappReplacementService.create(OfferReplacementOfferMapper.create(offer), ReceiverType.CUSTOMER)
        );
        replacements.putAll(this.offerReplacementService.create(OfferReplacementOfferMapper.create(offer)));
        return replacements;
    }

    protected Object[] createSubjectReplacements(final Offer offer, String vesselName) {
        return new Object[] { vesselName, offer.getOfferNumber(), offer.getBuyerReference() };
    }

    private OfferDeliveryReminderEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), OfferDeliveryReminderEventMessage.class);
    }
}
