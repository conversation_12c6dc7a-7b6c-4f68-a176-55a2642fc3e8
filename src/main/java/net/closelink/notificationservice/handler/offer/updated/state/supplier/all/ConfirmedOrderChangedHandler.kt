package net.closelink.notificationservice.handler.offer.updated.state.supplier.all

import de.tschumacher.simplestatemachine.domain.StateChange
import de.tschumacher.simplestatemachine.handler.StateChangeHandler
import net.closelink.cenqueue.types.OfferState
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierStateHandlerSupport
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent
import org.springframework.stereotype.Component

@Component
class ConfirmedOrderChangedHandler(
    private val supplierStateHandlerSupport: SupplierStateHandlerSupport
) : StateChangeHandler<OfferState, OfferUpdatedEvent> {
    override fun handle(
        stateChange: StateChange<OfferState>,
        offerUpdatedEvent: OfferUpdatedEvent,
    ): OfferUpdatedEvent {
        val notificationBuilder =
            supplierStateHandlerSupport.createNotificationBuilder(offerUpdatedEvent)

        val offer = offerUpdatedEvent.eventMessage.offer

        val notification =
            notificationBuilder
                .message("supplierConfirmedOrder.message")
                .subject(
                    if (offer.buyerReference.isNullOrEmpty()) "supplierConfirmedOrder.subject"
                    else "supplierConfirmedOrderBuyerRef.subject"
                )
                .template("Order_Customer_Order_Supplier_Changes")
                .groupMail(true)
                .build()

        supplierStateHandlerSupport.notify(notification)
        return offerUpdatedEvent
    }
}
