package net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge;

import java.util.HashMap;
import java.util.Optional;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Surcharge;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.service.rest.coredata.CoreDataService;
import net.closelink.notificationservice.service.rest.coredata.EnumMessage;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class SurchargeTypeChangedHandler implements OfferUpdatedEventTypeHandler {

    private static final String ACTIVITYLOG_SURCHARGE = "activitylog.surchagename";
    private static final String ACTIVITYLOG_WAIVER = "activitylog.waivername";

    private final BaseActivityLogService activityLogService;
    private final CoreDataService coreDataService;

    @Override
    public void handle(final OfferUpdatedEvent orderUpdatedEvent) {
        final Surcharge oldSurcharge = EncodeSupport.encodeSurcharge(orderUpdatedEvent.getEventMessage().getOldValue());
        final Surcharge newSurcharge = EncodeSupport.encodeSurcharge(orderUpdatedEvent.getEventMessage().getNewValue());
        HashMap<String, EnumMessage.DetailEnumMessage> surchargeTypeEnumMap =
            coreDataService.getHumanReadableValueForSurchargeTypeMessage();

        String oldSurchargeType = extractHumanReadableSurchargeType(surchargeTypeEnumMap, oldSurcharge);
        String newSurchargeType = extractHumanReadableSurchargeType(surchargeTypeEnumMap, newSurcharge);

        if ("CUSTOM".equals(oldSurcharge.getSurchargeType())) {
            this.activityLogService.createActivityLog(
                    orderUpdatedEvent,
                    SurchargeHelper.getMessage(newSurcharge, ACTIVITYLOG_SURCHARGE, ACTIVITYLOG_WAIVER),
                    new Object[] { oldSurcharge.getName(), newSurchargeType }
                );
        } else if ("CUSTOM".equals(newSurcharge.getSurchargeType())) {
            this.activityLogService.createActivityLog(
                    orderUpdatedEvent,
                    SurchargeHelper.getMessage(newSurcharge, ACTIVITYLOG_SURCHARGE, ACTIVITYLOG_WAIVER),
                    new Object[] { oldSurchargeType, newSurcharge.getName() }
                );
        } else {
            this.activityLogService.createActivityLog(
                    orderUpdatedEvent,
                    SurchargeHelper.getMessage(newSurcharge, ACTIVITYLOG_SURCHARGE, ACTIVITYLOG_WAIVER),
                    new Object[] { oldSurchargeType, newSurchargeType }
                );
        }
    }

    private String extractHumanReadableSurchargeType(
        HashMap<String, EnumMessage.DetailEnumMessage> surchargeTypeEnumMap,
        Surcharge surcharge
    ) {
        EnumMessage.DetailEnumMessage detailEnumMessage = surchargeTypeEnumMap.get(surcharge.getSurchargeType());
        return Optional.ofNullable(detailEnumMessage)
            .map(EnumMessage.DetailEnumMessage::getHumanReadableValue)
            .orElse(null);
    }
}
