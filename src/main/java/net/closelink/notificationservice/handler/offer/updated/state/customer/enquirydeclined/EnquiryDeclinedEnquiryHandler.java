package net.closelink.notificationservice.handler.offer.updated.state.customer.enquirydeclined;

import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.customer.StateChangeHandlerRegistry;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EnquiryDeclinedEnquiryHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private StateChangeHandlerRegistry stateChangeHandlerRegistry;

    @Autowired
    public EnquiryDeclinedEnquiryHandler(
        SpotEnquiryDeclinedEnquiryHandler spotEnquiryDeclinedEnquiryHandler,
        AssignedEnquiryDeclinedEnquiry<PERSON>andler assignedEnquiryDeclinedEnquiryHandler,
        ForwardedEnquiryDeclinedEnquiryHandler forwardedEnquiryDeclinedEnquiryHandler
    ) {
        this.stateChangeHandlerRegistry = makeStateChangeHandlerRegistry(
            spotEnquiryDeclinedEnquiryHandler,
            assignedEnquiryDeclinedEnquiryHandler,
            forwardedEnquiryDeclinedEnquiryHandler
        );
    }

    private static StateChangeHandlerRegistry makeStateChangeHandlerRegistry(
        SpotEnquiryDeclinedEnquiryHandler spotEnquiryDeclinedEnquiryHandler,
        final AssignedEnquiryDeclinedEnquiryHandler assignedEnquiryDeclinedEnquiryHandler,
        final ForwardedEnquiryDeclinedEnquiryHandler forwardedEnquiryDeclinedEnquiryHandler
    ) {
        StateChangeHandlerRegistry stateChangeHandlerRegistry = new StateChangeHandlerRegistry();

        stateChangeHandlerRegistry.put(EnquiryType.ASSIGNED, assignedEnquiryDeclinedEnquiryHandler);
        stateChangeHandlerRegistry.put(EnquiryType.SPOT, spotEnquiryDeclinedEnquiryHandler);
        stateChangeHandlerRegistry.put(EnquiryType.FORWARDED, forwardedEnquiryDeclinedEnquiryHandler);
        return stateChangeHandlerRegistry;
    }

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        EnquiryType enquiryType = offerUpdatedEvent.getEventMessage().getOffer().getEnquiryType();

        StateChangeHandler<OfferState, OfferUpdatedEvent> stateChangeHandler = stateChangeHandlerRegistry.get(
            enquiryType
        );

        return stateChangeHandler.handle(stateChange, offerUpdatedEvent);
    }
}
