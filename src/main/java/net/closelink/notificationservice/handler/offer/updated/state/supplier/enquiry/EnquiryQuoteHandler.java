package net.closelink.notificationservice.handler.offer.updated.state.supplier.enquiry;

import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.customer.StateChangeHandlerRegistry;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EnquiryQuoteHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private final StateChangeHandlerRegistry stateChangeHandlerRegistry;

    @Autowired
    public EnquiryQuoteHandler(
        EnquiryQuoteAssignedHandler enquiryQuoteAssignedHandler,
        EnquiryQuoteSpotHandler enquiryQuoteSpotHandler
    ) {
        this.stateChangeHandlerRegistry = makeStateChangeHandlerRegistry(
            enquiryQuoteAssignedHandler,
            enquiryQuoteSpotHandler
        );
    }

    private static StateChangeHandlerRegistry makeStateChangeHandlerRegistry(
        EnquiryQuoteAssignedHandler enquiryQuoteAssignedHandler,
        EnquiryQuoteSpotHandler enquiryQuoteSpotHandler
    ) {
        StateChangeHandlerRegistry stateChangeHandlerRegistry = new StateChangeHandlerRegistry();

        stateChangeHandlerRegistry.put(EnquiryType.ASSIGNED, enquiryQuoteAssignedHandler);
        stateChangeHandlerRegistry.put(EnquiryType.SPOT, enquiryQuoteSpotHandler);
        stateChangeHandlerRegistry.put(EnquiryType.FORWARDED, enquiryQuoteAssignedHandler);
        return stateChangeHandlerRegistry;
    }

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        EnquiryType enquiryType = offerUpdatedEvent.getEventMessage().getOffer().getEnquiryType();

        StateChangeHandler<OfferState, OfferUpdatedEvent> stateChangeHandler = stateChangeHandlerRegistry.get(
            enquiryType
        );

        return stateChangeHandler.handle(stateChange, offerUpdatedEvent);
    }
}
