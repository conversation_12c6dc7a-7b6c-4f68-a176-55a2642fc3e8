package net.closelink.notificationservice.handler.offer.updated.state.supplier.order;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.supplier.SupplierStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class GeneralSupplierAcknowledgedHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE = "acknowledgedOffer.message";
    private static final String SUBJECT = "acknowledgedOffer.subject";
    private static final String SUBJECT_BUYER_REF = "acknowledgedOfferBuyerRef.subject";
    private static final String TEMPLATE = "Order_Customer_Order_Acknowledged";

    private SupplierStateHandlerSupport supplierStateHandlerSupport;

    @Override
    public OfferUpdatedEvent handle(final StateChange<OfferState> stateChange, final OfferUpdatedEvent event) {
        final Notification notification = createAcknowledgedNotification(event);
        this.supplierStateHandlerSupport.notify(notification);

        return event;
    }

    private Notification createAcknowledgedNotification(final OfferUpdatedEvent event) {
        final Notification.NotificationBuilder builder =
            this.supplierStateHandlerSupport.createNotificationBuilder(event);

        Offer offer = event.getEventMessage().getOffer();

        return builder
            .message(MESSAGE)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .template(TEMPLATE)
            .build();
    }
}
