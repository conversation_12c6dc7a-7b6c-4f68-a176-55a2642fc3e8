package net.closelink.notificationservice.handler.offer.updated.fields.handler.item;

import java.util.Locale;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Item;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.service.rest.product.ProductApiRestService;
import net.closelink.notificationservice.service.rest.product.domain.ProductMessage;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class ItemPhysicalSupplierNameChangedHandler implements OfferUpdatedEventTypeHandler {

    private static final String PHYSICAL_SUPPLIER = "activitylog.itemphysicalsupplier";

    private final BaseActivityLogService activityLogService;
    private final ProductApiRestService productApiRestService;
    private final MessageSource messageSource;

    @Override
    public void handle(OfferUpdatedEvent orderUpdatedEvent) {
        Item oldItem = EncodeSupport.encodeItem(orderUpdatedEvent.getEventMessage().getOldValue());
        Item newItem = EncodeSupport.encodeItem(orderUpdatedEvent.getEventMessage().getNewValue());

        ProductMessage product = getProduct(newItem.getProductId());

        String translatedSulphurContent = messageSource.getMessage(product.getSulphurContent(), null, Locale.ENGLISH);

        StringBuilder productNameStringBuilder = new StringBuilder().append(product.getName());

        if (!translatedSulphurContent.equals(product.getSulphurContent())) {
            productNameStringBuilder.append(" ");
            productNameStringBuilder.append(translatedSulphurContent);
        }

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                PHYSICAL_SUPPLIER,
                new Object[] {
                    productNameStringBuilder.toString(),
                    oldItem.getPhysicalSupplierName(),
                    newItem.getPhysicalSupplierName(),
                }
            );
    }

    private ProductMessage getProduct(String productId) {
        return this.productApiRestService.getProduct(productId);
    }
}
