package net.closelink.notificationservice.handler.offer.updated.state.customer.acknowledge;

import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.customer.StateChangeHandlerRegistry;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AcknowledgedOrderHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private StateChangeHandlerRegistry stateChangeHandlerRegistry;

    @Autowired
    public AcknowledgedOrderHandler(
        GeneralAcknowledgedOrderHandler generalAcknowledgedOrderHandler,
        ForwardedAcknowledgedOrderHandler forwardedAcknowledgedOrderHandler
    ) {
        this.stateChangeHandlerRegistry = makeStateChangeHandlerRegistry(
            generalAcknowledgedOrderHandler,
            forwardedAcknowledgedOrderHandler
        );
    }

    private static StateChangeHandlerRegistry makeStateChangeHandlerRegistry(
        GeneralAcknowledgedOrderHandler generalAcknowledgedOrderHandler,
        ForwardedAcknowledgedOrderHandler forwardedAcknowledgedOrderHandler
    ) {
        StateChangeHandlerRegistry stateChangeHandlerRegistry = new StateChangeHandlerRegistry();

        stateChangeHandlerRegistry.put(EnquiryType.ASSIGNED, generalAcknowledgedOrderHandler);
        stateChangeHandlerRegistry.put(EnquiryType.SPOT, generalAcknowledgedOrderHandler);
        stateChangeHandlerRegistry.put(EnquiryType.FORWARDED, forwardedAcknowledgedOrderHandler);
        return stateChangeHandlerRegistry;
    }

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        EnquiryType enquiryType = offerUpdatedEvent.getEventMessage().getOffer().getEnquiryType();

        StateChangeHandler<OfferState, OfferUpdatedEvent> stateChangeHandler = stateChangeHandlerRegistry.get(
            enquiryType
        );

        return stateChangeHandler.handle(stateChange, offerUpdatedEvent);
    }
}
