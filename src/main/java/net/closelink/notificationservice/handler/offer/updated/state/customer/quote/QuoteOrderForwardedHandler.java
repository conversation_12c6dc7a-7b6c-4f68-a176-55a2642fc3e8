package net.closelink.notificationservice.handler.offer.updated.state.customer.quote;

import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import java.util.Collections;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.replacement.subject.supplier.SupplierSubjectReplacementService;
import net.closelink.notificationservice.service.attachment.ForwardedOrderAttachmentCreator;
import org.springframework.stereotype.Service;

// TODO: Check differences to ForwardedEnquiryOrderHandler
@Service
@AllArgsConstructor
public class QuoteOrderForwardedHand<PERSON> implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE = "quoteOrder.message";
    private static final String SUBJECT = "forwarded.newOrder.subject";
    private static final String TEMPLATE = "Supplier_Enquiry_Forwarded_To_Order";

    private final SupplierSubjectReplacementService subjectReplacementService;
    private final ForwardedOrderAttachmentCreator forwardedOrderAttachmentCreator;
    private final CustomerStateHandlerSupport customerStateHandlerSupport;

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        Offer offer = offerUpdatedEvent.getEventMessage().getOffer();
        Notification.NotificationBuilder notificationBuilder = customerStateHandlerSupport.createNotificationBuilder(
            offerUpdatedEvent
        );

        notificationBuilder
            .message(MESSAGE)
            .subject(SUBJECT)
            .subjectReplacements(subjectReplacementService.createSubjectReplacements(offer))
            .template(TEMPLATE)
            .attachments(forwardedOrderAttachmentCreator.createAttachmentForOffer(offer));

        customerStateHandlerSupport.notify(notificationBuilder.build());

        return offerUpdatedEvent;
    }
}
