package net.closelink.notificationservice.handler.offer.updated.state.customer.acknowledge;

import com.google.common.base.Strings;
import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.state.customer.CustomerStateHandlerSupport;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class GeneralAcknowledgedOrderHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private static final String MESSAGE = "acknowledgedOrder.message";
    private static final String SUBJECT = "acknowledgedOrder.subject";
    private static final String SUBJECT_BUYER_REF = "acknowledgedOrderBuyerRef.subject";
    private static final String TEMPLATE = "Order_Supplier_Order_Customer_Changes";

    private final CustomerStateHandlerSupport customerStateHandlerSupport;

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        final Notification.NotificationBuilder builder =
            this.customerStateHandlerSupport.createNotificationBuilder(offerUpdatedEvent);

        Offer offer = offerUpdatedEvent.getEventMessage().getOffer();

        final Notification notification = builder
            .message(MESSAGE)
            .subject(Strings.isNullOrEmpty(offer.getBuyerReference()) ? SUBJECT : SUBJECT_BUYER_REF)
            .template(TEMPLATE)
            .groupMail(true)
            .build();

        this.customerStateHandlerSupport.notify(notification);
        return offerUpdatedEvent;
    }
}
