package net.closelink.notificationservice.handler.offer.created;

import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import net.closelink.notificationservice.exception.NoHandlerFoundException;

@Slf4j
public abstract class HandlerRegistry<T, V> extends HashMap<T, V> {

    private static final long serialVersionUID = 1L;

    public V get(Object type) {
        V handler = super.get(type);

        if (handler == null) {
            throw new NoHandlerFoundException("No Handler found for " + type.toString());
        }

        log.info("Selected Handler {} for Type {}", handler.getClass().getName(), type.toString());

        return handler;
    }
}
