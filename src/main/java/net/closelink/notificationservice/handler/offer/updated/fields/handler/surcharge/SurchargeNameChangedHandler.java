package net.closelink.notificationservice.handler.offer.updated.fields.handler.surcharge;

import net.closelink.cenqueue.objects.Surcharge;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SurchargeNameChangedHandler implements OfferUpdatedEventTypeHandler {

    private static final String ACTIVITYLOG_SURCHARGE = "activitylog.surchagename";
    private static final String ACTIVITYLOG_WAIVER = "activitylog.waivername";

    private final BaseActivityLogService activityLogService;

    @Autowired
    public SurchargeNameChangedHandler(final BaseActivityLogService activityLogService) {
        this.activityLogService = activityLogService;
    }

    @Override
    public void handle(final OfferUpdatedEvent orderUpdatedEvent) {
        final Surcharge newValue = EncodeSupport.encodeSurcharge(orderUpdatedEvent.getEventMessage().getNewValue());
        final Surcharge oldValue = EncodeSupport.encodeSurcharge(orderUpdatedEvent.getEventMessage().getOldValue());

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                SurchargeHelper.getMessage(newValue, ACTIVITYLOG_SURCHARGE, ACTIVITYLOG_WAIVER),
                new Object[] { oldValue.getName(), newValue.getName() }
            );
    }
}
