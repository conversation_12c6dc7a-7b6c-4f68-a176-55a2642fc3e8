package net.closelink.notificationservice.handler.offer.updated.state.supplier;

import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.types.UserType;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.replacement.offer.OfferReplacementService;
import net.closelink.notificationservice.replacement.offer.cancel.OfferReplacementSupportService;
import net.closelink.notificationservice.replacement.offer.mapper.OfferReplacementOfferMapper;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.order.OrderApiRestService;
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.support.RecipientsCreatorKt;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class DefaultSupplierStateHandlerSupport implements SupplierStateHandlerSupport {

    private final NotificationService notificationService;
    private final CustomerApiRestService customerApiRestService;
    private final SupplierApiRestService supplierApiRestService;
    private final OrderApiRestService orderApiRestService;
    private final VesselApiRestService vesselApiRestService;
    private final OfferReplacementService orderReplacementService;
    private final OfferReplacementSupportService orderReplacementSupportService;
    private final WebAppReplacementService webappReplacementService;

    @Override
    public Notification.NotificationBuilder createNotificationBuilder(final OfferUpdatedEvent event) {
        var offer = event.getEventMessage().getOffer();
        var vessel = this.vesselApiRestService.getVessel(offer.getVesselId());
        var recipients = RecipientsCreatorKt.createRecipientListForVesselNotification(
            vessel.getEmail(),
            vessel.getMailNotificationSettings().getOrderUpdateSettings()
        );
        var receiverType = recipients.isEmpty() ? ReceiverType.CUSTOMER : ReceiverType.CUSTOM;

        return chooseSender(Notification.builder(), event)
            .sendMail(true)
            .offerId(offer.getId())
            .receiverId(offer.getCustomerId())
            .receiverType(receiverType)
            .recipients(recipients)
            .mailReplacements(createReplacements(offer))
            .subjectReplacements(createSubjectReplacements(offer))
            .category(NotificationCategory.OFFER_STATE_CHANGE);
    }

    private Notification.NotificationBuilder chooseSender(
        Notification.NotificationBuilder builder,
        OfferUpdatedEvent event
    ) {
        var companyType = event.getEvent().getEventTrigger().getCompanyType();

        if (UserType.SYSTEM.equals(companyType)) {
            builder.senderType(SenderType.SYSTEM);
            return builder;
        }

        String userId = event.getEvent().getEventTrigger().getUserId();
        if (userId != null) {
            builder.senderType(SenderType.USER).senderId(userId);
            return builder;
        }

        builder.senderId(event.getEvent().getEventTrigger().getCompanyId());

        switch (event.getEvent().getEventTrigger().getCompanyType()) {
            case CUSTOMER -> builder.senderType(SenderType.CUSTOMER);
            case SUPPLIER -> builder.senderType(SenderType.SUPPLIER);
        }

        return builder;
    }

    @Override
    public void notify(final Notification notification) {
        this.notificationService.notify(notification);
    }

    private Object[] createSubjectReplacements(final Offer offer) {
        return new Object[] {
            getVesselName(offer.getVesselId()),
            offer.getOfferNumber(),
            getSupplierName(offer),
            offer.getBuyerReference(),
        };
    }

    private String getVesselName(final String vesselId) {
        return this.vesselApiRestService.getVessel(vesselId).getName();
    }

    private String getSupplierName(final Offer offer) {
        return this.supplierApiRestService.getSupplier(offer.getSupplierId()).getName();
    }

    private String getCustomerName(final Offer offer) {
        return this.customerApiRestService.getCustomer(offer.getCustomerId()).getName();
    }

    private Map<String, String> createReplacements(final Offer offer) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.putAll(
            this.webappReplacementService.create(OfferReplacementOfferMapper.create(offer), ReceiverType.CUSTOMER)
        );
        replacements.putAll(this.orderReplacementService.create(OfferReplacementOfferMapper.create(offer)));
        return replacements;
    }

    @Override
    public Object[] createSpotSubjectReplacements(final Offer offer) {
        return new Object[] {
            getVesselName(offer.getVesselId()),
            getSupplierName(offer),
            getSpotNumber(offer.getOrderId()),
        };
    }

    private String getSpotNumber(final String spotId) {
        return this.orderApiRestService.getOrder(spotId).getOrderNumber();
    }

    @Override
    public String[] createCancelMessageReplacements(final Offer offer) {
        return new String[] {
            this.orderReplacementSupportService.createCancelReasonReplacement(offer.getCancelReason()),
        };
    }

    @Override
    public Object[] createInvoicedSubjectReplacements(Offer offer) {
        return new Object[] {
            getVesselName(offer.getVesselId()),
            offer.getOfferNumber(),
            getCustomerName(offer),
            offer.getBuyerReference(),
        };
    }
}
