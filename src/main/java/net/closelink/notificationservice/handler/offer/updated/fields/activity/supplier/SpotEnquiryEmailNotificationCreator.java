package net.closelink.notificationservice.handler.offer.updated.fields.activity.supplier;

import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.replacement.subject.supplier.SupplierSubjectReplacementService;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class SpotEnquiryEmailNotificationCreator implements EmailNotificationCreator {

    private static final String SUPPLIER_ENQUIRY_SUBJECT_ORDER = "activitylog.subject.trade.supplier.enquiry";
    private static final String SUPPLIER_ENQUIRY_SUBJECT_ORDER_BUYER_REF =
        "activitylog.subject.trade.supplier.enquiryBuyerRef";
    private static final String SUPPLIER_ENQUIRY_TEMPLATE_ORDER = "Order_Supplier_Enquiry_Customer_Changes_Trade";

    private final SupplierSubjectReplacementService supplierSubjectReplacementService;

    @Override
    public Notification createNotification(Notification.NotificationBuilder notificationBuilder, Offer offer) {
        return notificationBuilder
            .subject(
                Strings.isNullOrEmpty(offer.getBuyerReference())
                    ? SUPPLIER_ENQUIRY_SUBJECT_ORDER
                    : SUPPLIER_ENQUIRY_SUBJECT_ORDER_BUYER_REF
            )
            .subjectReplacements(this.supplierSubjectReplacementService.createSubjectReplacements(offer))
            .template(SUPPLIER_ENQUIRY_TEMPLATE_ORDER)
            .groupMail(true)
            .sendMail(true)
            .build();
    }
}
