package net.closelink.notificationservice.handler.offer.updated.fields.handler.samplekit;

import net.closelink.cenqueue.objects.Samplekit;
import net.closelink.notificationservice.handler.offer.updated.fields.activity.BaseActivityLogService;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEventTypeHandler;
import net.closelink.notificationservice.support.EncodeSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SamplekitNameChangedHandler implements OfferUpdatedEventTypeHandler {

    private static final String ACTIVITYLOG = "activitylog.samplekitname";

    private final BaseActivityLogService activityLogService;

    @Autowired
    public SamplekitNameChangedHandler(final BaseActivityLogService activityLogService) {
        this.activityLogService = activityLogService;
    }

    @Override
    public void handle(final OfferUpdatedEvent orderUpdatedEvent) {
        final Samplekit newValue = EncodeSupport.encodeSamplekit(orderUpdatedEvent.getEventMessage().getNewValue());
        final Samplekit oldValue = EncodeSupport.encodeSamplekit(orderUpdatedEvent.getEventMessage().getOldValue());

        this.activityLogService.createActivityLog(
                orderUpdatedEvent,
                ACTIVITYLOG,
                new Object[] { oldValue.getName(), newValue.getName() }
            );
    }
}
