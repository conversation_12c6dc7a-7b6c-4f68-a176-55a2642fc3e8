package net.closelink.notificationservice.handler.offer.updated.state.common.delivered;

import de.tschumacher.simplestatemachine.domain.StateChange;
import de.tschumacher.simplestatemachine.handler.StateChangeHandler;
import net.closelink.cenqueue.types.EnquiryType;
import net.closelink.cenqueue.types.OfferState;
import net.closelink.notificationservice.handler.offer.updated.state.customer.StateChangeHandlerRegistry;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SupplierDeliveredHandler implements StateChangeHandler<OfferState, OfferUpdatedEvent> {

    private final StateChangeHandlerRegistry stateChangeHandlerRegistry;

    @Autowired
    public SupplierDeliveredHandler(GeneralDeliveredHandler generalDeliveredHandler) {
        this.stateChangeHandlerRegistry = makeStateChangeHandlerRegistry(generalDeliveredHandler);
    }

    private static StateChangeHandlerRegistry makeStateChangeHandlerRegistry(
        GeneralDeliveredHandler generalDeliveredHandler
    ) {
        var stateChangeHandlerRegistry = new StateChangeHandlerRegistry();

        stateChangeHandlerRegistry.put(EnquiryType.ASSIGNED, generalDeliveredHandler);
        stateChangeHandlerRegistry.put(EnquiryType.SPOT, generalDeliveredHandler);
        stateChangeHandlerRegistry.put(EnquiryType.FORWARDED, generalDeliveredHandler);
        return stateChangeHandlerRegistry;
    }

    @Override
    public OfferUpdatedEvent handle(StateChange<OfferState> stateChange, OfferUpdatedEvent offerUpdatedEvent) {
        var enquiryType = offerUpdatedEvent.getEventMessage().getOffer().getEnquiryType();

        var stateChangeHandler = stateChangeHandlerRegistry.get(enquiryType);

        return stateChangeHandler.handle(stateChange, offerUpdatedEvent);
    }
}
