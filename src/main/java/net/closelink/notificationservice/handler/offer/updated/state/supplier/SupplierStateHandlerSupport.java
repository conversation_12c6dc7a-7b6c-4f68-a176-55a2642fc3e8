package net.closelink.notificationservice.handler.offer.updated.state.supplier;

import net.closelink.cenqueue.objects.ApprovalRequest;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.handler.offer.updated.support.OfferUpdatedEvent;

public interface SupplierStateHandlerSupport {
    Notification.NotificationBuilder createNotificationBuilder(OfferUpdatedEvent event);

    void notify(Notification notification);

    Object[] createSpotSubjectReplacements(Offer offer);

    String[] createCancelMessageReplacements(Offer offer);

    Object[] createInvoicedSubjectReplacements(Offer offer);
}
