package net.closelink.notificationservice.handler.keyportcall;

import static net.closelink.notificationservice.util.DateUtilKt.formatDisplayDate;
import static net.closelink.notificationservice.util.DateUtilKt.parseOffsetDateTimeOrNull;

import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.keyportcall.KeyPortCallNotificationEventMessage;
import net.closelink.cenqueue.objects.KeyPortCall;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.replacement.offer.url.WebAppReplacementService;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.port.PortApiRestService;
import net.closelink.notificationservice.service.rest.port.domain.PortMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import net.closelink.notificationservice.support.RecipientsCreatorKt;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class KeyPortCallNotificationCreatedHandler implements EventHandler {

    private final WebAppReplacementService webappReplacementService;
    private final NotificationService notificationService;
    private final CustomerApiRestService customerApiRestService;
    private final VesselApiRestService vesselApiRestService;
    private final PortApiRestService portApiRestService;
    public static final String SUBJECT = "keyPortCallNotification.created.subject";

    @Override
    public void handle(Event event) {
        final KeyPortCallNotificationEventMessage eventMessage = createEventMessage(event);
        Notification notification = createNotification(eventMessage);

        notificationService.notify(notification);
    }

    private Notification createNotification(KeyPortCallNotificationEventMessage keyPortCallNotificationEventMessage) {
        String template = getTemplateName(keyPortCallNotificationEventMessage.getKeyPortCalls());

        VesselMessage vessel = vesselApiRestService.getVessel(keyPortCallNotificationEventMessage.getVesselId());

        CustomerMessage customer = customerApiRestService.getCustomer(
            keyPortCallNotificationEventMessage.getCustomerId()
        );

        List<PortMessage> ports = getPorts(keyPortCallNotificationEventMessage.getKeyPortCalls());

        Map<String, String> replacements = createMultipleKeyPortCallNotificationsReplacements(
            keyPortCallNotificationEventMessage,
            vessel,
            customer,
            ports
        );

        var recipients = RecipientsCreatorKt.createRecipientListForVesselNotification(
            vessel.getEmail(),
            vessel.getMailNotificationSettings().getKeyPortCallSettings()
        );

        var receiverType = recipients.isEmpty() ? ReceiverType.CUSTOMER : ReceiverType.CUSTOM;

        return Notification.builder()
            .sendMail(true)
            .subject(SUBJECT)
            .subjectReplacements(new Object[] { vessel.getName() })
            .template(template)
            .mailReplacements(replacements)
            .receiverType(receiverType)
            .recipients(recipients)
            .receiverId(keyPortCallNotificationEventMessage.getCustomerId())
            .senderType(SenderType.SYSTEM)
            .category(NotificationCategory.KEY_PORT_CALL_REMINDER)
            .build();
    }

    private String getTemplateName(List<KeyPortCall> keyPortCalls) {
        boolean anyKeyPortCallHasOpenOrders = keyPortCalls
            .stream()
            .anyMatch(keyPortCall -> keyPortCall.getOrderIds() != null && !keyPortCall.getOrderIds().isEmpty());

        return anyKeyPortCallHasOpenOrders ? "Customer_KeyPortCall_Alert_PendingOrder" : "Customer_KeyPortCall_Alert";
    }

    private Map<String, String> createCommonReplacements(VesselMessage vesselMessage, CustomerMessage customerMessage) {
        Map<String, String> vesselDetailPageUrlReplacements = webappReplacementService.createVesselDetailPage(
            vesselMessage.getId()
        );

        HashMap<String, String> replacements = new HashMap<>(vesselDetailPageUrlReplacements);
        replacements.put("customer_name", customerMessage.getName());
        replacements.put("vessel_name", vesselMessage.getName());

        return replacements;
    }

    private Map<String, String> createMultipleKeyPortCallNotificationsReplacements(
        KeyPortCallNotificationEventMessage keyPortCallNotificationEventMessage,
        VesselMessage vesselMessage,
        CustomerMessage customerMessage,
        List<PortMessage> portMessages
    ) {
        HashMap<String, PortMessage> portIdToPortMessage = new HashMap<>();
        portMessages.forEach(portMessage -> portIdToPortMessage.put(portMessage.getId(), portMessage));

        List<KeyPortCall> sortedKeyPorts = keyPortCallNotificationEventMessage
            .getKeyPortCalls()
            .stream()
            .sorted(Comparator.comparing(k -> parseOffsetDateTimeOrNull(k.getEta())))
            .toList();

        List<String> formattedStrings = sortedKeyPorts
            .stream()
            .map(keyPortCall -> {
                PortMessage portMessage = portIdToPortMessage.get(keyPortCall.getPortId());

                String formattedKeyPortCallInformation = formatKeyPortCallInformation(keyPortCall, portMessage);

                return String.format("<li>%s</li>", formattedKeyPortCallInformation);
            })
            .collect(Collectors.toList());

        String html = String.format("<ul>%s</ul>", String.join("", formattedStrings));

        Map<String, String> commonReplacements = createCommonReplacements(vesselMessage, customerMessage);

        final Map<String, String> replacements = new HashMap<>(commonReplacements);
        replacements.put("key_port_call_list", html);

        return replacements;
    }

    private List<PortMessage> getPorts(List<KeyPortCall> keyPortCalls) {
        List<String> portIds = keyPortCalls.stream().map(KeyPortCall::getPortId).collect(Collectors.toList());

        return portApiRestService.getPorts(portIds);
    }

    private String formatKeyPortCallInformation(KeyPortCall keyPortCall, PortMessage portMessage) {
        OffsetDateTime eta = parseOffsetDateTimeOrNull(keyPortCall.getEta());

        String baseString = String.format(
            "%s - %s - %s (%s)",
            formatDisplayDate(eta),
            portMessage.getName(),
            portMessage.getCountry().getName(),
            portMessage.getLocCode()
        );

        if (keyPortCall.getOrderIds() == null || keyPortCall.getOrderIds().isEmpty()) {
            return baseString;
        }

        String suffix = "(pending enquiry/order)";

        return String.join("", Arrays.asList("<span style='color: #0001fe'>", baseString, "<br />", suffix, "</span>"));
    }

    private KeyPortCallNotificationEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), KeyPortCallNotificationEventMessage.class);
    }
}
