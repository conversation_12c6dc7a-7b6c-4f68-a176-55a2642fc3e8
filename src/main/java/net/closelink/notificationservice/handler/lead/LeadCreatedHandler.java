package net.closelink.notificationservice.handler.lead;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.lead.LeadCreatedEventMessage;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.service.notification.NotificationService;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class LeadCreatedHandler implements EventHandler {

    public static final String TEMPLATE = "Lead_Created";
    public static final String SUBJECT = "leadCreated.subject";

    private final NotificationService notificationService;

    @Override
    public void handle(Event event) {
        final LeadCreatedEventMessage eventMessage = createEventMessage(event);
        Notification notification = createNotification(eventMessage);

        notificationService.notify(notification);
    }

    private Notification createNotification(LeadCreatedEventMessage leadCreatedEventMessage) {
        return Notification.builder()
            .sendMail(true)
            .subject(SUBJECT)
            .template(TEMPLATE)
            .mailReplacements(createReplacements(leadCreatedEventMessage))
            .receiverType(ReceiverType.SYSTEM)
            .senderType(SenderType.SYSTEM)
            .build();
    }

    private Map<String, String> createReplacements(final LeadCreatedEventMessage leadCreatedEventMessage) {
        final Map<String, String> replacements = new HashMap<>();

        replacements.put("NAME", leadCreatedEventMessage.getName());
        replacements.put("BUSINESS_EMAIL_ADDRESS", leadCreatedEventMessage.getBusinessEmailAddress());
        replacements.put("COMPANY_NAME", leadCreatedEventMessage.getCompanyName());

        String phoneNumber = getStringOrNotAvailable(leadCreatedEventMessage.getPhoneNumber());
        String message = getStringOrNotAvailable(leadCreatedEventMessage.getMessage());
        String referrer = getStringOrNotAvailable(leadCreatedEventMessage.getReferrer());

        replacements.put("PHONE_NUMBER", phoneNumber);
        replacements.put("USER_MESSAGE", message);
        replacements.put("REFERRER", referrer);

        return replacements;
    }

    private String getStringOrNotAvailable(String string) {
        return Optional.ofNullable(string).orElse("N/A");
    }

    private LeadCreatedEventMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), LeadCreatedEventMessage.class);
    }
}
