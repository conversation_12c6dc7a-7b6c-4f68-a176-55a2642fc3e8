package net.closelink.notificationservice.handler.passwordreset;

import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.passwordreset.PasswordResetRequestCreatedMessage;
import net.closelink.cenqueue.domainobject.passwordreset.PasswordResetRequestResetMessage;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.user.UserApiRestService;
import net.closelink.notificationservice.service.rest.user.domain.UserMessage;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class PasswordResetRequestResetHandler implements EventHandler {

    private UserApiRestService userApiRestService;
    private NotificationService notificationService;

    @Override
    public void handle(Event event) {
        PasswordResetRequestResetMessage passwordResetRequestCreatedMessage = createEventMessage(event);

        UserMessage user = userApiRestService.getUser(passwordResetRequestCreatedMessage.getUserId());

        Notification notification = Notification.builder()
            .receiverType(ReceiverType.USER)
            .senderType(SenderType.SYSTEM)
            .receiverId(user.getId())
            .sendMail(true)
            .hidden(true)
            .subject("passwordResetRequest.reset.subject")
            .template("Password_Changed_User")
            .mailReplacements(createReplacements(user))
            .build();

        notificationService.notify(notification);
    }

    private Map<String, String> createReplacements(final UserMessage user) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.put("user_first_name", user.getFirstname());
        replacements.put("user_last_name", user.getLastname());

        return replacements;
    }

    private PasswordResetRequestResetMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), PasswordResetRequestResetMessage.class);
    }
}
