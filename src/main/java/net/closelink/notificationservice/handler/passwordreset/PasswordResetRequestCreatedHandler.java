package net.closelink.notificationservice.handler.passwordreset;

import com.amazonaws.util.StringUtils;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainobject.passwordreset.PasswordResetRequestCreatedMessage;
import net.closelink.cenqueue.domainobject.vesselrequisition.VesselRequisitionImportedEventMessage;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.domainobject.Recipient;
import net.closelink.notificationservice.domainobject.SenderType;
import net.closelink.notificationservice.handler.EventHandler;
import net.closelink.notificationservice.service.notification.NotificationService;
import net.closelink.notificationservice.service.rest.user.UserApiRestService;
import net.closelink.notificationservice.service.rest.user.domain.UserMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class PasswordResetRequestCreatedHandler implements EventHandler {

    private UserApiRestService userApiRestService;
    private NotificationService notificationService;

    @Override
    public void handle(Event event) {
        PasswordResetRequestCreatedMessage passwordResetRequestCreatedMessage = createEventMessage(event);

        UserMessage user = userApiRestService.getUser(passwordResetRequestCreatedMessage.getUserId());

        Notification notification = Notification.builder()
            .receiverType(ReceiverType.USER)
            .senderType(SenderType.SYSTEM)
            .receiverId(user.getId())
            .sendMail(true)
            .hidden(true)
            .subject("passwordResetRequest.created.subject")
            .template("Password_Change_Request_User")
            .mailReplacements(createReplacements(user, passwordResetRequestCreatedMessage.getPasswordResetLink()))
            .build();

        notificationService.notify(notification);
    }

    private Map<String, String> createReplacements(final UserMessage user, final String passwordResetLink) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.put("user_first_name", user.getFirstname());
        replacements.put("user_last_name", user.getLastname());
        replacements.put("password_reset_link", passwordResetLink);

        return replacements;
    }

    private PasswordResetRequestCreatedMessage createEventMessage(final Event event) {
        return GsonCoder.encode(event.getRawMessage(), PasswordResetRequestCreatedMessage.class);
    }
}
