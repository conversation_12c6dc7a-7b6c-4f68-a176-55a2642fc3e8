package net.closelink.notificationservice.service.notification;

import io.micrometer.core.annotation.Timed;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.closelink.mapper.EnumMapper;
import net.closelink.notificationservice.domainobject.Attachment;
import net.closelink.notificationservice.domainobject.Notification;
import net.closelink.notificationservice.domainobject.Recipient;
import net.closelink.notificationservice.service.rest.message.MessageApiRestService;
import net.closelink.notificationservice.service.rest.message.domain.AttachmentMessage;
import net.closelink.notificationservice.service.rest.message.domain.MessageMessage;
import net.closelink.notificationservice.service.rest.message.domain.MessageReceiverType;
import net.closelink.notificationservice.service.rest.message.domain.MessageSenderType;
import net.closelink.notificationservice.service.rest.message.domain.RecipientMessage;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class DefaultNotificationService implements NotificationService {

    private static final Locale LOCALE = Locale.ENGLISH;

    private final MessageSource messageSource;
    private final MessageApiRestService messageApiRestService;
    private final NotificationPreferenceService notificationPreferenceService;

    @Timed("notificationservice_createMessage")
    @Override
    public void notify(Notification notification) {
        var shouldSendNotification = notificationPreferenceService.shouldSendNotification(
            notification.getReceiverType(),
            notification.getReceiverId(),
            notification.getCategory()
        );
        if (!shouldSendNotification) {
            log.info(
                "Notification ignored and not sent because it is disabled in the settings for receiver: {}",
                notification.getReceiverId()
            );
            return;
        }

        var message = buildMessage(notification);
        this.messageApiRestService.createMessage(message);
    }

    private MessageMessage buildMessage(Notification notification) {
        var message = new MessageMessage();

        if (notification.getMessage() != null) {
            message.setMessage(translate(notification.getMessage(), notification.getMessageReplacements()));
        }
        if (notification.getSubject() != null) {
            message.setSubject(translate(notification.getSubject(), notification.getSubjectReplacements()));
        }

        message.setReceiverId(notification.getReceiverId());
        message.setReceiverType(EnumMapper.map(MessageReceiverType.class, notification.getReceiverType()));
        message.setSenderType(EnumMapper.map(MessageSenderType.class, notification.getSenderType()));
        message.setSenderId(notification.getSenderId());
        message.setSendMail(notification.isSendMail());
        message.setGroupMail(notification.isGroupMail());
        message.setHidden(true);
        message.setTemplate(notification.getTemplate());
        message.setOfferId(notification.getOfferId());
        message.setOrderId(notification.getOrderId());
        message.setReplacements(notification.getMailReplacements());
        message.setSystemMessage(true);
        message.setCustomSenderEmailAddress(notification.getCustomSenderEmailAddress());
        message.setCustomSenderName(notification.getCustomSenderName());

        var attachments = notification.getAttachments();

        if (attachments != null && !attachments.isEmpty()) {
            message.setAttachments(mapAttachments(attachments));
        }

        var recipients = notification.getRecipients();

        if (recipients != null && !recipients.isEmpty()) {
            message.setRecipients(mapRecipient(recipients));
        }

        return message;
    }

    private List<AttachmentMessage> mapAttachments(List<Attachment> attachments) {
        return attachments
            .stream()
            .map(attachment ->
                AttachmentMessage.builder()
                    .name(attachment.getName())
                    .url(attachment.getUrl())
                    .fileId(attachment.getFileId())
                    .build()
            )
            .collect(Collectors.toList());
    }

    private List<RecipientMessage> mapRecipient(List<Recipient> recipients) {
        return recipients
            .stream()
            .map(recipient -> {
                RecipientMessage pojoRecipient = new RecipientMessage();
                pojoRecipient.setEmailAddress(recipient.getEmailAddress());
                pojoRecipient.setName(recipient.getName());
                pojoRecipient.setType(EnumMapper.map(RecipientMessage.Type.class, recipient.getType()));

                return pojoRecipient;
            })
            .collect(Collectors.toList());
    }

    private String translate(String message, Object[] messageReplacements) {
        return this.messageSource.getMessage(message, messageReplacements, LOCALE);
    }
}
