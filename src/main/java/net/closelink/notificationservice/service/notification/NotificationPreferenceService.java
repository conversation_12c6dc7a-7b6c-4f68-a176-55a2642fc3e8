package net.closelink.notificationservice.service.notification;

import java.util.HashMap;
import java.util.Optional;
import lombok.AllArgsConstructor;
import net.closelink.notificationservice.domainobject.NotificationCategory;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.service.rest.company.EmailCategorySettings;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class NotificationPreferenceService {

    private final SupplierApiRestService supplierApiRestService;
    private final CustomerApiRestService customerApiRestService;

    public boolean shouldSendNotification(
        ReceiverType receiverType,
        String receiverId,
        NotificationCategory notificationCategory
    ) {
        if (receiverType == ReceiverType.SUPPLIER) {
            var settings = supplierApiRestService.getSettings(receiverId);

            return isEnabled(notificationCategory, settings.getEmailCategorySettings());
        }

        if (receiverType == ReceiverType.CUSTOMER) {
            var settings = customerApiRestService.getSettings(receiverId);

            return isEnabled(notificationCategory, settings.getEmailCategorySettings());
        }

        return true;
    }

    private boolean isEnabled(NotificationCategory notificationCategory, EmailCategorySettings emailCategorySettings) {
        var notificationCategoryToBoolean = new HashMap<NotificationCategory, Boolean>();
        notificationCategoryToBoolean.put(NotificationCategory.OFFER_UPDATE, emailCategorySettings.isOfferUpdate());
        notificationCategoryToBoolean.put(
            NotificationCategory.SYSTEM_REMINDER,
            emailCategorySettings.isSystemReminder()
        );
        notificationCategoryToBoolean.put(
            NotificationCategory.OFFER_STATE_CHANGE,
            emailCategorySettings.isOfferStateChange()
        );
        notificationCategoryToBoolean.put(
            NotificationCategory.NEW_CHAT_MESSAGE,
            emailCategorySettings.isNewChatMessage()
        );
        notificationCategoryToBoolean.put(
            NotificationCategory.OPEN_ENQUIRY_REMINDER,
            emailCategorySettings.isOpenEnquiryReminder()
        );
        notificationCategoryToBoolean.put(
            NotificationCategory.ORDER_DELIVERY_REMINDER,
            emailCategorySettings.isOrderDeliveryReminder()
        );
        notificationCategoryToBoolean.put(
            NotificationCategory.KEY_PORT_CALL_REMINDER,
            emailCategorySettings.isKeyPortCallReminder()
        );
        notificationCategoryToBoolean.put(
            NotificationCategory.STOCK_WARNING_LEVEL_REMINDER,
            emailCategorySettings.isStockWarningLevelReminder()
        );
        notificationCategoryToBoolean.put(
            NotificationCategory.SAFETY_RESERVE_LEVEL_REMINDER,
            emailCategorySettings.isSafetyReserveLevelReminder()
        );

        var isEnabled = notificationCategoryToBoolean.get(notificationCategory);
        return Optional.ofNullable(isEnabled).orElse(true);
    }
}
