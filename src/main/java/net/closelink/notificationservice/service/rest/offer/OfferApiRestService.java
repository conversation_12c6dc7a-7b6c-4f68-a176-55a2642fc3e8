package net.closelink.notificationservice.service.rest.offer;

import net.closelink.notificationservice.properties.InternalApiProperties;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;
import net.closelink.restclients.RestClientFactory;
import net.closelink.restclients.RestServiceExecutor;
import org.springframework.stereotype.Service;
import retrofit2.Call;

@Service
public class OfferApiRestService {

    private final OfferApiRetroClient offerApiRetroClient;

    public OfferApiRestService(final InternalApiProperties properties) {
        offerApiRetroClient = RestClientFactory.buildRestClient(
            OfferApiRetroClient.class,
            properties.getOrderService().getBaseUrl(),
            properties.getAuthorization()
        );
    }

    public OfferMessage getOffer(final String offerId) {
        final Call<OfferMessage> call = this.offerApiRetroClient.getOffer(offerId);
        return RestServiceExecutor.executeCall(call);
    }
}
