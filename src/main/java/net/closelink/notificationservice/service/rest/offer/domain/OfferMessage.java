package net.closelink.notificationservice.service.rest.offer.domain;

import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OfferMessage {

    private String id;

    private String orderId;
    private String customerId;
    private String supplierId;
    private String enquiryType;
    private String state;

    private String vendorReference;
    private String offerNumber;
    private String cancelReason;
    private BigDecimal volume;
    private Money ppl;
    private Money total;
    private String updatedBy;
    private String supplyMode;
    private Long noticeDays;

    // order fields
    private String portId;
    private Long dateDelivery;
    private String agent;
    private String buyerReference;
    private String vesselId;
    private List<ItemMessage> items;
    private List<SurchargeMessage> surcharges;
    private List<SamplekitMessage> samplekits;
    private String type;
}
