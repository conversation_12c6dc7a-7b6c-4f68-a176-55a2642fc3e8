package net.closelink.notificationservice.service.rest.supplier;

import java.util.List;
import java.util.Set;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierMessage;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierSettingsResponseMessage;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Path;
import retrofit2.http.Query;

public interface SupplierApiRetroClient {
    @GET("v1/api/company/supplier")
    Call<List<SupplierMessage>> getSuppliers(@Query("supplierIds") Set<String> supplierIds);

    @GET("/v1/api/suppliers/{supplierId}/settings")
    Call<SupplierSettingsResponseMessage> getSettings(@Path("supplierId") String supplierId);
}
