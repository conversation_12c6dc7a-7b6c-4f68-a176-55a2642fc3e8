package net.closelink.notificationservice.service.rest.vessel.domain;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MailNotificationSettings {

    private NotificationSettings orderUpdateSettings;
    private NotificationSettings stockWarningSettings;
    private NotificationSettings safetyReserveSettings;
    private NotificationSettings keyPortCallSettings;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NotificationSettings {

        private boolean enabled;
        private boolean sendToVessel;
        private boolean sendToOthers;
        private List<String> recipients = new ArrayList<>();
    }
}
