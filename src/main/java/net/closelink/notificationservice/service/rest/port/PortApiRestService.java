package net.closelink.notificationservice.service.rest.port;

import java.util.List;
import net.closelink.notificationservice.properties.InternalApiProperties;
import net.closelink.notificationservice.service.rest.port.domain.PortMessage;
import net.closelink.restclients.RestClientFactory;
import net.closelink.restclients.RestServiceExecutor;
import org.springframework.stereotype.Service;
import retrofit2.Call;

@Service
public class PortApiRestService {

    private final CoreDataApiRetroClient coreDataApiRetroClient;

    public PortApiRestService(final InternalApiProperties properties) {
        coreDataApiRetroClient = RestClientFactory.buildRestClient(
            CoreDataApiRetroClient.class,
            properties.getCoredataService().getBaseUrl(),
            properties.getAuthorization()
        );
    }

    public PortMessage getPort(final String portId) {
        final Call<PortMessage> call = this.coreDataApiRetroClient.getPort(portId);
        return RestServiceExecutor.executeCall(call);
    }

    public List<PortMessage> getPorts(final List<String> portIds) {
        final Call<List<PortMessage>> call = this.coreDataApiRetroClient.getPorts(portIds);
        return RestServiceExecutor.executeCall(call);
    }
}
