package net.closelink.notificationservice.service.rest.order;

import net.closelink.notificationservice.properties.InternalApiProperties;
import net.closelink.notificationservice.service.rest.order.domain.OrderMessage;
import net.closelink.restclients.RestClientFactory;
import net.closelink.restclients.RestServiceExecutor;
import org.springframework.stereotype.Service;
import retrofit2.Call;

@Service
public class OrderApiRestService {

    private final OrderApiRetroClient apiOrderService;

    public OrderApiRestService(final InternalApiProperties properties) {
        apiOrderService = RestClientFactory.buildRestClient(
            OrderApiRetroClient.class,
            properties.getOrderService().getBaseUrl(),
            properties.getAuthorization()
        );
    }

    public OrderMessage getOrder(final String orderId) {
        final Call<OrderMessage> call = this.apiOrderService.getOrder(orderId);
        return RestServiceExecutor.executeCall(call);
    }
}
