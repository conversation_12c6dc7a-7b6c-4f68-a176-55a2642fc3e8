package net.closelink.notificationservice.service.rest.product;

import java.util.Set;
import net.closelink.notificationservice.properties.InternalApiProperties;
import net.closelink.notificationservice.service.rest.product.domain.ProductMessage;
import net.closelink.restclients.RestClientFactory;
import net.closelink.restclients.RestServiceExecutor;
import org.springframework.stereotype.Service;
import retrofit2.Call;

@Service
public class ProductApiRestService {

    private final ProductApiRetroClient productApiRetroClient;

    public ProductApiRestService(final InternalApiProperties properties) {
        productApiRetroClient = RestClientFactory.buildRestClient(
            ProductApiRetroClient.class,
            properties.getProductPriceService().getBaseUrl(),
            properties.getAuthorization()
        );
    }

    public ProductMessage getProduct(final String productId) {
        final Call<ProductMessage> call = this.productApiRetroClient.getProduct(productId);
        return RestServiceExecutor.executeCall(call);
    }

    public Set<ProductMessage> getProducts(final Set<String> productIds) {
        final Call<Set<ProductMessage>> call = this.productApiRetroClient.getProducts(productIds);
        return RestServiceExecutor.executeCall(call);
    }
}
