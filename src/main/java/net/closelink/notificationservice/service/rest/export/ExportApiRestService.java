package net.closelink.notificationservice.service.rest.export;

import net.closelink.notificationservice.properties.InternalApiProperties;
import net.closelink.notificationservice.service.rest.export.domain.OfferExportMessage;
import net.closelink.restclients.RestClientFactory;
import net.closelink.restclients.RestServiceExecutor;
import org.springframework.stereotype.Service;
import retrofit2.Call;

@Service
public class ExportApiRestService {

    private ExportApiRetroClient exportApiRetroClient;

    public ExportApiRestService(final InternalApiProperties properties) {
        exportApiRetroClient = RestClientFactory.buildRestClient(
            ExportApiRetroClient.class,
            properties.getExportService().getBaseUrl(),
            properties.getAuthorization()
        );
    }

    public OfferExportMessage getOfferExport(String offerId) {
        Call<OfferExportMessage> offerExportCall = this.exportApiRetroClient.getOfferExport(offerId);
        return RestServiceExecutor.executeCall(offerExportCall);
    }
}
