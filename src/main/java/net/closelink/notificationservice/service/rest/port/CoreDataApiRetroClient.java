package net.closelink.notificationservice.service.rest.port;

import java.util.List;
import net.closelink.notificationservice.service.rest.port.domain.PortMessage;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Path;
import retrofit2.http.Query;

public interface CoreDataApiRetroClient {
    @GET("v1/api/port/{portId}")
    Call<PortMessage> getPort(@Path("portId") String portId);

    @GET("v1/api/port/list")
    Call<List<PortMessage>> getPorts(@Query("portIds") List<String> portIds);

    @GET("v1/api/port/loccode")
    Call<PortMessage> portByLocCode(@Query("locCode") String locCode);
}
