package net.closelink.notificationservice.service.rest.customer;

import java.util.List;
import java.util.Set;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerSettingsResponseMessage;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierSettingsResponseMessage;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Path;
import retrofit2.http.Query;

public interface CustomerApiRetroClient {
    @GET("v1/api/company/customer")
    Call<List<CustomerMessage>> getCustomers(@Query("customerIds") Set<String> customerIds);

    @GET("/v1/api/customers/{customerId}/settings")
    Call<CustomerSettingsResponseMessage> getSettings(@Path("customerId") String customerId);
}
