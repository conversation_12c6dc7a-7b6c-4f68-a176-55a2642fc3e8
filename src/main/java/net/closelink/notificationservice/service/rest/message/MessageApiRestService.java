package net.closelink.notificationservice.service.rest.message;

import net.closelink.notificationservice.properties.InternalApiProperties;
import net.closelink.notificationservice.service.rest.message.domain.MessageMessage;
import net.closelink.restclients.RestClientFactory;
import net.closelink.restclients.RestServiceExecutor;
import org.springframework.stereotype.Service;
import retrofit2.Call;

@Service
public class MessageApiRestService {

    private final MessageApiRetroClient messageApiRetroClient;

    public MessageApiRestService(final InternalApiProperties properties) {
        messageApiRetroClient = RestClientFactory.buildRestClient(
            MessageApiRetroClient.class,
            properties.getMessageService().getBaseUrl(),
            properties.getAuthorization()
        );
    }

    public MessageMessage createMessage(final MessageMessage message) {
        final Call<MessageMessage> call = this.messageApiRetroClient.create(message);
        return RestServiceExecutor.executeCall(call);
    }

    public MessageMessage getMessage(final String messageId) {
        final Call<MessageMessage> call = this.messageApiRetroClient.get(messageId);
        return RestServiceExecutor.executeCall(call);
    }
}
