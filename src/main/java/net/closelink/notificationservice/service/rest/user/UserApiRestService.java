package net.closelink.notificationservice.service.rest.user;

import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import net.closelink.notificationservice.properties.InternalApiProperties;
import net.closelink.notificationservice.service.rest.user.domain.UserMessage;
import net.closelink.restclients.RestClientFactory;
import net.closelink.restclients.RestServiceExecutor;
import org.springframework.stereotype.Service;
import retrofit2.Call;

@Service
@Slf4j
public class UserApiRestService {

    private final UserApiRetroClient userApiRetroClient;

    private static final UserMessage UNKNOWN_USER = UserMessage.builder().firstname("UNKNOWN").lastname("USER").build();

    public UserApiRestService(final InternalApiProperties properties) {
        userApiRetroClient = RestClientFactory.buildRestClient(
            UserApiRetroClient.class,
            properties.getUserService().getBaseUrl(),
            properties.getAuthorization()
        );
    }

    public List<UserMessage> getUsers(final Set<String> userIds) {
        final Call<List<UserMessage>> call = this.userApiRetroClient.getUser(userIds);
        return RestServiceExecutor.executeCall(call);
    }

    public UserMessage getUser(final String userId) {
        final List<UserMessage> users = getUsers(Set.of(userId));
        if (users.isEmpty()) {
            log.error("User (%s) not found.".formatted(userId));
            return UNKNOWN_USER;
        }
        return users.get(0);
    }
}
