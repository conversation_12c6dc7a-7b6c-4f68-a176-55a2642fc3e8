package net.closelink.notificationservice.service.rest.vesseltank;

import java.util.List;
import net.closelink.notificationservice.properties.InternalApiProperties;
import net.closelink.restclients.RestClientFactory;
import net.closelink.restclients.RestServiceExecutor;
import org.springframework.stereotype.Service;
import retrofit2.Call;

@Service
public class VesselTankApiRestService {

    private final VesselTankApiRetroClient vesselTankApiRetroClient;

    public VesselTankApiRestService(final InternalApiProperties properties) {
        vesselTankApiRetroClient = RestClientFactory.buildRestClient(
            VesselTankApiRetroClient.class,
            properties.getVesselTankService().getBaseUrl(),
            properties.getAuthorization()
        );
    }

    public List<VesselTanksMessage.VesselTankMessage> getVesselTanks(final List<String> vesselTankIds) {
        final Call<VesselTanksMessage> call = this.vesselTankApiRetroClient.getVesselTanks(vesselTankIds);
        return RestServiceExecutor.executeCall(call).getVesselTanks();
    }
}
