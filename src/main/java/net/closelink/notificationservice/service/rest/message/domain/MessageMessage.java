package net.closelink.notificationservice.service.rest.message.domain;

import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class MessageMessage {

    private String id;
    private Long dateCreated;
    private Long dateUpdated;

    private String receiverId;
    private String senderId;
    private String orderId;
    private String offerId;
    private String subject;
    private String message;
    private String template;
    private boolean sendMail;
    private boolean groupMail;
    private Map<String, String> replacements;
    private boolean read;
    private MessageSenderType senderType;
    private MessageReceiverType receiverType;
    private boolean hidden;
    private boolean systemMessage;
    private List<AttachmentMessage> attachments;
    private List<RecipientMessage> recipients;
    private String customSenderEmailAddress;
    private String customSenderName;
}
