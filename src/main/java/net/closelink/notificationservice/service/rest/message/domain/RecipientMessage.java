package net.closelink.notificationservice.service.rest.message.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecipientMessage {

    /**
     * <p>The Recipient type (To, Cc, Bcc, ...)
     */
    public enum Type {
        TO,
        BCC,
        CC,
    }

    private String emailAddress;
    private String name;

    @Builder.Default
    private Type type = Type.TO;
}
