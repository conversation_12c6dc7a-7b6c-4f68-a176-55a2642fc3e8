package net.closelink.notificationservice.service.rest.vesseltank;

import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class VesselTanksMessage {

    private List<VesselTankMessage> vesselTanks;

    @Data
    @Builder
    public static class VesselTankMessage {

        private String id;
        private String name;
        private String category;
        private String defaultProductId;
    }
}
