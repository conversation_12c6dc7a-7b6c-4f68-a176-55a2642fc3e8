package net.closelink.notificationservice.service.rest.coredata;

import java.util.HashMap;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class EnumMessage {

    private HashMap<String, DetailEnumMessage> vesselTankCategory;
    private HashMap<String, DetailEnumMessage> surchargeType;
    private HashMap<String, DetailEnumMessage> productGroup;
    private HashMap<String, DetailEnumMessage> paymentTermReference;

    @Data
    @Builder
    public static class DetailEnumMessage {

        private String humanReadableValue;
    }
}
