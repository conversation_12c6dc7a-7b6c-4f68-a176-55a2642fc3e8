package net.closelink.notificationservice.service.rest.vessel;

import net.closelink.notificationservice.properties.InternalApiProperties;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import net.closelink.restclients.RestClientFactory;
import net.closelink.restclients.RestServiceExecutor;
import org.springframework.stereotype.Service;
import retrofit2.Call;

@Service
public class VesselApiRestService {

    private final VesselApiRetroClient vesselApiRetroClient;

    public VesselApiRestService(final InternalApiProperties properties) {
        vesselApiRetroClient = RestClientFactory.buildRestClient(
            VesselApiRetroClient.class,
            properties.getVesselService().getBaseUrl(),
            properties.getAuthorization()
        );
    }

    public VesselMessage getVessel(final String vesselId) {
        final Call<VesselMessage> call = this.vesselApiRetroClient.getVessel(vesselId);
        return RestServiceExecutor.executeCall(call);
    }
}
