package net.closelink.notificationservice.service.rest.product;

import java.util.Set;
import net.closelink.notificationservice.service.rest.product.domain.ProductMessage;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Path;
import retrofit2.http.Query;

public interface ProductApiRetroClient {
    @GET("v1/api/product/{id}")
    Call<ProductMessage> getProduct(@Path("id") String id);

    @GET("v1/api/product/list")
    Call<Set<ProductMessage>> getProducts(@Query("productIds") Set<String> ids);
}
