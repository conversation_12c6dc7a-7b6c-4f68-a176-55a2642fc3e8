package net.closelink.notificationservice.service.rest.company;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class EmailCategorySettings {

    private boolean offerUpdate;
    private boolean newChatMessage;
    private boolean offerStateChange;
    private boolean systemReminder;
    private boolean stockWarningLevelReminder;
    private boolean safetyReserveLevelReminder;
    private boolean keyPortCallReminder;
    private boolean orderDeliveryReminder;
    private boolean openEnquiryReminder;
}
