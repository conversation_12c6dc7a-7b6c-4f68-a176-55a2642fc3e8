package net.closelink.notificationservice.service.rest.user;

import java.util.List;
import java.util.Set;
import net.closelink.notificationservice.service.rest.user.domain.UserMessage;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

public interface UserApiRetroClient {
    @GET("v1/api/user")
    Call<List<UserMessage>> getUser(@Query("userIds") Set<String> userIds);
}
