package net.closelink.notificationservice.service.rest.customer;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import net.closelink.notificationservice.properties.InternalApiProperties;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerSettingsResponseMessage;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierSettingsResponseMessage;
import net.closelink.restclients.RestClientFactory;
import net.closelink.restclients.RestServiceExecutor;
import net.closelink.restclients.exception.RestServiceException;
import org.springframework.stereotype.Service;
import retrofit2.Call;

@Service
public class CustomerApiRestService {

    private final CustomerApiRetroClient customerApiRetroClient;

    public CustomerApiRestService(final InternalApiProperties properties) {
        customerApiRetroClient = RestClientFactory.buildRestClient(
            CustomerApiRetroClient.class,
            properties.getCompanyService().getBaseUrl(),
            properties.getAuthorization()
        );
    }

    public List<CustomerMessage> getCustomers(final Set<String> customerIds) {
        final Call<List<CustomerMessage>> call = this.customerApiRetroClient.getCustomers(customerIds);
        return RestServiceExecutor.executeCall(call);
    }

    public CustomerMessage getCustomer(final String customerId) {
        final List<CustomerMessage> customers = getCustomers(
            Stream.of(customerId).collect(Collectors.toCollection(HashSet::new))
        );
        if (customers.size() <= 0) {
            throw new RestServiceException(String.format("Customer(%s) not found.", customerId));
        }
        return customers.get(0);
    }

    public CustomerSettingsResponseMessage getSettings(String customerId) {
        return RestServiceExecutor.executeCall(this.customerApiRetroClient.getSettings(customerId));
    }
}
