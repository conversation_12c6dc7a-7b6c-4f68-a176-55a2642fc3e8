package net.closelink.notificationservice.service.rest.supplier;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import net.closelink.notificationservice.properties.InternalApiProperties;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierMessage;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierSettingsResponseMessage;
import net.closelink.restclients.RestClientFactory;
import net.closelink.restclients.RestServiceExecutor;
import net.closelink.restclients.exception.RestServiceException;
import org.springframework.stereotype.Service;
import retrofit2.Call;

@Service
public class SupplierApiRestService {

    private final SupplierApiRetroClient supplierApiRetroClient;

    public SupplierApiRestService(final InternalApiProperties properties) {
        supplierApiRetroClient = RestClientFactory.buildRestClient(
            SupplierApiRetroClient.class,
            properties.getCompanyService().getBaseUrl(),
            properties.getAuthorization()
        );
    }

    public List<SupplierMessage> getSuppliers(final Set<String> supplierIds) {
        final Call<List<SupplierMessage>> call = this.supplierApiRetroClient.getSuppliers(supplierIds);
        return RestServiceExecutor.executeCall(call);
    }

    public SupplierMessage getSupplier(final String supplierId) {
        final List<SupplierMessage> suppliers = getSuppliers(
            Stream.of(supplierId).collect(Collectors.toCollection(HashSet::new))
        );
        if (suppliers.size() <= 0) {
            throw new RestServiceException(String.format("Supplier(%s) not found.", supplierId));
        }
        return suppliers.get(0);
    }

    public SupplierSettingsResponseMessage getSettings(String supplierId) {
        return RestServiceExecutor.executeCall(this.supplierApiRetroClient.getSettings(supplierId));
    }
}
