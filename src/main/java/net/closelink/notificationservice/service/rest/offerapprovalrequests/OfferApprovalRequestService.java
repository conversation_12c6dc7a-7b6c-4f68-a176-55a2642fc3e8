package net.closelink.notificationservice.service.rest.offerapprovalrequests;

import java.util.Collections;
import java.util.List;
import net.closelink.notificationservice.properties.InternalApiProperties;
import net.closelink.restclients.RestClientFactory;
import net.closelink.restclients.RestServiceExecutor;
import org.springframework.stereotype.Service;
import retrofit2.Call;

@Service
public class OfferApprovalRequestService {

    private final OfferApprovalRequestServiceClient offerApprovalRequestServiceClient;

    public OfferApprovalRequestService(final InternalApiProperties properties) {
        offerApprovalRequestServiceClient = RestClientFactory.buildRestClient(
            OfferApprovalRequestServiceClient.class,
            properties.getOfferApprovalRequestService().getBaseUrl(),
            properties.getAuthorization()
        );
    }

    public List<OfferApprovalRequestsMessage.ApprovalRequestMessage> getApprovalRequestsForOfferId(String offerId) {
        Call<OfferApprovalRequestsMessage> call = offerApprovalRequestServiceClient.getApprovalRequestsForOfferIds(
            Collections.singletonList(offerId)
        );

        return RestServiceExecutor.executeCall(call).getApprovalRequests();
    }
}
