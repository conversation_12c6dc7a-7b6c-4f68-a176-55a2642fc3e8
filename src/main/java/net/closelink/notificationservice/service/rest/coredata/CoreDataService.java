package net.closelink.notificationservice.service.rest.coredata;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import net.closelink.notificationservice.properties.RestProperties;
import net.closelink.restclients.RestClientFactory;
import net.closelink.restclients.RestServiceExecutor;
import org.springframework.stereotype.Service;
import retrofit2.Call;

@Service
public class CoreDataService {

    private final CoreDataServiceClient coreDataServiceClient;

    public CoreDataService(RestProperties restProperties) {
        coreDataServiceClient = RestClientFactory.buildRestClient(
            CoreDataServiceClient.class,
            restProperties.getCoredataService().getBaseUrl()
        );
    }

    public String getHumanReadableValueForVesselTankCategory(String enumValue) {
        Call<EnumMessage> call = coreDataServiceClient.getEnums();
        EnumMessage enumMessage = RestServiceExecutor.executeCall(call);

        EnumMessage.DetailEnumMessage detailEnumMessage = enumMessage.getVesselTankCategory().get(enumValue);

        return getStringOrNullValue(detailEnumMessage);
    }

    public String getHumanReadableValueForProductGroup(String enumValue) {
        Call<EnumMessage> call = coreDataServiceClient.getEnums();
        EnumMessage enumMessage = RestServiceExecutor.executeCall(call);

        EnumMessage.DetailEnumMessage detailEnumMessage = enumMessage.getProductGroup().get(enumValue);

        return getStringOrNullValue(detailEnumMessage);
    }

    public Map<String, EnumMessage.DetailEnumMessage> getHumanReadableValueForPaymentTermReference() {
        Call<EnumMessage> call = coreDataServiceClient.getEnums();
        EnumMessage enumMessage = RestServiceExecutor.executeCall(call);

        return enumMessage.getPaymentTermReference();
    }

    private String getStringOrNullValue(EnumMessage.DetailEnumMessage detailEnumMessage) {
        return Optional.ofNullable(detailEnumMessage)
            .map(EnumMessage.DetailEnumMessage::getHumanReadableValue)
            .orElse(null);
    }

    public HashMap<String, EnumMessage.DetailEnumMessage> getHumanReadableValueForSurchargeTypeMessage() {
        Call<EnumMessage> call = coreDataServiceClient.getEnums();
        EnumMessage enumMessage = RestServiceExecutor.executeCall(call);

        return enumMessage.getSurchargeType();
    }
}
