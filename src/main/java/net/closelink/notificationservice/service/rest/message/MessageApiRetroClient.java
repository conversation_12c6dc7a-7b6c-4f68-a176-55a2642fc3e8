package net.closelink.notificationservice.service.rest.message;

import net.closelink.notificationservice.service.rest.message.domain.MessageMessage;
import retrofit2.Call;
import retrofit2.http.*;

public interface MessageApiRetroClient {
    @POST("v1/api/message")
    Call<MessageMessage> create(@Body MessageMessage message);

    @PUT("v1/api/message/{messageId}")
    Call<MessageMessage> update(@Path(value = "messageId") String messageId, @Body MessageMessage message);

    @GET("v1/api/message/{messageId}")
    Call<MessageMessage> get(@Path(value = "messageId") String messageId);
}
