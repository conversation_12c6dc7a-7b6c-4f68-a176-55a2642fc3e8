package net.closelink.notificationservice.service.cenqueue;

import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import net.closelink.cenqueue.consumer.CENQHandler;
import net.closelink.cenqueue.domainobject.Event;
import net.closelink.cenqueue.domainvalue.EventType;
import net.closelink.notificationservice.exception.NoHandlerFoundException;
import net.closelink.notificationservice.handler.EventHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class NotificationCENQHandler implements CENQHandler {

    private final EventHandlerRegistry handlerRegistry;

    @Autowired
    public NotificationCENQHandler(final EventHandlerRegistry handlerRegistry) {
        super();
        this.handlerRegistry = handlerRegistry;
    }

    @Timed("notificationservice_handleCenqEvent")
    @Override
    public void handle(final Event event) {
        log.info("Handle Event({})", event);
        try {
            if (event.getEventType() == null) {
                log.warn("Event type is unknown for Event({}). Skipped processing.", event);
                return;
            }
            findHandler(event.getEventType()).handle(event);
        } catch (final NoHandlerFoundException e) {
            log.warn(e.getMessage());
        }
    }

    private EventHandler findHandler(final EventType eventType) {
        return this.handlerRegistry.get(eventType);
    }
}
