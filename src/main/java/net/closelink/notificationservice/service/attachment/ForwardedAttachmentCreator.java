package net.closelink.notificationservice.service.attachment;

import static net.closelink.notificationservice.util.DateUtilKt.formatDisplayDate;
import static net.closelink.notificationservice.util.DateUtilKt.fromEpochMillisOrNull;

import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.domainobject.Attachment;
import net.closelink.notificationservice.service.rest.export.ExportApiRestService;
import net.closelink.notificationservice.service.rest.export.domain.OfferExportMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ForwardedAttachmentCreator {

    private ExportApiRestService exportApiRestService;
    private VesselApiRestService vesselApiRestService;

    public Attachment createAttachment(Offer offer, String filename) {
        OfferExportMessage offerExport = getOfferExport(offer.getId());
        VesselMessage vessel = getVessel(offer.getVesselId());
        String formattedDate = formatDate(offer);

        String attachmentName = createAttachmentName(filename, vessel.getName(), formattedDate);

        return Attachment.builder().name(attachmentName).url(offerExport.getUrl()).build();
    }

    private String createAttachmentName(String filename, String vesselName, String formattedDate) {
        return String.format(filename, vesselName, formattedDate);
    }

    private String formatDate(Offer offer) {
        return formatDisplayDate(fromEpochMillisOrNull(offer.getDateDelivery()));
    }

    private OfferExportMessage getOfferExport(String offerId) {
        return exportApiRestService.getOfferExport(offerId);
    }

    private VesselMessage getVessel(String vesselId) {
        return vesselApiRestService.getVessel(vesselId);
    }
}
