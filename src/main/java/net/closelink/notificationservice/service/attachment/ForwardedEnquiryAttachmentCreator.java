package net.closelink.notificationservice.service.attachment;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.domainobject.Attachment;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ForwardedEnquiryAttachmentCreator {

    private final ForwardedAttachmentCreator forwardedAttachmentCreator;

    public List<Attachment> createAttachmentForOffer(Offer offer) {
        var enquiryAttachment = List.of(forwardedAttachmentCreator.createAttachment(offer, "RFQ %s %s.pdf"));
        if (offer.getFileIds() == null) {
            return enquiryAttachment;
        }

        var offerAttachments = offer.getFileIds().stream().map(fileId -> Attachment.builder().fileId(fileId).build());
        return Stream.concat(enquiryAttachment.stream(), offerAttachments).collect(Collectors.toList());
    }
}
