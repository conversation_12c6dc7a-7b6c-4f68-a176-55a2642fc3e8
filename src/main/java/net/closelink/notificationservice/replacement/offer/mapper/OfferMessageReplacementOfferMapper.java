package net.closelink.notificationservice.replacement.offer.mapper;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

import static net.closelink.notificationservice.util.DateUtilKt.fromEpochMillisOrNull;
import java.util.List;
import java.util.stream.Collectors;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementItem;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementMoney;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementOffer;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementSamplekit;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementSurcharge;
import net.closelink.notificationservice.service.rest.offer.domain.ItemMessage;
import net.closelink.notificationservice.service.rest.offer.domain.Money;
import net.closelink.notificationservice.service.rest.offer.domain.OfferMessage;
import net.closelink.notificationservice.service.rest.offer.domain.SamplekitMessage;
import net.closelink.notificationservice.service.rest.offer.domain.SurchargeMessage;

public class OfferMessageReplacementOfferMapper {

    public static ReplacementOffer create(final OfferMessage offerMessage) {
        ReplacementOffer replacementOffer = new ReplacementOffer();

        replacementOffer.setId(offerMessage.getId());
        replacementOffer.setDateCreated(null);

        replacementOffer.setOrderId(offerMessage.getOrderId());
        replacementOffer.setCustomerId(offerMessage.getCustomerId());
        replacementOffer.setSupplierId(offerMessage.getSupplierId());
        replacementOffer.setState(offerMessage.getState());
        replacementOffer.setEnquiryType(offerMessage.getEnquiryType());

        replacementOffer.setVendorReference(offerMessage.getVendorReference());
        replacementOffer.setOfferNumber(offerMessage.getOfferNumber());
        replacementOffer.setCancelReason(offerMessage.getCancelReason());
        replacementOffer.setVolume(offerMessage.getVolume());
        replacementOffer.setPpl(createMoney(offerMessage.getPpl()));
        replacementOffer.setTotal(createMoney(offerMessage.getTotal()));
        replacementOffer.setUpdatedBy(offerMessage.getUpdatedBy());
        replacementOffer.setSupplyMode(offerMessage.getSupplyMode());
        replacementOffer.setNoticeDays(offerMessage.getNoticeDays());

        replacementOffer.setPortId(offerMessage.getPortId());
        replacementOffer.setDateDelivery(
            offerMessage.getDateDelivery() != null
                ? Instant.ofEpochMilli(offerMessage.getDateDelivery()).atOffset(ZoneOffset.UTC)
                : null
        );
        replacementOffer.setAgent(offerMessage.getAgent());
        replacementOffer.setBuyerReference(offerMessage.getBuyerReference());
        replacementOffer.setVesselId(offerMessage.getVesselId());
        replacementOffer.setItems(createReplacementItems(offerMessage.getItems()));
        replacementOffer.setSurcharges(createReplacementSurcharges(offerMessage.getSurcharges()));
        replacementOffer.setSamplekits(createReplacementSamplekits(offerMessage.getSamplekits()));

        return replacementOffer;
    }

    private static List<ReplacementSamplekit> createReplacementSamplekits(
        final List<SamplekitMessage> samplekitMessages
    ) {
        if (samplekitMessages == null) return null;

        return samplekitMessages
            .stream()
            .map(OfferMessageReplacementOfferMapper::createReplacementSamplekit)
            .collect(Collectors.toList());
    }

    private static ReplacementSamplekit createReplacementSamplekit(final SamplekitMessage samplekitMessage) {
        if (samplekitMessage == null) return null;

        ReplacementSamplekit replacementSamplekit = new ReplacementSamplekit();
        replacementSamplekit.setName(samplekitMessage.getName());
        replacementSamplekit.setQuantity(samplekitMessage.getQuantity());
        replacementSamplekit.setValue(createMoney(samplekitMessage.getValue()));
        return replacementSamplekit;
    }

    private static List<ReplacementSurcharge> createReplacementSurcharges(final List<SurchargeMessage> surcharges) {
        if (surcharges == null) return null;

        return surcharges
            .stream()
            .map(OfferMessageReplacementOfferMapper::createReplacementSurcharge)
            .collect(Collectors.toList());
    }

    private static ReplacementSurcharge createReplacementSurcharge(final SurchargeMessage surchargeMessage) {
        if (surchargeMessage == null) return null;

        ReplacementSurcharge replacementSurcharge = new ReplacementSurcharge();
        replacementSurcharge.setName(surchargeMessage.getName());
        replacementSurcharge.setValue(createMoney(surchargeMessage.getValue()));
        return replacementSurcharge;
    }

    private static List<ReplacementItem> createReplacementItems(final List<ItemMessage> items) {
        if (items == null) return null;

        return items
            .stream()
            .map(OfferMessageReplacementOfferMapper::createReplacementItem)
            .collect(Collectors.toList());
    }

    private static ReplacementItem createReplacementItem(final ItemMessage itemMessage) {
        if (itemMessage == null) return null;

        ReplacementItem replacementItem = new ReplacementItem();
        replacementItem.setProductId(itemMessage.getProductId());
        replacementItem.setPrice(createMoney(itemMessage.getPrice()));
        replacementItem.setPackType(itemMessage.getPackType());
        replacementItem.setUnitSize(itemMessage.getUnitSize());
        replacementItem.setUnits(itemMessage.getUnits());
        replacementItem.setUnit(itemMessage.getUnit());
        replacementItem.setVolume(itemMessage.getVolume());
        replacementItem.setTotal(createMoney(itemMessage.getTotal()));
        return replacementItem;
    }

    private static ReplacementMoney createMoney(final Money money) {
        if (money == null) return null;
        return ReplacementMoney.builder().currency(money.getCurrency()).value(money.getValue()).build();
    }
}
