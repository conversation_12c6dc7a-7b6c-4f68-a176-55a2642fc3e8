package net.closelink.notificationservice.replacement.offer.url;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import net.closelink.notificationservice.domainobject.ReceiverType;
import net.closelink.notificationservice.properties.WebappProperties;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementOffer;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class WebAppReplacementService {

    private static final String URL_KEY = "webapp_url";
    private final WebappProperties properties;

    public Map<String, String> create(final ReplacementOffer offer, final ReceiverType messageReceiverType) {
        switch (messageReceiverType) {
            case SUPPLIER:
                return createSupplierUrl(offer);
            case CUSTOMER:
                return createCustomerUrl(offer);
            default:
                break;
        }
        return null;
    }

    public Map<String, String> create(final String orderId) {
        return createReplacements(createWebappOrderUrl(orderId));
    }

    public Map<String, String> createDraft(final String orderId) {
        return createReplacements(createWebappDraftOrderUrl(orderId));
    }

    public Map<String, String> createVesselDetailPage(final String vesselId) {
        return createReplacements(createWebappVesselDetailUrl(vesselId));
    }

    private String createWebappDraftOrderUrl(final String orderId) {
        return this.properties.getDraftOrderUrl() + orderId;
    }

    private Map<String, String> createCustomerUrl(final ReplacementOffer offer) {
        String url;
        if (!isInOrderState(offer.getState())) {
            url = createWebappOrderUrl(offer.getOrderId());
        } else {
            url = createWebappOfferUrl(offer.getId());
        }

        return createReplacements(url);
    }

    private Map<String, String> createSupplierUrl(final ReplacementOffer offer) {
        String webAppUrl = createWebappOfferUrl(offer.getId());
        return createReplacements(webAppUrl);
    }

    private String createWebappOrderUrl(final String orderId) {
        return this.properties.getOrderUrl() + orderId;
    }

    private String createWebappOfferUrl(final String orderId) {
        return this.properties.getOfferUrl() + orderId;
    }

    private String createWebappVesselDetailUrl(String vesselId) {
        return this.properties.getVesselDetailUrl() + vesselId;
    }

    private Map<String, String> createReplacements(final String url) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.put(URL_KEY, url);
        return replacements;
    }

    private boolean isInOrderState(final String offerState) {
        return Arrays.asList(
            "ORDER",
            "ADJUSTED",
            "CONFIRMED",
            "ACKNOWLEDGED",
            "CANCELED",
            "DELIVERED",
            "DELIVERY_CONFIRMED",
            "INVOICED"
        ).contains(offerState);
    }
}
