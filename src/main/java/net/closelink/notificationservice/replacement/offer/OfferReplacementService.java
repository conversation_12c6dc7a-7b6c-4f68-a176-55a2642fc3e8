package net.closelink.notificationservice.replacement.offer;

import java.util.List;
import java.util.Map;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementItem;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementOffer;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementSamplekit;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementSurcharge;

public interface OfferReplacementService {
    Map<String, String> create(ReplacementOffer offer);

    List<Map<String, String>> createSurchargeList(List<ReplacementSurcharge> surcharges);

    List<Map<String, String>> createSamplekitList(List<ReplacementSamplekit> samplekits);

    List<Map<String, String>> createItemsList(List<ReplacementItem> items);
}
