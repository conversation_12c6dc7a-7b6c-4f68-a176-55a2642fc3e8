package net.closelink.notificationservice.replacement.offer.mapper;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.stream.Collectors;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.objects.Surcharge;
import net.closelink.notificationservice.replacement.DomainReplacementMapper;
import net.closelink.notificationservice.replacement.offer.domain.*;

public class OfferReplacementOfferMapper extends DomainReplacementMapper {

    public static ReplacementOffer create(final Offer offerMessage) {
        ReplacementOffer replacementOffer = new ReplacementOffer();

        replacementOffer.setId(offerMessage.getId());
        replacementOffer.setDateCreated(
            offerMessage.getDateCreated() != null ? OffsetDateTime.parse(offerMessage.getDateCreated()) : null
        );

        replacementOffer.setOrderId(offerMessage.getOrderId());
        replacementOffer.setCustomerId(offerMessage.getCustomerId());
        replacementOffer.setSupplierId(offerMessage.getSupplierId());
        replacementOffer.setState(createEnumString(offerMessage.getState()));
        replacementOffer.setEnquiryType(createEnumString(offerMessage.getEnquiryType()));

        replacementOffer.setVendorReference(offerMessage.getVendorReference());
        replacementOffer.setOfferNumber(offerMessage.getOfferNumber());
        replacementOffer.setCancelReason(offerMessage.getCancelReason());
        replacementOffer.setVolume(offerMessage.getVolume());
        replacementOffer.setPpl(createMoney(offerMessage.getPpl()));
        replacementOffer.setTotal(createMoney(offerMessage.getTotal()));
        replacementOffer.setUpdatedBy(offerMessage.getUpdatedBy());
        replacementOffer.setSupplyMode(offerMessage.getSupplyMode());
        replacementOffer.setNoticeDays(offerMessage.getNoticeDays());

        replacementOffer.setPortId(offerMessage.getPortId());
        replacementOffer.setDateDelivery(
            offerMessage.getDateDelivery() != null
                ? Instant.ofEpochMilli(offerMessage.getDateDelivery()).atOffset(ZoneOffset.UTC)
                : null
        );
        replacementOffer.setAgent(offerMessage.getAgent());
        replacementOffer.setBuyerReference(offerMessage.getBuyerReference());
        replacementOffer.setVesselId(offerMessage.getVesselId());
        replacementOffer.setItems(createReplacementItems(offerMessage.getItems()));
        replacementOffer.setSurcharges(createReplacementSurcharges(offerMessage.getSurcharges()));
        replacementOffer.setSamplekits(createReplacementSamplekits(offerMessage.getSamplekits()));

        return replacementOffer;
    }

    private static List<ReplacementSurcharge> createReplacementSurcharges(final List<Surcharge> surcharges) {
        if (surcharges == null) return null;

        return surcharges
            .stream()
            .map(OfferReplacementOfferMapper::createReplacementSurcharge)
            .collect(Collectors.toList());
    }

    private static ReplacementSurcharge createReplacementSurcharge(final Surcharge surcharge) {
        if (surcharge == null) return null;

        ReplacementSurcharge replacementSurcharge = new ReplacementSurcharge();
        replacementSurcharge.setName(surcharge.getName());
        replacementSurcharge.setValue(createMoney(surcharge.getValue()));
        return replacementSurcharge;
    }
}
