package net.closelink.notificationservice.replacement.offer.cancel;

import java.util.Locale;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

@Service
public class DefaultOfferReplacementSupportService implements OfferReplacementSupportService {

    private static final String CANCEL_REASON_NOT_AVAILABLE = "cancel_reason_not_available";
    private final MessageSource messageSource;

    @Autowired
    public DefaultOfferReplacementSupportService(final MessageSource messageSource) {
        super();
        this.messageSource = messageSource;
    }

    @Override
    public String createCancelReasonReplacement(final String cancelReason) {
        if (cancelReason == null) {
            return this.messageSource.getMessage(CANCEL_REASON_NOT_AVAILABLE, null, Locale.ENGLISH);
        }
        return cancelReason;
    }
}
