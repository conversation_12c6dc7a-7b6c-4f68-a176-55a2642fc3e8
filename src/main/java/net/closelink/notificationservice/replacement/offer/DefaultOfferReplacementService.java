package net.closelink.notificationservice.replacement.offer;

import com.google.common.base.Strings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import lombok.AllArgsConstructor;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementItem;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementMoney;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementOffer;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementSamplekit;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementSurcharge;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.AddressMessage;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.port.PortApiRestService;
import net.closelink.notificationservice.service.rest.port.domain.PortMessage;
import net.closelink.notificationservice.service.rest.product.ProductApiRestService;
import net.closelink.notificationservice.service.rest.product.domain.ProductMessage;
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierMessage;
import net.closelink.notificationservice.service.rest.user.UserApiRestService;
import net.closelink.notificationservice.service.rest.user.domain.UserMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import net.closelink.utils.Formatter;
import org.joda.money.BigMoney;
import org.joda.money.CurrencyUnit;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class DefaultOfferReplacementService implements OfferReplacementService {

    private static final Locale DEFAULT_LOCALE = Locale.ENGLISH;

    private static final String SURCHARGE_NAME = "name";
    private static final String SAMPLEKIT_NAME = "name";
    private static final String SAMPLEKIT_VALUE = "value";
    private static final String SAMPLEKIT_QUANTITY = "quantity";
    private static final String SURCHARGE_VALUE = "value";
    private static final String ITEM_PACK_TYPE = "packType";
    private static final String ITEM_UNIT_SIZE = "unitSize";
    private static final String ITEM_UNITS = "units";
    private static final String ITEM_UNIT = "unit";
    private static final String ITEM_VOLUME = "volume";
    private static final String ITEM_TOTAL = "total";
    private static final String ITEM_PRICE = "price";
    private static final String ITEM_NAME = "name";

    private static final String ORDER = "order_";
    private static final String PPL = ORDER + "ppl";
    private static final String TOTAL = ORDER + "total";
    private static final String VOLUME = ORDER + "volume";
    private static final String CUSTOMER_NAME = ORDER + "customer_name";
    private static final String CUSTOMER_ADDRESS = ORDER + "customer_address";
    private static final String SUPPLIER_NAME = ORDER + "supplier_name";
    private static final String PORT_LOCCODE = ORDER + "port_loccode";
    private static final String PORT_COUNTRY = ORDER + "port_country";
    private static final String PORT_COUNTRY_NAME = ORDER + "port_country_name";
    private static final String PORT_NAME = ORDER + "port_name";
    private static final String VESSEL_IMO = ORDER + "vessel_imo";
    private static final String VESSEL_NAME = ORDER + "vessel_name";
    private static final String STATE = ORDER + "state";
    private static final String CANCELREASON = ORDER + "cancelreason";
    private static final String DELIVERYDATE = ORDER + "deliverydate";
    private static final String DATESTARTED = ORDER + "datestarted";
    private static final String SUPPLY_MODE = ORDER + "supply_mode";
    private static final String NOTICE_DAYS = ORDER + "notice_days";
    private static final String NUMBER = ORDER + "number";
    private static final String BUYERREF = ORDER + "buyerref";
    private static final String VENDORREF = ORDER + "vendorref";
    private static final String ID = ORDER + "id";
    private static final String ORDER_ID = ORDER + "trade_id";
    private static final String AGENT = ORDER + "agent";
    private static final String STATE_TITLE = ORDER + "state_title";
    private static final String UPDATED_BY_USER_FIRST_NAME = ORDER + "updated_by_user_first_name";
    private static final String UPDATED_BY_USER_LAST_NAME = ORDER + "updated_by_user_last_name";

    private final PortApiRestService portApiRestService;
    private final VesselApiRestService vesselApiRestService;
    private final CustomerApiRestService customerApiRestService;
    private final SupplierApiRestService supplierApiRestService;
    private final ProductApiRestService productApiRestService;
    private final UserApiRestService userApiRestService;
    private final MessageSource messageSource;

    @Override
    public List<Map<String, String>> createItemsList(final List<ReplacementItem> items) {
        final List<Map<String, String>> formattedItems = new ArrayList<>();
        for (final ReplacementItem item : items) {
            final Map<String, String> formattedItem = createItem(item);
            formattedItems.add(formattedItem);
        }
        return formattedItems;
    }

    @Override
    public List<Map<String, String>> createSurchargeList(final List<ReplacementSurcharge> surcharges) {
        final List<Map<String, String>> formattedSurcharges = new ArrayList<>();
        for (final ReplacementSurcharge surcharge : surcharges) {
            final Map<String, String> formattedSurcharge = createSurcharge(surcharge);
            formattedSurcharges.add(formattedSurcharge);
        }
        return formattedSurcharges;
    }

    @Override
    public List<Map<String, String>> createSamplekitList(final List<ReplacementSamplekit> samplekits) {
        final List<Map<String, String>> formattedSamplekits = new ArrayList<>();
        for (final ReplacementSamplekit samplekit : samplekits) {
            final Map<String, String> formattedSamplekit = createSamplekit(samplekit);
            formattedSamplekits.add(formattedSamplekit);
        }
        return formattedSamplekits;
    }

    @Override
    public Map<String, String> create(final ReplacementOffer offer) {
        final Map<String, String> replacement = createOfferReplacements(offer);
        replacement.putAll(createCustomerReplacements(offer.getCustomerId()));
        if (offer.getSupplierId() != null) {
            replacement.putAll(createSupplierReplacements(offer.getSupplierId()));
        }
        replacement.putAll(createPortReplacements(offer.getPortId()));
        replacement.putAll(createVesselReplacements(offer.getVesselId()));

        String updatedByUserId = offer.getUpdatedBy();

        if (updatedByUserId != null) {
            replacement.putAll(createUpdatedByUserReplacements(updatedByUserId));
        }

        return replacement;
    }

    private Map<String, String> createUpdatedByUserReplacements(final String userId) {
        final Map<String, String> replacements = new HashMap<>();

        UserMessage user = userApiRestService.getUser(userId);

        replacements.put(UPDATED_BY_USER_FIRST_NAME, user.getFirstname());
        replacements.put(UPDATED_BY_USER_LAST_NAME, user.getLastname());

        return replacements;
    }

    private Map<String, String> createOfferReplacements(final ReplacementOffer offer) {
        final Map<String, String> replacement = new HashMap<>();
        replacement.put(ID, offer.getId());
        replacement.put(ORDER_ID, offer.getOrderId());
        replacement.put(VENDORREF, offer.getVendorReference());
        replacement.put(BUYERREF, offer.getBuyerReference());
        replacement.put(NUMBER, offer.getOfferNumber());
        replacement.put(DELIVERYDATE, offer.getDateDelivery().toString());
        replacement.put(DATESTARTED, offer.getDateCreated().toString());
        replacement.put(CANCELREASON, offer.getCancelReason());

        Long noticeDays = offer.getNoticeDays();
        if (noticeDays != null) {
            replacement.put(NOTICE_DAYS, noticeDays.toString());
        }

        if (offer.getSupplyMode() != null) {
            replacement.put(SUPPLY_MODE, translateSupplyMode(offer.getSupplyMode()));
        }

        replacement.put(AGENT, offer.getAgent());
        if (offer.getState() != null) {
            replacement.put(STATE, offer.getState());
            replacement.put(STATE_TITLE, createTitleString(offer.getState()));
        }
        if (offer.getPpl() != null) {
            replacement.put(PPL, formatPrice(offer.getPpl()));
        }
        if (offer.getVolume() != null) {
            replacement.put(VOLUME, offer.getVolume().toString());
        }
        if (offer.getTotal() != null) {
            replacement.put(TOTAL, formatPrice(offer.getTotal()));
        }
        return replacement;
    }

    private String translateSupplyMode(String supplyMode) {
        return this.messageSource.getMessage(supplyMode, null, Locale.ENGLISH);
    }

    private Map<String, String> createPortReplacements(final String portId) {
        final Map<String, String> replacement = new HashMap<>();
        final PortMessage port = this.portApiRestService.getPort(portId);
        replacement.put(PORT_NAME, port.getName());
        replacement.put(PORT_COUNTRY, port.getCountry().getCode());
        replacement.put(PORT_COUNTRY_NAME, port.getCountry().getName());
        replacement.put(PORT_LOCCODE, port.getLocCode());
        return replacement;
    }

    private Map<String, String> createSupplierReplacements(final String supplierId) {
        final Map<String, String> replacement = new HashMap<>();
        final SupplierMessage supplier = this.supplierApiRestService.getSupplier(supplierId);
        replacement.put(SUPPLIER_NAME, supplier.getName());
        return replacement;
    }

    private Map<String, String> createCustomerReplacements(final String customerId) {
        final Map<String, String> replacement = new HashMap<>();
        final CustomerMessage customer = this.customerApiRestService.getCustomer(customerId);
        replacement.put(CUSTOMER_NAME, customer.getName());

        AddressMessage customerAddress = customer.getAddress();
        if (isCompleteAddress(customerAddress)) {
            replacement.put(CUSTOMER_ADDRESS, createAddressStringFromAddress(customerAddress));
        }

        return replacement;
    }

    private boolean isCompleteAddress(AddressMessage address) {
        return (
            !Strings.isNullOrEmpty(address.getStreet()) &&
            !Strings.isNullOrEmpty(address.getStreetNumber()) &&
            !Strings.isNullOrEmpty(address.getZipcode()) &&
            !Strings.isNullOrEmpty(address.getCity()) &&
            !Strings.isNullOrEmpty(address.getCountry())
        );
    }

    private String createAddressStringFromAddress(AddressMessage address) {
        return String.format(
            "%s %s <br /> %s %s <br /> %s",
            address.getStreet(),
            address.getStreetNumber(),
            address.getZipcode(),
            address.getCity(),
            address.getCountry()
        );
    }

    private Map<String, String> createVesselReplacements(final String vesselId) {
        final Map<String, String> replacement = new HashMap<>();
        final VesselMessage vessel = this.vesselApiRestService.getVessel(vesselId);
        replacement.put(VESSEL_NAME, vessel.getName());
        replacement.put(VESSEL_IMO, vessel.getImo());
        return replacement;
    }

    private Map<String, String> createSurcharge(final ReplacementSurcharge surcharge) {
        final Map<String, String> formattedSurcharge = new HashMap<>();
        formattedSurcharge.put(SURCHARGE_NAME, surcharge.getName());
        formattedSurcharge.put(SURCHARGE_VALUE, formatPrice(surcharge.getValue()));
        return formattedSurcharge;
    }

    private Map<String, String> createSamplekit(final ReplacementSamplekit samplekit) {
        final Map<String, String> formattedSamplekit = new HashMap<>();
        formattedSamplekit.put(SAMPLEKIT_NAME, samplekit.getName());
        formattedSamplekit.put(SAMPLEKIT_QUANTITY, samplekit.getQuantity().toString());
        formattedSamplekit.put(SAMPLEKIT_VALUE, formatPrice(samplekit.getValue()));
        return formattedSamplekit;
    }

    private Map<String, String> createItem(final ReplacementItem item) {
        final Map<String, String> formattedItem = new HashMap<>();
        final ProductMessage product = this.productApiRestService.getProduct(item.getProductId());
        formattedItem.put(ITEM_NAME, product.getName());
        formattedItem.put(ITEM_PRICE, formatItemPrice(item.getPrice()));
        formattedItem.put(ITEM_TOTAL, formatPrice(item.getTotal()));
        formattedItem.put(ITEM_VOLUME, item.getVolume().toString());
        formattedItem.put(ITEM_UNIT, item.getUnit() == null ? "L" : item.getUnit());
        formattedItem.put(ITEM_UNITS, item.getUnits().toString());
        formattedItem.put(ITEM_UNIT_SIZE, item.getUnitSize().toString());
        formattedItem.put(ITEM_PACK_TYPE, createPackTypeString(item.getPackType()));
        return formattedItem;
    }

    private String createPackTypeString(final String packType) {
        return this.messageSource.getMessage(packType, null, DEFAULT_LOCALE);
    }

    private String createTitleString(final String state) {
        return this.messageSource.getMessage(getTitleKey(state), null, DEFAULT_LOCALE);
    }

    private String formatPrice(final ReplacementMoney value) {
        return Formatter.formatPrice(createMoney(value));
    }

    private String formatItemPrice(final ReplacementMoney value) {
        return Formatter.formatPrice(createMoney(value), 4);
    }

    private static BigMoney createMoney(ReplacementMoney money) {
        if (money == null) return null;
        return BigMoney.of(CurrencyUnit.of(money.getCurrency()), money.getValue());
    }

    private String getTitleKey(final String state) {
        return "state." + state + ".title";
    }
}
