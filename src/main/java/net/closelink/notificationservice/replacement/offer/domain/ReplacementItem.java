package net.closelink.notificationservice.replacement.offer.domain;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class ReplacementItem {

    private String productId;
    private ReplacementMoney price;
    private String packType;
    private BigDecimal unitSize;
    private BigDecimal units;
    private String unit;
    private BigDecimal volume;
    private ReplacementMoney total;
}
