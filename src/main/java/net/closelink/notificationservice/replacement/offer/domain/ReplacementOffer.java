package net.closelink.notificationservice.replacement.offer.domain;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.Data;

@Data
public class ReplacementOffer {

    private String id;
    private OffsetDateTime dateCreated;

    private String orderId;
    private String customerId;
    private String supplierId;
    private String state;
    private String enquiryType;

    private String vendorReference;
    private String offerNumber;
    private String cancelReason;
    private BigDecimal volume;
    private ReplacementMoney ppl;
    private ReplacementMoney total;
    private String updatedBy;
    private String supplyMode;
    private Long noticeDays;

    // order fields
    private String portId;
    private OffsetDateTime dateDelivery;
    private String agent;
    private String buyerReference;
    private String vesselId;
    private List<ReplacementItem> items;
    private List<ReplacementSurcharge> surcharges;
    private List<ReplacementSamplekit> samplekits;
}
