package net.closelink.notificationservice.replacement.price;

import java.util.HashMap;
import java.util.Map;

public class PriceListUploadReplacer {

    private static final String UPLOAD = "upload_";
    private static final String CUSTOMERID = UPLOAD + "customerid";
    private static final String SUPPLIERGROUPID = UPLOAD + "supplierGroupId";

    public static Map<String, String> createReplacements(final String customerId, final String supplierGroupId) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.put(CUSTOMERID, customerId);
        replacements.put(SUPPLIERGROUPID, supplierGroupId);
        return replacements;
    }
}
