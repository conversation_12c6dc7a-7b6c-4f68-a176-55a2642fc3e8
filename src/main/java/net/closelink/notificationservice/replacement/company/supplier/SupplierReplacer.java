package net.closelink.notificationservice.replacement.company.supplier;

import java.util.HashMap;
import java.util.Map;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierMessage;

public class SupplierReplacer {

    private static final String SUPPLIER_NAME = "supplier_name";

    public static Map<String, String> createSupplierReplacements(final SupplierMessage supplier) {
        final Map<String, String> replacements = new HashMap<>();
        replacements.put(SUPPLIER_NAME, supplier.getName());
        return replacements;
    }
}
