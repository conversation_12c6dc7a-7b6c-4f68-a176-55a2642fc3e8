package net.closelink.notificationservice.replacement.subject.customer;

import net.closelink.notificationservice.replacement.offer.domain.ReplacementOffer;
import net.closelink.notificationservice.service.rest.order.OrderApiRestService;
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DefaultCustomerSubjectReplacementService implements CustomerSubjectReplacementService {

    private final VesselApiRestService vesselApiRestService;
    private final OrderApiRestService orderService;
    private final SupplierApiRestService supplierApiRestService;

    @Autowired
    public DefaultCustomerSubjectReplacementService(
        final VesselApiRestService vesselApiRestService,
        final OrderApiRestService orderService,
        final SupplierApiRestService supplierApiRestService
    ) {
        super();
        this.vesselApiRestService = vesselApiRestService;
        this.orderService = orderService;
        this.supplierApiRestService = supplierApiRestService;
    }

    @Override
    public Object[] createSubjectReplacements(final ReplacementOffer offer) {
        return new Object[] {
            getVesselName(offer.getVesselId()),
            offer.getOfferNumber(),
            getSupplierName(offer),
            offer.getBuyerReference(),
        };
    }

    @Override
    public Object[] createSpotSubjectReplacements(final ReplacementOffer offer) {
        return new Object[] {
            getVesselName(offer.getVesselId()),
            getOrderNumber(offer.getOrderId()),
            getSupplierName(offer),
            offer.getBuyerReference(),
        };
    }

    private String getOrderNumber(final String orderId) {
        return this.orderService.getOrder(orderId).getOrderNumber();
    }

    private String getVesselName(final String vesselId) {
        return this.vesselApiRestService.getVessel(vesselId).getName();
    }

    private String getSupplierName(final ReplacementOffer offer) {
        return this.supplierApiRestService.getSupplier(offer.getSupplierId()).getName();
    }
}
