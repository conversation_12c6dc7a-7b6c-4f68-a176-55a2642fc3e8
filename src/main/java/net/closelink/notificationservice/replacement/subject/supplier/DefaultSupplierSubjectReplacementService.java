package net.closelink.notificationservice.replacement.subject.supplier;

import net.closelink.cenqueue.objects.Offer;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DefaultSupplierSubjectReplacementService implements SupplierSubjectReplacementService {

    private final VesselApiRestService vesselApiRestService;
    private final CustomerApiRestService customerApiRestService;

    @Autowired
    public DefaultSupplierSubjectReplacementService(
        final VesselApiRestService vesselApiRestService,
        final CustomerApiRestService customerApiRestService
    ) {
        super();
        this.vesselApiRestService = vesselApiRestService;
        this.customerApiRestService = customerApiRestService;
    }

    @Override
    public Object[] createSubjectReplacements(final Offer offer) {
        return new Object[] {
            getVesselName(offer.getVesselId()),
            offer.getOfferNumber(),
            getCustomerName(offer),
            offer.getBuyerReference(),
        };
    }

    private String getVesselName(final String vesselId) {
        return this.vesselApiRestService.getVessel(vesselId).getName();
    }

    private String getCustomerName(final Offer offer) {
        return this.customerApiRestService.getCustomer(offer.getCustomerId()).getName();
    }
}
