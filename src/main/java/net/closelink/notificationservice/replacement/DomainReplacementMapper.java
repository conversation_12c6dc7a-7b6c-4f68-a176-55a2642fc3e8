package net.closelink.notificationservice.replacement;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import net.closelink.cenqueue.objects.Item;
import net.closelink.cenqueue.objects.Samplekit;
import net.closelink.cenqueue.values.Money;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementItem;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementMoney;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementSamplekit;
import net.closelink.notificationservice.replacement.order.mapper.ReplacementOrderMapper;

public class DomainReplacementMapper {

    protected static ReplacementItem createReplacementItem(final Item item) {
        return Optional.ofNullable(item)
            .map(it -> {
                ReplacementItem replacementItem = new ReplacementItem();
                replacementItem.setProductId(it.getProductId());
                replacementItem.setPrice(createMoney(it.getPrice()));
                replacementItem.setPackType(it.getPackType());
                replacementItem.setUnitSize(it.getUnitSize());
                replacementItem.setUnits(it.getUnits());
                replacementItem.setUnit(it.getUnit());
                replacementItem.setVolume(it.getVolume());
                replacementItem.setTotal(createMoney(it.getTotal()));
                return replacementItem;
            })
            .orElse(null);
    }

    protected static ReplacementSamplekit createReplacementSamplekit(final Samplekit samplekit) {
        return Optional.ofNullable(samplekit)
            .map(it -> {
                ReplacementSamplekit replacementSamplekit = new ReplacementSamplekit();
                replacementSamplekit.setName(it.getName());
                replacementSamplekit.setQuantity(it.getQuantity());
                replacementSamplekit.setValue(createMoney(it.getValue()));
                return replacementSamplekit;
            })
            .orElse(null);
    }

    protected static List<ReplacementSamplekit> createReplacementSamplekits(final List<Samplekit> samplekits) {
        return Optional.ofNullable(samplekits)
            .map(it -> it.stream().map(ReplacementOrderMapper::createReplacementSamplekit).collect(Collectors.toList()))
            .orElse(null);
    }

    protected static List<ReplacementItem> createReplacementItems(final List<Item> items) {
        return Optional.ofNullable(items)
            .map(it -> it.stream().map(ReplacementOrderMapper::createReplacementItem).collect(Collectors.toList()))
            .orElse(null);
    }

    protected static ReplacementMoney createMoney(final Money money) {
        return Optional.ofNullable(money)
            .map(it -> ReplacementMoney.builder().currency(it.getCurrency()).value(it.getValue()).build())
            .orElse(null);
    }

    protected static String createEnumString(final Enum<?> type) {
        return Optional.ofNullable(type).map(Enum::toString).orElse(null);
    }
}
