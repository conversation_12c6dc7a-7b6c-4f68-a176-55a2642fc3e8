package net.closelink.notificationservice.replacement.order.domain;

import java.time.OffsetDateTime;
import java.util.List;
import lombok.Data;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementItem;
import net.closelink.notificationservice.replacement.offer.domain.ReplacementSamplekit;
import net.closelink.notificationservice.replacement.order.types.OrderState;

@Data
public class ReplacementOrder {

    private String id;
    private OffsetDateTime dateCreated;
    private OffsetDateTime dateUpdated;

    private String customerId;
    private String supplierId;
    private String vesselId;
    private String portId;
    private OrderState state;
    private String agent;
    private OffsetDateTime dateDelivery;
    private OffsetDateTime dateCanceled;
    private String buyerReference;
    private String orderNumber;
    private String cancelReason;
    private List<ReplacementItem> items;
    private List<ReplacementSamplekit> samplekits;
}
