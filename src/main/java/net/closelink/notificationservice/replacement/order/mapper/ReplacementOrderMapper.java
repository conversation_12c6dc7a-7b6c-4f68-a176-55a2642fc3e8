package net.closelink.notificationservice.replacement.order.mapper;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import net.closelink.cenqueue.objects.Order;
import net.closelink.mapper.EnumMapper;
import net.closelink.notificationservice.replacement.DomainReplacementMapper;
import net.closelink.notificationservice.replacement.order.domain.ReplacementOrder;
import net.closelink.notificationservice.replacement.order.types.OrderState;

public class ReplacementOrderMapper extends DomainReplacementMapper {

    public static ReplacementOrder replace(final Order order) {
        ReplacementOrder replacementOrder = new ReplacementOrder();

        replacementOrder.setId(order.getId());
        replacementOrder.setDateCreated(
            order.getDateCreated() != null ? OffsetDateTime.parse(order.getDateCreated()) : null
        );
        replacementOrder.setDateUpdated(
            order.getDateUpdated() != null ? OffsetDateTime.parse(order.getDateUpdated()) : null
        );

        replacementOrder.setCustomerId(order.getCustomerId());
        replacementOrder.setPortId(order.getPortId());
        replacementOrder.setVesselId(order.getVesselId());
        replacementOrder.setSupplierId(order.getSupplierId());
        replacementOrder.setState(EnumMapper.map(OrderState.class, order.getState()));
        replacementOrder.setAgent(order.getAgent());
        replacementOrder.setDateDelivery(
            order.getDateDelivery() != null
                ? Instant.ofEpochMilli(order.getDateDelivery()).atOffset(ZoneOffset.UTC)
                : null
        );
        replacementOrder.setDateCanceled(
            order.getDateCanceled() != null
                ? Instant.ofEpochMilli(order.getDateCanceled()).atOffset(ZoneOffset.UTC)
                : null
        );
        replacementOrder.setBuyerReference(order.getBuyerReference());
        replacementOrder.setOrderNumber(order.getOrderNumber());
        replacementOrder.setCancelReason(order.getCancelReason());
        replacementOrder.setItems(createReplacementItems(order.getItems()));
        replacementOrder.setSamplekits(createReplacementSamplekits(order.getSamplekits()));

        return replacementOrder;
    }
}
