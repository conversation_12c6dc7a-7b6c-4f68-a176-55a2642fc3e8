package net.closelink.notificationservice.replacement.order.mapper;

import static net.closelink.notificationservice.util.DateUtilKt.fromEpochMillisOrNull;
import static net.closelink.notificationservice.util.DateUtilKt.parseOffsetDateTimeOrNull;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import net.closelink.cenqueue.objects.Order;
import net.closelink.mapper.EnumMapper;
import net.closelink.notificationservice.replacement.DomainReplacementMapper;
import net.closelink.notificationservice.replacement.order.domain.ReplacementOrder;
import net.closelink.notificationservice.replacement.order.types.OrderState;

public class ReplacementOrderMapper extends DomainReplacementMapper {

    public static ReplacementOrder replace(final Order order) {
        ReplacementOrder replacementOrder = new ReplacementOrder();

        replacementOrder.setId(order.getId());
        replacementOrder.setDateCreated(parseOffsetDateTimeOrNull(order.getDateCreated()));
        replacementOrder.setDateUpdated(parseOffsetDateTimeOrNull(order.getDateUpdated()));

        replacementOrder.setCustomerId(order.getCustomerId());
        replacementOrder.setPortId(order.getPortId());
        replacementOrder.setVesselId(order.getVesselId());
        replacementOrder.setSupplierId(order.getSupplierId());
        replacementOrder.setState(EnumMapper.map(OrderState.class, order.getState()));
        replacementOrder.setAgent(order.getAgent());
        replacementOrder.setDateDelivery(fromEpochMillisOrNull(order.getDateDelivery()));
        replacementOrder.setDateCanceled(fromEpochMillisOrNull(order.getDateCanceled()));
        replacementOrder.setBuyerReference(order.getBuyerReference());
        replacementOrder.setOrderNumber(order.getOrderNumber());
        replacementOrder.setCancelReason(order.getCancelReason());
        replacementOrder.setItems(createReplacementItems(order.getItems()));
        replacementOrder.setSamplekits(createReplacementSamplekits(order.getSamplekits()));

        return replacementOrder;
    }
}
