package net.closelink.notificationservice.replacement.order;

import java.util.HashMap;
import java.util.Map;
import net.closelink.notificationservice.replacement.order.domain.ReplacementOrder;
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService;
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage;
import net.closelink.notificationservice.service.rest.port.PortApiRestService;
import net.closelink.notificationservice.service.rest.port.domain.PortMessage;
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService;
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierMessage;
import net.closelink.notificationservice.service.rest.vessel.VesselApiRestService;
import net.closelink.notificationservice.service.rest.vessel.domain.VesselMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DefaultOrderReplacementService implements OrderReplacementService {

    private static final String ORDER = "order_";
    private static final String CUSTOMER_NAME = ORDER + "customer_name";
    private static final String SUPPLIER_NAME = ORDER + "supplier_name";
    private static final String PORT_LOCCODE = ORDER + "port_loccode";
    private static final String PORT_COUNTRY = ORDER + "port_country";
    private static final String PORT_COUNTRY_NAME = ORDER + "port_country_name";
    private static final String PORT_NAME = ORDER + "port_name";
    private static final String VESSEL_IMO = ORDER + "vessel_imo";
    private static final String VESSEL_NAME = ORDER + "vessel_name";
    private static final String STATE = ORDER + "state";
    private static final String CANCELREASON = ORDER + "cancelreason";
    private static final String DELIVERYDATE = ORDER + "deliverydate";
    private static final String DATESTARTED = ORDER + "datestarted";
    private static final String NUMBER = ORDER + "number";
    private static final String BUYERREF = ORDER + "buyerref";
    private static final String ID = ORDER + "id";
    private static final String AGENT = ORDER + "agent";

    private final PortApiRestService portApiRestService;
    private final VesselApiRestService vesselApiRestService;
    private final CustomerApiRestService customerApiRestService;
    private final SupplierApiRestService supplierApiRestService;

    @Autowired
    public DefaultOrderReplacementService(
        final PortApiRestService portApiRestService,
        final VesselApiRestService vesselApiRestService,
        final CustomerApiRestService customerApiRestService,
        final SupplierApiRestService supplierApiRestService
    ) {
        super();
        this.portApiRestService = portApiRestService;
        this.vesselApiRestService = vesselApiRestService;
        this.customerApiRestService = customerApiRestService;
        this.supplierApiRestService = supplierApiRestService;
    }

    @Override
    public Map<String, String> create(final ReplacementOrder order) {
        final Map<String, String> replacement = createOrderReplacements(order);
        replacement.putAll(createCustomerReplacements(order.getCustomerId()));
        if (order.getSupplierId() != null) {
            replacement.putAll(createSupplierReplacements(order.getSupplierId()));
        }
        replacement.putAll(createPortReplacements(order.getPortId()));
        replacement.putAll(createVesselReplacements(order.getVesselId()));
        return replacement;
    }

    private Map<String, String> createOrderReplacements(final ReplacementOrder order) {
        final Map<String, String> replacement = new HashMap<>();
        replacement.put(ID, order.getId());
        replacement.put(BUYERREF, order.getBuyerReference());
        replacement.put(NUMBER, order.getOrderNumber());
        replacement.put(DELIVERYDATE, order.getDateDelivery().toString());
        replacement.put(DATESTARTED, order.getDateCreated().toString());
        replacement.put(CANCELREASON, order.getCancelReason());
        replacement.put(AGENT, order.getAgent());
        if (order.getState() != null) {
            replacement.put(STATE, order.getState().toString());
        }

        return replacement;
    }

    private Map<String, String> createPortReplacements(final String portId) {
        if (portId == null) {
            return new HashMap<>();
        }

        final Map<String, String> replacement = new HashMap<>();
        final PortMessage port = this.portApiRestService.getPort(portId);
        replacement.put(PORT_NAME, port.getName());
        replacement.put(PORT_COUNTRY, port.getCountry().getCode());
        replacement.put(PORT_COUNTRY_NAME, port.getCountry().getName());
        replacement.put(PORT_LOCCODE, port.getLocCode());
        return replacement;
    }

    private Map<String, String> createSupplierReplacements(final String supplierId) {
        final Map<String, String> replacement = new HashMap<>();
        final SupplierMessage supplier = this.supplierApiRestService.getSupplier(supplierId);
        replacement.put(SUPPLIER_NAME, supplier.getName());
        return replacement;
    }

    private Map<String, String> createCustomerReplacements(final String customerId) {
        final Map<String, String> replacement = new HashMap<>();
        final CustomerMessage customer = this.customerApiRestService.getCustomer(customerId);
        replacement.put(CUSTOMER_NAME, customer.getName());
        return replacement;
    }

    private Map<String, String> createVesselReplacements(final String vesselId) {
        final Map<String, String> replacement = new HashMap<>();
        final VesselMessage vessel = this.vesselApiRestService.getVessel(vesselId);
        replacement.put(VESSEL_NAME, vessel.getName());
        replacement.put(VESSEL_IMO, vessel.getImo());
        return replacement;
    }
}
