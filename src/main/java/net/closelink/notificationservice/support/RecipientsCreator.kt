package net.closelink.notificationservice.support

import net.closelink.notificationservice.domainobject.Recipient
import net.closelink.notificationservice.service.rest.vessel.domain.MailNotificationSettings.NotificationSettings

fun createRecipientListForVesselNotification(
    vesselEmail: String?,
    notificationSettings: NotificationSettings,
): List<Recipient> {
    val recipients = mutableListOf<Recipient>()

    if (!notificationSettings.isEnabled) {
        return listOf()
    }

    if (notificationSettings.isSendToVessel && vesselEmail != null && vesselEmail.isNotBlank()) {
        recipients.add(Recipient.builder().emailAddress(vesselEmail).build())
    }

    if (notificationSettings.isSendToOthers) {
        recipients.addAll(
            notificationSettings.recipients.map { Recipient.builder().emailAddress(it).build() }
        )
    }

    return recipients
}
