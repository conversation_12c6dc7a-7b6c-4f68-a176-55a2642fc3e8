package net.closelink.notificationservice.support;

import java.time.OffsetDateTime;
import net.closelink.cenqueue.coder.GsonCoder;
import net.closelink.cenqueue.objects.Item;
import net.closelink.cenqueue.objects.Offer;
import net.closelink.cenqueue.objects.Samplekit;
import net.closelink.cenqueue.objects.Surcharge;
import net.closelink.cenqueue.types.OfferState;
import org.joda.time.DateTime;

public class EncodeSupport {

    public static Item encodeItem(final String value) {
        return GsonCoder.encode(value, Item.class);
    }

    public static Samplekit encodeSamplekit(final String value) {
        return GsonCoder.encode(value, Samplekit.class);
    }

    public static Surcharge encodeSurcharge(final String value) {
        return GsonCoder.encode(value, Surcharge.class);
    }

    public static Offer encodeOffer(String value) {
        return GsonCoder.encode(value, Offer.class);
    }

    public static DateTime encodeDateTime(final String value) {
        return GsonCoder.encode(value, DateTime.class);
    }

    public static OffsetDateTime encodeOffsetDateTime(final String value) {
        return GsonCoder.encode(value, OffsetDateTime.class);
    }

    public static Long encodeLong(final String value) {
        return GsonCoder.encode(value, Long.class);
    }

    public static OfferState encodeOfferState(final String value) {
        return GsonCoder.encode(value, OfferState.class);
    }

    public static String encodeString(final String value) {
        return GsonCoder.encode(value, String.class);
    }
}
