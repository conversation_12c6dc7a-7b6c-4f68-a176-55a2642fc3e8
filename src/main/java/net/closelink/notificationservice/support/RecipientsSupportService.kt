package net.closelink.notificationservice.support

import net.closelink.cenqueue.objects.Offer
import net.closelink.cenqueue.types.EnquiryType
import net.closelink.notificationservice.domainobject.ReceiverType
import net.closelink.notificationservice.domainobject.Recipient
import net.closelink.notificationservice.service.rest.customer.CustomerApiRestService
import net.closelink.notificationservice.service.rest.customer.domain.CustomerMessage
import net.closelink.notificationservice.service.rest.supplier.SupplierApiRestService
import net.closelink.notificationservice.service.rest.supplier.domain.SupplierMessage
import org.springframework.stereotype.Service

data class RecipientInfo(val receiverType: ReceiverType, val recipients: List<Recipient>? = null)

@Service
class RecipientsSupportService(
    private val supplierApiRestService: SupplierApiRestService,
    private val customerApiRestService: CustomerApiRestService,
) {
    fun buildSupplierRecipientInfo(offer: Offer): RecipientInfo {
        val isForwarded = EnquiryType.FORWARDED == offer.enquiryType
        val customer = customerApiRestService.getCustomer(offer.customerId)
        return if (isForwarded && customer.type == "LUBES") {
            // The logic for resolving recipients and their email addresses is currently split
            // between
            // this service and the MessageService. Since the case of: forwarded (to anti-suppliers)
            // mails from Lubes
            // Customers is a special case and not handled in the MessageService we need to add the
            // recipients manually
            val supplier = supplierApiRestService.getSupplier(offer.supplierId)
            RecipientInfo(
                receiverType = ReceiverType.CUSTOM,
                recipients = buildRecipientsForForwardedSupplier(supplier, customer),
            )
        } else {
            // Other cases are still handled in the MessageService
            RecipientInfo(receiverType = ReceiverType.SUPPLIER)
        }
    }

    private fun buildRecipientsForForwardedSupplier(
        supplier: SupplierMessage,
        customer: CustomerMessage,
    ): List<Recipient> {
        val supplierRecipient =
            Recipient.builder()
                .type(Recipient.Type.TO)
                .name(supplier.name)
                .emailAddress(supplier.email)
                .build()
        val customerRecipient =
            Recipient.builder()
                .type(Recipient.Type.CC)
                .name(customer.name)
                .emailAddress(customer.email)
                .build()
        return listOf(supplierRecipient, customerRecipient)
    }
}
