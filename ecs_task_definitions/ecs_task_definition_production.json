{"family": "notificationservice-production", "containerDefinitions": [{"logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "production_notificationservice", "awslogs-region": "eu-central-1"}}, "portMappings": [{"hostPort": 0, "protocol": "tcp", "containerPort": 8443}], "cpu": 50, "environment": [{"name": "ENV", "value": "production"}, {"name": "JAVA_TOOL_OPTIONS", "value": "-Xms256M -Xmx512M"}], "memory": 1024, "memoryReservation": 768, "image": "${DOCKER_IMAGE_TAG}", "name": "notificationservice", "essential": true, "dockerLabels": {"PROMETHEUS_EXPORTER_PORT": "8443", "PROMETHEUS_EXPORTER_PATH": "/actuator/prometheus", "PROMETHEUS_EXPORTER_SCHEME": "https"}}], "volumes": [{"name": "volume"}]}