# NotificationService

---

The NotificationService registers on the Closelink Event Notification Queue (CENQ) to generate and send messages.

## Resources

-   https://docs.spring.io/spring-boot/docs/current-SNAPSHOT/reference/htmlsingle
-   https://docs.spring.io/spring-boot/docs/current/reference/html/index.html

-   https://springframework.guru/running-spring-boot-in-a-docker-container/
-   https://spring.io/guides/gs/spring-boot-docker/
-   https://blog.docker.com/2017/05/spring-boot-development-docker/

-   http://javasplitter.blogspot.de/2011/08/sharing-configuration-files-from-maven.html
